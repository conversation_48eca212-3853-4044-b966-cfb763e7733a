import type { Config } from "tailwindcss"

const config: Config = {
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        // VentureDirection Brand Colors using CSS variables
        venture: {
          50: 'rgb(var(--venture-50) / <alpha-value>)',
          100: 'rgb(var(--venture-100) / <alpha-value>)',
          200: 'rgb(var(--venture-200) / <alpha-value>)',
          300: 'rgb(var(--venture-300) / <alpha-value>)',
          400: 'rgb(var(--venture-400) / <alpha-value>)',
          500: 'rgb(var(--venture-500) / <alpha-value>)',
          600: 'rgb(var(--venture-600) / <alpha-value>)',
          700: 'rgb(var(--venture-700) / <alpha-value>)',
          800: 'rgb(var(--venture-800) / <alpha-value>)',
          900: 'rgb(var(--venture-900) / <alpha-value>)',
        },

        // Semantic colors using CSS variables
        success: {
          50: 'rgb(var(--success-50) / <alpha-value>)',
          100: 'rgb(var(--success-100) / <alpha-value>)',
          200: 'rgb(var(--success-200) / <alpha-value>)',
          300: 'rgb(var(--success-300) / <alpha-value>)',
          400: 'rgb(var(--success-400) / <alpha-value>)',
          500: 'rgb(var(--success-500) / <alpha-value>)',
          600: 'rgb(var(--success-600) / <alpha-value>)',
          700: 'rgb(var(--success-700) / <alpha-value>)',
          800: 'rgb(var(--success-800) / <alpha-value>)',
          900: 'rgb(var(--success-900) / <alpha-value>)',
        },

        warning: {
          50: 'rgb(var(--warning-50) / <alpha-value>)',
          100: 'rgb(var(--warning-100) / <alpha-value>)',
          200: 'rgb(var(--warning-200) / <alpha-value>)',
          300: 'rgb(var(--warning-300) / <alpha-value>)',
          400: 'rgb(var(--warning-400) / <alpha-value>)',
          500: 'rgb(var(--warning-500) / <alpha-value>)',
          600: 'rgb(var(--warning-600) / <alpha-value>)',
          700: 'rgb(var(--warning-700) / <alpha-value>)',
          800: 'rgb(var(--warning-800) / <alpha-value>)',
          900: 'rgb(var(--warning-900) / <alpha-value>)',
        },

        error: {
          50: 'rgb(var(--error-50) / <alpha-value>)',
          100: 'rgb(var(--error-100) / <alpha-value>)',
          200: 'rgb(var(--error-200) / <alpha-value>)',
          300: 'rgb(var(--error-300) / <alpha-value>)',
          400: 'rgb(var(--error-400) / <alpha-value>)',
          500: 'rgb(var(--error-500) / <alpha-value>)',
          600: 'rgb(var(--error-600) / <alpha-value>)',
          700: 'rgb(var(--error-700) / <alpha-value>)',
          800: 'rgb(var(--error-800) / <alpha-value>)',
          900: 'rgb(var(--error-900) / <alpha-value>)',
        },

        // Map primary to venture colors
        primary: {
          DEFAULT: 'rgb(var(--venture-500) / <alpha-value>)',
          foreground: '#ffffff',
        },

        // Base colors
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',

        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
}

export default config
