@tailwind base;
@tailwind components;
@tailwind utilities;

/* VentureDirection Design System - Blue & White */
:root {
  /* VentureDirection Brand Colors */
  --venture-50: 239 246 255;
  --venture-100: 219 234 254;
  --venture-200: 191 219 254;
  --venture-300: 147 197 253;
  --venture-400: 96 165 250;
  --venture-500: 59 130 246;
  --venture-600: 37 99 235;
  --venture-700: 29 78 216;
  --venture-800: 30 64 175;
  --venture-900: 30 58 138;

  /* Success Colors */
  --success-50: 240 253 244;
  --success-100: 220 252 231;
  --success-200: 187 247 208;
  --success-300: 134 239 172;
  --success-400: 74 222 128;
  --success-500: 34 197 94;
  --success-600: 22 163 74;
  --success-700: 21 128 61;
  --success-800: 22 101 52;
  --success-900: 20 83 45;

  /* Warning Colors */
  --warning-50: 255 251 235;
  --warning-100: 254 243 199;
  --warning-200: 253 230 138;
  --warning-300: 252 211 77;
  --warning-400: 251 191 36;
  --warning-500: 245 158 11;
  --warning-600: 217 119 6;
  --warning-700: 180 83 9;
  --warning-800: 146 64 14;
  --warning-900: 120 53 15;

  /* Error Colors */
  --error-50: 254 242 242;
  --error-100: 254 226 226;
  --error-200: 252 202 202;
  --error-300: 252 165 165;
  --error-400: 248 113 113;
  --error-500: 239 68 68;
  --error-600: 220 38 38;
  --error-700: 185 28 28;
  --error-800: 153 27 27;
  --error-900: 127 29 29;

  /* Base Colors - VentureDirection Blue & White Theme */
  --background: 0 0% 100%;                    /* Pure white */
  --foreground: 222.2 84% 4.9%;              /* Dark text */
  --card: 0 0% 100%;                         /* White cards */
  --card-foreground: 222.2 84% 4.9%;        /* Dark text on cards */
  --popover: 0 0% 100%;                      /* White popovers */
  --popover-foreground: 222.2 84% 4.9%;     /* Dark text in popovers */
  --primary: 214 95% 57%;                    /* VentureDirection blue */
  --primary-foreground: 0 0% 100%;          /* White text on blue */
  --secondary: 220 14% 96%;                  /* Light gray */
  --secondary-foreground: 222.2 84% 4.9%;   /* Dark text on light gray */
  --muted: 220 14% 97%;                      /* Very light gray */
  --muted-foreground: 215.4 16.3% 46.9%;    /* Muted text */
  --accent: 214 100% 97%;                    /* Light blue accent */
  --accent-foreground: 214 95% 36%;         /* Dark blue text */
  --destructive: 0 84.2% 60.2%;             /* Red for errors */
  --destructive-foreground: 0 0% 100%;      /* White text on red */
  --border: 220 13% 91%;                     /* Light gray borders */
  --input: 220 13% 91%;                      /* Light gray input borders */
  --ring: 214 95% 57%;                       /* Blue focus ring */
  --radius: 0.5rem;                          /* Border radius */

  /* Semantic Colors */
  --success: 142 76% 36%;                    /* Green */
  --success-foreground: 0 0% 100%;          /* White text on green */
  --warning: 38 92% 50%;                     /* Amber */
  --warning-foreground: 0 0% 100%;          /* White text on amber */
  --error: 0 84% 60%;                        /* Red */
  --error-foreground: 0 0% 100%;            /* White text on red */
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
}

/* Base styles */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Test styles to ensure CSS is loading */
.test-red {
  background-color: red !important;
  color: white !important;
  padding: 1rem !important;
}
