import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { prisma } from '@/lib/db/prisma'
import { getVentureUser } from '@/lib/services/venture-user-service'

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get current business context
    const user = await getVentureUser({ includeBusinessContext: true })
    const businessId = user?.currentBusinessId

    if (!businessId) {
      return NextResponse.json({ error: 'No active business' }, { status: 400 })
    }

    // Get query parameters
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const search = url.searchParams.get('search') || ''
    const status = url.searchParams.get('status') || ''

    // Build where clause
    const where: any = {
      businessId,
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { sku: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      }),
      ...(status && { status })
    }

    // Get products with pagination
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          variants: true,
          _count: {
            select: {
              orderItems: true,
              inventoryMovements: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.product.count({ where })
    ])

    return NextResponse.json({
      products,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching products:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get current business context
    const user = await getVentureUser({ includeBusinessContext: true })
    const businessId = user?.currentBusinessId

    if (!businessId) {
      return NextResponse.json({ error: 'No active business' }, { status: 400 })
    }

    const body = await request.json()
    const {
      name,
      description,
      sku,
      price,
      costPrice,
      compareAtPrice,
      category,
      brand,
      inventoryQuantity,
      lowStockThreshold,
      trackInventory,
      allowBackorder,
      weight,
      weightUnit,
      dimensions,
      tags,
      images,
      variants
    } = body

    // Create product with variants
    const product = await prisma.product.create({
      data: {
        businessId,
        name,
        description,
        sku,
        price: parseFloat(price),
        costPrice: costPrice ? parseFloat(costPrice) : null,
        compareAtPrice: compareAtPrice ? parseFloat(compareAtPrice) : null,
        category,
        brand,
        inventoryQuantity: parseInt(inventoryQuantity) || 0,
        lowStockThreshold: lowStockThreshold ? parseInt(lowStockThreshold) : null,
        trackInventory: trackInventory !== false,
        allowBackorder: allowBackorder === true,
        weight: weight ? parseFloat(weight) : null,
        weightUnit,
        dimensions,
        tags: tags || [],
        images: images || [],
        status: 'DRAFT',
        variants: variants?.length ? {
          create: variants.map((variant: any) => ({
            businessId,
            name: variant.name,
            sku: variant.sku,
            price: variant.price ? parseFloat(variant.price) : null,
            costPrice: variant.costPrice ? parseFloat(variant.costPrice) : null,
            inventoryQuantity: parseInt(variant.inventoryQuantity) || 0,
            options: variant.options || {},
            weight: variant.weight ? parseFloat(variant.weight) : null,
            dimensions: variant.dimensions,
            image: variant.image
          }))
        } : undefined
      },
      include: {
        variants: true
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Product created successfully',
      product
    })

  } catch (error) {
    console.error('Error creating product:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
