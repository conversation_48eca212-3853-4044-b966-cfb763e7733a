import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { onboardingService } from '@/lib/services/onboarding-service'
import { sessionContextService } from '@/lib/services/session-context-service'
import type { OnboardingProgressUpdate } from '@/lib/types/onboarding'

export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get business ID from session or query params
    const url = new URL(request.url)
    const businessId = url.searchParams.get('businessId') || 
                      sessionClaims?.metadata?.activeBusinessId

    if (!businessId) {
      return NextResponse.json(
        { error: 'Business ID required' },
        { status: 400 }
      )
    }

    // Verify user has access to this business
    await sessionContextService.requireBusinessAccess(userId, businessId)

    // Auto-detect progress based on actual data
    await onboardingService.autoDetectProgress(businessId)

    // Get current onboarding status
    const status = await onboardingService.getBusinessOnboardingStatus(businessId)

    return NextResponse.json(status)

  } catch (error) {
    console.error('Error getting onboarding progress:', error)
    
    if (error instanceof Error && error.message === 'Access denied to this business') {
      return NextResponse.json(
        { error: 'Access denied to this business' },
        { status: 403 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body: OnboardingProgressUpdate = await request.json()
    
    // Use business ID from request body or session
    const businessId = body.businessId || sessionClaims?.metadata?.activeBusinessId

    if (!businessId) {
      return NextResponse.json(
        { error: 'Business ID required' },
        { status: 400 }
      )
    }

    // Verify user has access to this business
    await sessionContextService.requireBusinessAccess(userId, businessId)

    // Update onboarding progress
    await onboardingService.updateOnboardingProgress({
      ...body,
      businessId,
    })

    // Get updated status
    const status = await onboardingService.getBusinessOnboardingStatus(businessId)

    return NextResponse.json({
      success: true,
      message: 'Onboarding progress updated',
      status,
    })

  } catch (error) {
    console.error('Error updating onboarding progress:', error)
    
    if (error instanceof Error && error.message === 'Access denied to this business') {
      return NextResponse.json(
        { error: 'Access denied to this business' },
        { status: 403 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { businessId: requestBusinessId } = await request.json()
    
    // Use business ID from request or session
    const businessId = requestBusinessId || sessionClaims?.metadata?.activeBusinessId

    if (!businessId) {
      return NextResponse.json(
        { error: 'Business ID required' },
        { status: 400 }
      )
    }

    // Verify user has access to this business
    await sessionContextService.requireBusinessAccess(userId, businessId)

    // Initialize onboarding for this business
    await onboardingService.initializeBusinessOnboarding(businessId)

    // Get initial status
    const status = await onboardingService.getBusinessOnboardingStatus(businessId)

    return NextResponse.json({
      success: true,
      message: 'Onboarding initialized',
      status,
    })

  } catch (error) {
    console.error('Error initializing onboarding:', error)
    
    if (error instanceof Error && error.message === 'Access denied to this business') {
      return NextResponse.json(
        { error: 'Access denied to this business' },
        { status: 403 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
