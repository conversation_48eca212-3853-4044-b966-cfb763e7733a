import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { sessionContextService } from '@/lib/services/session-context-service'

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Initialize user's business context
    await sessionContextService.initializeUserContext(userId)

    // Get updated context
    const context = await sessionContextService.getUserSessionContext(userId)

    return NextResponse.json({ 
      success: true,
      message: 'Context initialized successfully',
      context,
    })

  } catch (error) {
    console.error('Error initializing context:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
