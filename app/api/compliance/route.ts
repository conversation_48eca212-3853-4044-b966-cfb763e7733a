import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { complianceService } from '@/lib/services/compliance-service'

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const businessId = searchParams.get('businessId')
    const status = searchParams.get('status')

    const compliance = await complianceService.getCompliance({
      userId,
      businessId: businessId || undefined,
      status: status || undefined as any,
    })

    return NextResponse.json(compliance)
  } catch (error) {
    console.error('Error fetching compliance:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
