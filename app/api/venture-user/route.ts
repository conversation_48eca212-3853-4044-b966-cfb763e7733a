import { NextRequest, NextResponse } from 'next/server'
import { auth, currentUser } from '@clerk/nextjs/server'
import { ventureUserService } from '@/lib/services/venture-user-service'
import type { ResolveUserOptions } from '@/lib/types/venture-user'

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse query parameters for options
    const url = new URL(request.url)
    const includeRoles = url.searchParams.get('includeRoles') === 'true'
    const includePermissions = url.searchParams.get('includePermissions') === 'true'
    const includeBusinessContext = url.searchParams.get('includeBusinessContext') === 'true'
    const businessId = url.searchParams.get('businessId') || undefined
    const businessGroupId = url.searchParams.get('businessGroupId') || undefined

    const options: ResolveUserOptions = {
      includeRoles: includeRoles || includePermissions, // Permissions require roles
      includePermissions,
      includeBusinessContext,
      businessId,
      businessGroupId
    }

    // Get VentureUser with specified options
    const ventureUser = await ventureUserService.getCurrentVentureUser(options)

    if (!ventureUser) {
      return NextResponse.json(
        { error: 'VentureUser not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(ventureUser)

  } catch (error) {
    console.error('Error getting VentureUser:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get Clerk user data
    const clerkUser = await currentUser()
    
    if (!clerkUser) {
      return NextResponse.json(
        { error: 'Clerk user not found' },
        { status: 404 }
      )
    }

    // Create or update VentureUser
    const ventureUser = await ventureUserService.upsertVentureUser({
      clerkUserId: clerkUser.id,
      email: clerkUser.emailAddresses[0]?.emailAddress || '',
      firstName: clerkUser.firstName || undefined,
      lastName: clerkUser.lastName || undefined,
      avatar: clerkUser.imageUrl || undefined,
      emailVerified: clerkUser.emailAddresses[0]?.verification?.status === 'verified'
    })

    return NextResponse.json({
      success: true,
      message: 'VentureUser created/updated successfully',
      user: ventureUser
    })

  } catch (error) {
    console.error('Error creating/updating VentureUser:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { permission, businessId, role } = await request.json()

    let hasAccess = false

    if (permission) {
      hasAccess = await ventureUserService.hasPermission(userId, permission, businessId)
    } else if (role) {
      hasAccess = await ventureUserService.hasRole(userId, role, businessId)
    }

    return NextResponse.json({
      hasAccess,
      permission,
      role,
      businessId
    })

  } catch (error) {
    console.error('Error checking user access:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
