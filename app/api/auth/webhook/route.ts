import { NextRequest, NextResponse } from 'next/server';
import { Webhook } from 'svix';
import { headers } from 'next/headers';
import { userService } from '@/lib/services/user-service';

const webhookSecret = process.env.CLERK_WEBHOOK_SECRET! as string;

if (!webhookSecret) {
  throw new Error('Please add CLERK_WEBHOOK_SECRET to your environment variables');
}

export async function POST(req: NextRequest) {
  // Get the headers
  const headerPayload = await headers();
  const svix_id = headerPayload.get('svix-id');
  const svix_timestamp = headerPayload.get('svix-timestamp');
  const svix_signature = headerPayload.get('svix-signature');

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return NextResponse.json(
      { error: 'Missing svix headers' },
      { status: 400 }
    );
  }

  // Get the body
  const payload = await req.text();

  // Create a new Svix instance with  secret
  const wh = new Webhook(webhookSecret);

  let evt: any;

  // Verify the payload with the headers
  try {
    evt = wh.verify(payload, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    });
  } catch (err) {
    console.error('Error verifying webhook:', err);
    return NextResponse.json(
      { error: 'Invalid webhook signature' },
      { status: 400 }
    );
  }

  // Handle the webhook
  const eventType = evt.type;

  try {
    switch (eventType) {
      case 'user.created':
        await handleUserCreated(evt.data);
        break;
      case 'user.updated':
        await handleUserUpdated(evt.data);
        break;
      case 'user.deleted':
        await handleUserDeleted(evt.data);
        break;
      default:
        console.log(`Unhandled webhook event type: ${eventType}`);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error handling webhook:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function handleUserCreated(userData: any) {
  console.log('User created:', userData.id);

  try {
    const primaryEmail = userData.email_addresses?.find(
      (email: any) => email.id === userData.primary_email_address_id
    );

    await userService.createUser(userData.id, {
      firstName: userData.first_name || '',
      lastName: userData.last_name || '',
      phone: userData.phone_numbers?.[0]?.phone_number || '',
      language: 'en',
      timezone: 'UTC',
      emailAddress: primaryEmail?.email_address,
      emailVerified: primaryEmail?.verification?.status === 'verified',
    });

    console.log('User created in database:', userData.id);
  } catch (error) {
    console.error('Error creating user in database:', error);
    throw error;
  }
}

async function handleUserUpdated(userData: any) {
  console.log('User updated:', userData.id);

  try {
    await userService.updateUser(userData.id, {
      firstName: userData.first_name || '',
      lastName: userData.last_name || '',
      phone: userData.phone_numbers?.[0]?.phone_number || '',
    });

    console.log('User updated in database:', userData.id);
  } catch (error) {
    console.error('Error updating user in database:', error);
    // Don't throw here as user might not exist in our database yet
  }
}

async function handleUserDeleted(userData: any) {
  console.log('User deleted:', userData.id);

  try {
    await userService.deleteUser(userData.id);
    console.log('User deleted from database:', userData.id);
  } catch (error) {
    console.error('Error deleting user from database:', error);
    // Don't throw here as user might not exist in our database
  }
}
