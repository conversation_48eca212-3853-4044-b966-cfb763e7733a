import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { sessionContextService } from '@/lib/services/session-context-service'
import type { SwitchBusinessGroupRequest } from '@/lib/types/session'

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body: SwitchBusinessGroupRequest = await request.json()
    
    if (!body.businessGroupId) {
      return NextResponse.json(
        { error: 'Business Group ID is required' },
        { status: 400 }
      )
    }

    // Switch business group context
    await sessionContextService.switchBusinessGroup(userId, body)

    return NextResponse.json({ 
      success: true,
      message: 'Business group context switched successfully',
      businessGroupId: body.businessGroupId,
      defaultBusinessId: body.defaultBusinessId,
    })

  } catch (error) {
    console.error('Error switching business group:', error)
    
    if (error instanceof Error && error.message === 'Access denied to this business group') {
      return NextResponse.json(
        { error: 'Access denied to this business group' },
        { status: 403 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
