import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { sessionContextService } from '@/lib/services/session-context-service'

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get user's default business (last accessed or first)
    const defaultBusinessId = await sessionContextService.getDefaultBusiness(userId)
    
    if (!defaultBusinessId) {
      return NextResponse.json({
        defaultBusinessId: null,
        message: 'No businesses found for user'
      })
    }

    return NextResponse.json({
      defaultBusinessId,
      message: 'Default business retrieved successfully'
    })

  } catch (error) {
    console.error('Error getting default business:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Initialize user context (will set default business if none active)
    await sessionContextService.initializeUserContext(userId)

    // Get updated context
    const context = await sessionContextService.getUserSessionContext(userId)

    return NextResponse.json({
      success: true,
      message: 'User context initialized',
      activeBusinessId: context.activeBusinessId,
      context
    })

  } catch (error) {
    console.error('Error initializing user context:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
