import { NextRequest } from 'next/server'
import { GET, POST } from '../businesses/route'

// Mock the auth function
jest.mock('@clerk/nextjs/server', () => ({
  auth: jest.fn(),
}))

// Mock the business service
jest.mock('@/lib/services/business-service', () => ({
  businessService: {
    getUserBusinesses: jest.fn(),
    createBusiness: jest.fn(),
  },
}))

// Mock the validation schema
jest.mock('@/lib/validations/business', () => ({
  createBusinessSchema: {
    parse: jest.fn(),
  },
}))

import { auth } from '@clerk/nextjs/server'
import { businessService } from '@/lib/services/business-service'
import { createBusinessSchema } from '@/lib/validations/business'

const mockAuth = auth as jest.MockedFunction<typeof auth>
const mockBusinessService = businessService as jest.Mocked<typeof businessService>
const mockCreateBusinessSchema = createBusinessSchema as jest.Mocked<typeof createBusinessSchema>

describe('/api/businesses', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/businesses', () => {
    it('returns businesses for authenticated user', async () => {
      const mockBusinesses = [
        { id: '1', name: 'Test Business 1' },
        { id: '2', name: 'Test Business 2' },
      ]

      mockAuth.mockResolvedValue({ userId: 'test-user-id' })
      mockBusinessService.getUserBusinesses.mockResolvedValue(mockBusinesses)

      const request = new NextRequest('http://localhost:3000/api/businesses')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toEqual(mockBusinesses)
      expect(mockBusinessService.getUserBusinesses).toHaveBeenCalledWith('test-user-id')
    })

    it('returns 401 for unauthenticated user', async () => {
      mockAuth.mockResolvedValue({ userId: null })

      const request = new NextRequest('http://localhost:3000/api/businesses')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })

    it('handles service errors', async () => {
      mockAuth.mockResolvedValue({ userId: 'test-user-id' })
      mockBusinessService.getUserBusinesses.mockRejectedValue(new Error('Service error'))

      const request = new NextRequest('http://localhost:3000/api/businesses')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Internal server error')
    })
  })

  describe('POST /api/businesses', () => {
    it('creates business for authenticated user', async () => {
      const mockBusinessData = {
        name: 'New Business',
        description: 'Test description',
      }
      const mockCreatedBusiness = {
        id: '1',
        ...mockBusinessData,
      }

      mockAuth.mockResolvedValue({ userId: 'test-user-id' })
      mockCreateBusinessSchema.parse.mockReturnValue(mockBusinessData)
      mockBusinessService.createBusiness.mockResolvedValue(mockCreatedBusiness)

      const request = new NextRequest('http://localhost:3000/api/businesses', {
        method: 'POST',
        body: JSON.stringify(mockBusinessData),
      })
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data).toEqual(mockCreatedBusiness)
      expect(mockBusinessService.createBusiness).toHaveBeenCalledWith('test-user-id', mockBusinessData)
    })

    it('returns 401 for unauthenticated user', async () => {
      mockAuth.mockResolvedValue({ userId: null })

      const request = new NextRequest('http://localhost:3000/api/businesses', {
        method: 'POST',
        body: JSON.stringify({ name: 'Test' }),
      })
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })

    it('handles validation errors', async () => {
      mockAuth.mockResolvedValue({ userId: 'test-user-id' })
      mockCreateBusinessSchema.parse.mockImplementation(() => {
        throw new Error('Validation error')
      })

      const request = new NextRequest('http://localhost:3000/api/businesses', {
        method: 'POST',
        body: JSON.stringify({ invalid: 'data' }),
      })
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Internal server error')
    })

    it('handles service errors during creation', async () => {
      const mockBusinessData = { name: 'Test Business' }

      mockAuth.mockResolvedValue({ userId: 'test-user-id' })
      mockCreateBusinessSchema.parse.mockReturnValue(mockBusinessData)
      mockBusinessService.createBusiness.mockRejectedValue(new Error('Creation failed'))

      const request = new NextRequest('http://localhost:3000/api/businesses', {
        method: 'POST',
        body: JSON.stringify(mockBusinessData),
      })
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Internal server error')
    })
  })
})
