import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { sessionContextService } from '@/lib/services/session-context-service'
import type { SwitchBusinessRequest } from '@/lib/types/session'

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body: SwitchBusinessRequest = await request.json()
    
    if (!body.businessId) {
      return NextResponse.json(
        { error: 'Business ID is required' },
        { status: 400 }
      )
    }

    // Switch business context
    await sessionContextService.switchBusiness(userId, body)

    return NextResponse.json({ 
      success: true,
      message: 'Business context switched successfully',
      businessId: body.businessId,
      businessGroupId: body.businessGroupId,
    })

  } catch (error) {
    console.error('Error switching business:', error)
    
    if (error instanceof Error && error.message === 'Access denied to this business') {
      return NextResponse.json(
        { error: 'Access denied to this business' },
        { status: 403 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get user's session context
    const context = await sessionContextService.getUserSessionContext(userId)

    return NextResponse.json(context)

  } catch (error) {
    console.error('Error getting session context:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
