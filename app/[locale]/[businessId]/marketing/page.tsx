'use client'

import { MarketingOverview } from '@/components/marketing/marketingOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Mail, Target, BarChart } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'

export default function MarketingPage() {
  const t = useTranslations('business.marketing')
  const params = useParams()
  const businessId = params.businessId as string
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <BarChart className="h-4 w-4 mr-2" />
              {t('analytics')}
            </Button>
            <Button variant="outline">
              <Target className="h-4 w-4 mr-2" />
              {t('audience')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('createCampaign')}
            </Button>
          </div>
        }
      />
      <MarketingOverview businessId={businessId} />
    </div>
  )
}
