'use client'

import { RolesOverview } from '@/components/roles/rolesOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Shield, Users, Settings } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'

export default function RolesPage() {
  const t = useTranslations('business.roles')
  const params = useParams()
  const businessId = params.businessId as string
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Shield className="h-4 w-4 mr-2" />
              {t('permissions')}
            </Button>
            <Button variant="outline">
              <Users className="h-4 w-4 mr-2" />
              {t('assignRoles')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('createRole')}
            </Button>
          </div>
        }
      />
      <RolesOverview businessId={businessId} />
    </div>
  )
}
