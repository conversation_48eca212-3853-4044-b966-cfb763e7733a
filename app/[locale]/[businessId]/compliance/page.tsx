'use client'

import { ComplianceOverview } from '@/components/compliance/complianceOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, AlertTriangle, CheckCircle } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'

export default function CompliancePage() {
  const t = useTranslations('business.compliance')
  const params = useParams()
  const businessId = params.businessId as string
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <AlertTriangle className="h-4 w-4 mr-2" />
              {t('viewOverdue')}
            </Button>
            <Button variant="outline">
              <CheckCircle className="h-4 w-4 mr-2" />
              {t('runAudit')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('addRequirement')}
            </Button>
          </div>
        }
      />
      <ComplianceOverview businessId={businessId} />
    </div>
  )
}
