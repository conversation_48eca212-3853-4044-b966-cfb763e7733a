'use client'

import { SecureVaultOverview } from '@/components/secure-vault/secureVaultOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Shield, Key } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'

export default function SecureVaultPage() {
  const t = useTranslations('business.secureVault')
  const params = useParams()
  const businessId = params.businessId as string
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Shield className="h-4 w-4 mr-2" />
              {t('securityAudit')}
            </Button>
            <Button variant="outline">
              <Key className="h-4 w-4 mr-2" />
              {t('generatePassword')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('addCredential')}
            </Button>
          </div>
        }
      />
      <SecureVaultOverview businessId={businessId} />
    </div>
  )
}
