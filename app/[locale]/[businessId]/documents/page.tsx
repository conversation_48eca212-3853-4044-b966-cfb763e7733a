'use client'

import { DocumentManager } from '@/components/documents/documentManager'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Upload, FolderPlus, Search } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'

export default function DocumentsPage() {
  const t = useTranslations('business.documents')
  const params = useParams()
  const businessId = params.businessId as string
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Search className="h-4 w-4 mr-2" />
              {t('search')}
            </Button>
            <Button variant="outline">
              <FolderPlus className="h-4 w-4 mr-2" />
              {t('createFolder')}
            </Button>
            <Button>
              <Upload className="h-4 w-4 mr-2" />
              {t('uploadDocument')}
            </Button>
          </div>
        }
      />
      <DocumentManager businessId={businessId} />
    </div>
  )
}
