'use client'

import { OrdersOverview } from '@/components/orders/ordersOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Download } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'

export default function OrdersPage() {
  const t = useTranslations('business.orders')
  const params = useParams()
  const businessId = params.businessId as string
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              {t('exportOrders')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('createOrder')}
            </Button>
          </div>
        }
      />
      <OrdersOverview businessId={businessId} />
    </div>
  )
}
