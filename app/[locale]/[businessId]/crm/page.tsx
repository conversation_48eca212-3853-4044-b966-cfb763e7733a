'use client'

import { CrmOverview } from '@/components/crm/crmOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Users, Mail } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'

export default function CrmPage() {
  const t = useTranslations('business.crm')
  const params = useParams()
  const businessId = params.businessId as string
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Mail className="h-4 w-4 mr-2" />
              {t('sendCampaign')}
            </Button>
            <Button variant="outline">
              <Users className="h-4 w-4 mr-2" />
              {t('importContacts')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('addContact')}
            </Button>
          </div>
        }
      />
      <CrmOverview businessId={businessId} />
    </div>
  )
}
