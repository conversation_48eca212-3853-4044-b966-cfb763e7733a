'use client'

import { BusinessGroupsOverview } from '@/components/business-groups/businessGroupsOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Building, Users, Settings } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'

export default function BusinessGroupsPage() {
  const t = useTranslations('business.businessGroups')
  const params = useParams()
  const businessId = params.businessId as string
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Users className="h-4 w-4 mr-2" />
              {t('manageAccess')}
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              {t('groupSettings')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('createGroup')}
            </Button>
          </div>
        }
      />
      <BusinessGroupsOverview businessId={businessId} />
    </div>
  )
}
