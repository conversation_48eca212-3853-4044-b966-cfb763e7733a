'use client'

import { BusinessDashboard } from '@/components/business/businessDashboard'
import { PageHeader } from '@/components/common/pageHeader'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'

export default function BusinessDashboardPage() {
  const t = useTranslations('business.dashboard')
  const params = useParams()
  const businessId = params.businessId as string
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
      />
      <BusinessDashboard businessId={businessId} />
    </div>
  )
}
