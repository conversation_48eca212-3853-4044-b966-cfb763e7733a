'use client'

import { IntegrationsOverview } from '@/components/integrations/integrationsOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Zap, Settings, Search } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'

export default function IntegrationsPage() {
  const t = useTranslations('business.integrations')
  const params = useParams()
  const businessId = params.businessId as string
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Search className="h-4 w-4 mr-2" />
              {t('browse')}
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              {t('apiSettings')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('addIntegration')}
            </Button>
          </div>
        }
      />
      <IntegrationsOverview businessId={businessId} />
    </div>
  )
}
