'use client'

import { PlannerOverview } from '@/components/planner/plannerOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Calendar, CheckSquare } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'

export default function PlannerPage() {
  const t = useTranslations('business.planner')
  const params = useParams()
  const businessId = params.businessId as string
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              {t('viewCalendar')}
            </Button>
            <Button variant="outline">
              <CheckSquare className="h-4 w-4 mr-2" />
              {t('createProject')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('addTask')}
            </Button>
          </div>
        }
      />
      <PlannerOverview businessId={businessId} />
    </div>
  )
}
