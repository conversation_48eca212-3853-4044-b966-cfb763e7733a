'use client'

import { BusinessSettings } from '@/components/settings/businessSettings'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Save, RefreshCw, AlertTriangle } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'

export default function BusinessSettingsPage() {
  const t = useTranslations('business.settings')
  const params = useParams()
  const businessId = params.businessId as string
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              {t('reset')}
            </Button>
            <Button variant="outline">
              <AlertTriangle className="h-4 w-4 mr-2" />
              {t('dangerZone')}
            </Button>
            <Button>
              <Save className="h-4 w-4 mr-2" />
              {t('saveChanges')}
            </Button>
          </div>
        }
      />
      <BusinessSettings businessId={businessId} />
    </div>
  )
}
