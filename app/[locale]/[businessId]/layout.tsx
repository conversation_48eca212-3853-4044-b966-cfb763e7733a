import { BusinessLayout } from '@/components/layouts/businessLayout'
import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { businessService } from '@/lib/services/business-service'
import { notFound } from 'next/navigation'

interface BusinessLayoutProps {
  children: React.ReactNode
  params: Promise<{
    locale: string
    businessId: string
  }>
}

export default async function BusinessLayoutPage({
  children,
  params
}: BusinessLayoutProps) {
  const { userId } = await auth()
  const { businessId, locale } = await params
  
  if (!userId) {
    redirect('/auth/sign-in')
  }

  // Verify user has access to this business
  try {
    const business = await businessService.getBusinessById(businessId)
    if (!business) {
      notFound()
    }

    // Check if user has access to this business
    const hasAccess = await businessService.checkUserBusinessAccess(userId, businessId)
    if (!hasAccess) {
      redirect('/dashboard')
    }

    return (
      <BusinessLayout business={business} locale={locale}>
        {children}
      </BusinessLayout>
    )
  } catch (error) {
    console.error('Error loading business:', error)
    notFound()
  }
}
