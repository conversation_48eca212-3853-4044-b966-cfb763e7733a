'use client'

import { UserProfile } from '@/components/profile/userProfile'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Save, Camera, Shield } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'

export default function ProfilePage() {
  const t = useTranslations('business.profile')
  const params = useParams()
  const businessId = params.businessId as string
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Camera className="h-4 w-4 mr-2" />
              {t('changePhoto')}
            </Button>
            <Button variant="outline">
              <Shield className="h-4 w-4 mr-2" />
              {t('security')}
            </Button>
            <Button>
              <Save className="h-4 w-4 mr-2" />
              {t('saveProfile')}
            </Button>
          </div>
        }
      />
      <UserProfile businessId={businessId} />
    </div>
  )
}
