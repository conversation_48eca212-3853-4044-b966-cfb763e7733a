'use client'

import { HrOverview } from '@/components/hr/hrOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Users, Calendar, FileText } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'

export default function HrPage() {
  const t = useTranslations('business.hr')
  const params = useParams()
  const businessId = params.businessId as string
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              {t('timeOff')}
            </Button>
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              {t('payroll')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('addEmployee')}
            </Button>
          </div>
        }
      />
      <HrOverview businessId={businessId} />
    </div>
  )
}
