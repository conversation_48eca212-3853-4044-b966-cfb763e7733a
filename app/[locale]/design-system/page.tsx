'use client'

import { ColorPalette } from '@/components/common/colorPalette'
import { VentureLogo } from '@/components/common/ventureLogo'
import { Button } from '@/components/common/button'
import { Badge } from '@/components/common/badge'
import { PageHeader } from '@/components/common/pageHeader'
import {
  Building2,
  Store,
  ShoppingCart,
  Users,
  DollarSign,
  Settings,
  BarChart3,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Info,
  LayoutDashboard
} from 'lucide-react'

export default function DesignSystemPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6 space-y-12">
        {/* Header */}
        <PageHeader
          title="VentureDirection Design System"
          description="Blue and white color scheme for professional business management"
        />

        {/* Color Test Section */}
        <section className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Color Test</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-4 bg-venture-500 text-white rounded-lg text-center">
              <p className="font-medium">Venture 500</p>
              <p className="text-sm opacity-90">bg-venture-500</p>
            </div>
            <div className="p-4 bg-success-500 text-white rounded-lg text-center">
              <p className="font-medium">Success 500</p>
              <p className="text-sm opacity-90">bg-success-500</p>
            </div>
            <div className="p-4 bg-warning-500 text-white rounded-lg text-center">
              <p className="font-medium">Warning 500</p>
              <p className="text-sm opacity-90">bg-warning-500</p>
            </div>
            <div className="p-4 bg-error-500 text-white rounded-lg text-center">
              <p className="font-medium">Error 500</p>
              <p className="text-sm opacity-90">bg-error-500</p>
            </div>
          </div>
          <div className="mt-4 grid grid-cols-3 gap-4">
            <div className="p-4 bg-venture-100 text-venture-900 rounded-lg text-center">
              <p className="font-medium">Venture Light</p>
              <p className="text-sm opacity-75">bg-venture-100</p>
            </div>
            <div className="p-4 bg-venture-600 text-white rounded-lg text-center">
              <p className="font-medium">Venture Dark</p>
              <p className="text-sm opacity-90">bg-venture-600</p>
            </div>
            <div className="p-4 bg-gray-100 text-gray-900 rounded-lg text-center">
              <p className="font-medium">Gray</p>
              <p className="text-sm opacity-75">bg-gray-100</p>
            </div>
          </div>
        </section>

        {/* Logo Showcase */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Logo Variations</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
              <h3 className="text-lg font-medium text-gray-700 mb-4">Full Logo</h3>
              <VentureLogo size="lg" variant="full" />
            </div>
            <div className="text-center p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
              <h3 className="text-lg font-medium text-gray-700 mb-4">Icon Only</h3>
              <div className="flex justify-center">
                <VentureLogo size="lg" variant="icon" />
              </div>
            </div>
            <div className="text-center p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
              <h3 className="text-lg font-medium text-gray-700 mb-4">Text Only</h3>
              <VentureLogo size="lg" variant="text" />
            </div>
          </div>
        </section>

        {/* Button Showcase */}
        <section className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Button Variants</h2>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-700 mb-3">Primary Variants</h3>
              <div className="flex flex-wrap gap-3">
                <Button variant="default">Primary Button</Button>
                <Button variant="outline">Secondary Button</Button>
                <Button variant="ghost">Ghost Button</Button>
                <Button variant="link">Link Button</Button>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-700 mb-3">Semantic Variants</h3>
              <div className="flex flex-wrap gap-3">
                <Button variant="success">Success Button</Button>
                <Button variant="warning">Warning Button</Button>
                <Button variant="destructive">Error Button</Button>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-700 mb-3">Size Variants</h3>
              <div className="flex flex-wrap gap-3 items-center">
                <Button size="sm">Small</Button>
                <Button size="default">Default</Button>
                <Button size="lg">Large</Button>
              </div>
            </div>
          </div>
        </section>

        {/* Badge Showcase */}
        <section className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Badge Variants</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium text-gray-700 mb-3">Status Badges</h3>
              <div className="flex flex-wrap gap-3">
                <Badge variant="default">Primary</Badge>
                <Badge variant="secondary">Secondary</Badge>
                <Badge variant="success">Success</Badge>
                <Badge variant="warning">Warning</Badge>
                <Badge variant="error">Error</Badge>
                <Badge variant="info">Info</Badge>
                <Badge variant="outline">Outline</Badge>
                <Badge variant="solid">Solid</Badge>
              </div>
            </div>
          </div>
        </section>

        {/* Business Icons */}
        <section className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Business Feature Icons</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {[
              { icon: Building2, name: 'Business', color: 'text-venture-600' },
              { icon: Store, name: 'Store', color: 'text-venture-600' },
              { icon: ShoppingCart, name: 'Orders', color: 'text-venture-600' },
              { icon: Users, name: 'CRM', color: 'text-venture-600' },
              { icon: DollarSign, name: 'Finance', color: 'text-venture-600' },
              { icon: BarChart3, name: 'Analytics', color: 'text-venture-600' },
              { icon: Settings, name: 'Settings', color: 'text-venture-600' },
              { icon: CheckCircle, name: 'Success', color: 'text-success-600' },
              { icon: AlertTriangle, name: 'Warning', color: 'text-warning-600' },
              { icon: XCircle, name: 'Error', color: 'text-error-600' },
              { icon: Info, name: 'Info', color: 'text-venture-600' },
            ].map(({ icon: Icon, name, color }) => (
              <div key={name} className="text-center p-4 bg-gray-50 border border-gray-200 rounded-lg hover:bg-venture-50 hover:border-venture-200 transition-colors">
                <Icon className={`h-8 w-8 mx-auto mb-2 ${color}`} />
                <p className="text-sm font-medium text-gray-700">{name}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Cards */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Card Styles</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Default Card</h3>
              <p className="text-gray-600">White background with subtle border and shadow.</p>
              <Button className="mt-4" size="sm">Action</Button>
            </div>
            <div className="bg-venture-50 border border-venture-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-venture-900 mb-2">Primary Card</h3>
              <p className="text-venture-700">Light blue background for emphasis.</p>
              <Button className="mt-4" size="sm" variant="outline">Action</Button>
            </div>
            <div className="bg-venture-500 text-white rounded-lg p-6 shadow-md">
              <h3 className="text-lg font-semibold mb-2">Accent Card</h3>
              <p className="text-venture-100">Primary blue background for highlights.</p>
              <Button className="mt-4" size="sm" variant="secondary">Action</Button>
            </div>
          </div>
        </section>

        {/* Navigation Example */}
        <section className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Navigation Example</h2>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-700 mb-4">Business Sidebar Navigation</h3>
            <nav className="space-y-1">
              {[
                { name: 'Dashboard', active: true, icon: LayoutDashboard },
                { name: 'Store', active: false, icon: Store },
                { name: 'Orders', active: false, icon: ShoppingCart },
                { name: 'Finance', active: false, icon: DollarSign },
                { name: 'Settings', active: false, icon: Settings },
              ].map((item) => {
                const IconComponent = item.icon
                return (
                  <a
                    key={item.name}
                    href="#"
                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      item.active
                        ? 'bg-venture-500 text-white shadow-sm'
                        : 'text-gray-600 hover:bg-venture-50 hover:text-venture-700'
                    }`}
                  >
                    <IconComponent className="mr-3 h-4 w-4" />
                    {item.name}
                  </a>
                )
              })}
            </nav>
          </div>
        </section>

        {/* Status Indicators */}
        <section className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Status Indicators</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="h-4 w-4 bg-success-500 rounded-full mx-auto mb-2"></div>
              <p className="text-sm font-medium text-gray-700">Active</p>
              <p className="text-xs text-gray-500">bg-success-500</p>
            </div>
            <div className="text-center p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="h-4 w-4 bg-warning-500 rounded-full mx-auto mb-2"></div>
              <p className="text-sm font-medium text-gray-700">Pending</p>
              <p className="text-xs text-gray-500">bg-warning-500</p>
            </div>
            <div className="text-center p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="h-4 w-4 bg-error-500 rounded-full mx-auto mb-2"></div>
              <p className="text-sm font-medium text-gray-700">Error</p>
              <p className="text-xs text-gray-500">bg-error-500</p>
            </div>
            <div className="text-center p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="h-4 w-4 bg-gray-400 rounded-full mx-auto mb-2"></div>
              <p className="text-sm font-medium text-gray-700">Inactive</p>
              <p className="text-xs text-gray-500">bg-gray-400</p>
            </div>
          </div>
        </section>

        {/* Color Palette */}
        <section className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900">Complete Color Palette</h2>
            <p className="text-gray-600 mt-2">VentureDirection's blue and white color system</p>
          </div>
          <ColorPalette />
        </section>
      </div>
    </div>
  )
}
