'use client'

import { PlannerOverview } from '@/components/planner/plannerOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Calendar, CheckSquare, StickyNote } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'

export default function PlannerPage() {
  const t = useTranslations('business.planner')
  const { currentBusiness } = useBusiness()
  
  if (!currentBusiness) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a business to access planner.</p>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              {t('viewCalendar')}
            </Button>
            <Button variant="outline">
              <CheckSquare className="h-4 w-4 mr-2" />
              {t('createProject')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('addTask')}
            </Button>
          </div>
        }
      />
      <PlannerOverview businessId={currentBusiness.id} />
    </div>
  )
}
