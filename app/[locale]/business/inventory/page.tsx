'use client'

import { InventoryOverview } from '@/components/inventory/inventoryOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Package, AlertTriangle } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'

export default function InventoryPage() {
  const t = useTranslations('business.inventory')
  const { currentBusiness } = useBusiness()
  
  if (!currentBusiness) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a business to access inventory.</p>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <AlertTriangle className="h-4 w-4 mr-2" />
              {t('lowStock')}
            </Button>
            <Button variant="outline">
              <Package className="h-4 w-4 mr-2" />
              {t('stockAdjustment')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('addItem')}
            </Button>
          </div>
        }
      />
      <InventoryOverview businessId={currentBusiness.id} />
    </div>
  )
}
