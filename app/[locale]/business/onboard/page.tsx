'use client'

import { OnboardingChecklist } from '@/components/onboarding/onboardingChecklist'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { ArrowLeft, Rocket } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'
import { useRouter } from 'next/navigation'
import { Link } from '@/i18n/navigation'

export default function OnboardPage() {
  const t = useTranslations('onboarding')
  const { currentBusiness } = useBusiness()
  const router = useRouter()
  
  if (!currentBusiness) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a business to access onboarding.</p>
        <Link href="/dashboard">
          <Button className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>
    )
  }
  
  const handleComplete = () => {
    router.push('/dashboard')
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/dashboard">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>
      
      <PageHeader
        title={
          <div className="flex items-center gap-3">
            <Rocket className="h-6 w-6 text-blue-600" />
            {t('pageTitle')}
          </div>
        }
        description={t('pageDescription', { businessName: currentBusiness.name })}
      />
      
      <div className="max-w-4xl">
        <OnboardingChecklist onDismiss={handleComplete} />
      </div>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="font-medium text-blue-900 mb-2">{t('needHelp')}</h3>
        <p className="text-blue-700 text-sm mb-4">
          {t('helpDescription')}
        </p>
        <div className="flex gap-3">
          <Button variant="outline" size="sm">
            {t('viewGuides')}
          </Button>
          <Button variant="outline" size="sm">
            {t('contactSupport')}
          </Button>
        </div>
      </div>
    </div>
  )
}
