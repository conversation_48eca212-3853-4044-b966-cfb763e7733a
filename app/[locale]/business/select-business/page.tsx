'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'
import { BusinessSelector } from '@/components/businesses/businessSelector'
import { PageHeader } from '@/components/common/pageHeader'
import { Loader2 } from 'lucide-react'

export default function SelectBusinessPage() {
  const t = useTranslations('business.switcher')
  const router = useRouter()
  const { businesses, currentBusiness, isLoading, switchBusiness } = useBusiness()

  // Redirect if user already has an active business
  useEffect(() => {
    if (!isLoading && currentBusiness) {
      router.push('/dashboard')
    }
  }, [isLoading, currentBusiness, router])

  const handleBusinessSelect = async (businessId: string, businessGroupId?: string) => {
    try {
      await switchBusiness(businessId, businessGroupId)
      router.push('/dashboard')
    } catch (error) {
      console.error('Failed to select business:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">{t('loading')}</p>
        </div>
      </div>
    )
  }

  // Transform businesses for BusinessSelector
  const businessesForSelector = businesses.map(business => ({
    id: business.id,
    name: business.name,
    type: business.type,
    logo: undefined, // Add logo support later
  }))

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t('selectBusiness')}
          </h1>
          <p className="text-gray-600">
            Choose a business to access your workspace
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <BusinessSelector 
            businesses={businessesForSelector}
            onBusinessSelect={handleBusinessSelect}
          />
        </div>
      </div>
    </div>
  )
}
