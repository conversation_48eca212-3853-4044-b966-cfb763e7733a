'use client'

import { AnalyticsOverview } from '@/components/analytics/analyticsOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { But<PERSON> } from '@/components/common/button'
import { Download, Filter, Calendar } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'

export default function AnalyticsPage() {
  const t = useTranslations('business.analytics')
  const { currentBusiness } = useBusiness()
  
  if (!currentBusiness) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a business to access analytics.</p>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              {t('dateRange')}
            </Button>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              {t('filters')}
            </Button>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              {t('exportReport')}
            </Button>
          </div>
        }
      />
      <AnalyticsOverview businessId={currentBusiness.id} />
    </div>
  )
}
