'use client'

import { StoreOverview } from '@/components/store/storeOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, ShoppingBag } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'

export default function StorePage() {
  const t = useTranslations('business.store')
  const { currentBusiness } = useBusiness()
  
  if (!currentBusiness) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a business to access the store.</p>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <ShoppingBag className="h-4 w-4 mr-2" />
              {t('viewStore')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('addProduct')}
            </Button>
          </div>
        }
      />
      <StoreOverview businessId={currentBusiness.id} />
    </div>
  )
}
