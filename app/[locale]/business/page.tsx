'use client'

import { DashboardOverview } from '@/components/dashboard';
import { BusinessDashboard } from '@/components/business/businessDashboard';
import { BusinessSelector } from '@/components/businesses/businessSelector'
import { OnboardingChecklist } from '@/components/onboarding/onboardingChecklist'
import { PageHeader } from '@/components/common';
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'
import { useOnboarding } from '@/lib/hooks/useOnboarding'

export default function DashboardPage() {
  const t = useTranslations('dashboard')
  const { currentBusiness, businesses, isLoading } = useBusiness()
  const { onboardingStatus } = useOnboarding()

  if (isLoading) {
    return (
      <div className="space-y-6">
        <PageHeader
          title={t('title')}
          description={t('description')}
        />
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your businesses...</p>
        </div>
      </div>
    )
  }

  // If user has a selected business, show business-specific dashboard
  if (currentBusiness) {
    const showOnboarding = onboardingStatus && !onboardingStatus.isFullyOnboarded

    return (
      <div className="space-y-6">
        <PageHeader
          title={`${currentBusiness.name} Dashboard`}
          description={t('businessDescription')}
        />

        {/* Show onboarding checklist if not fully onboarded */}
        {showOnboarding && (
          <OnboardingChecklist compact />
        )}

        <BusinessDashboard businessId={currentBusiness.id} />
      </div>
    )
  }
  
  // If no business selected, show business selector
  const businessesForSelector = businesses.map(business => ({
    id: business.id,
    name: business.name,
    type: business.type,
    logo: undefined,
  }))
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
      />
      
      <div className="grid gap-8 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <DashboardOverview />
        </div>
        
        <div>
          <h3 className="text-lg font-medium mb-4">Your Businesses</h3>
          <BusinessSelector businesses={businessesForSelector} />
        </div>
      </div>
    </div>
  )
}
