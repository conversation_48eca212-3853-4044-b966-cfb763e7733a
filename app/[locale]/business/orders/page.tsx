'use client'

import { OrdersOverview } from '@/components/orders/ordersOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Download } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'

export default function OrdersPage() {
  const t = useTranslations('business.orders')
  const { currentBusiness } = useBusiness()
  
  if (!currentBusiness) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a business to access orders.</p>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              {t('exportOrders')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('createOrder')}
            </Button>
          </div>
        }
      />
      <OrdersOverview businessId={currentBusiness.id} />
    </div>
  )
}
