'use client'

import { MarketingOverview } from '@/components/marketing/marketingOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Target, BarChart } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'

export default function MarketingPage() {
  const t = useTranslations('business.marketing')
  const { currentBusiness } = useBusiness()
  
  if (!currentBusiness) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a business to access marketing.</p>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <BarChart className="h-4 w-4 mr-2" />
              {t('analytics')}
            </Button>
            <Button variant="outline">
              <Target className="h-4 w-4 mr-2" />
              {t('audience')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('createCampaign')}
            </Button>
          </div>
        }
      />
      <MarketingOverview businessId={currentBusiness.id} />
    </div>
  )
}
