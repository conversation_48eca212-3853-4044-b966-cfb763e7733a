'use client'

import { SecureVaultOverview } from '@/components/secure-vault/secureVaultOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Shield, Key, Lock } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'

export default function SecureVaultPage() {
  const t = useTranslations('business.secureVault')
  const { currentBusiness } = useBusiness()
  
  if (!currentBusiness) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a business to access secure vault.</p>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Shield className="h-4 w-4 mr-2" />
              {t('securityAudit')}
            </Button>
            <Button variant="outline">
              <Key className="h-4 w-4 mr-2" />
              {t('generatePassword')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('addCredential')}
            </Button>
          </div>
        }
      />
      <SecureVaultOverview businessId={currentBusiness.id} />
    </div>
  )
}
