'use client'

import { DocumentManager } from '@/components/documents/documentManager'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Upload } from 'lucide-react'
import { useTranslations } from 'next-intl'

export default function DocumentsPage() {
  const t = useTranslations('documents')
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <Button>
            <Upload className="h-4 w-4 mr-2" />
            {t('uploadDocument')}
          </Button>
        }
      />
      <DocumentManager />
    </div>
  )
}
