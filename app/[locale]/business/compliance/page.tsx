'use client'

import { ComplianceOverview } from '@/components/compliances/complianceOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, AlertTriangle, CheckCircle, FileCheck } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'

export default function CompliancePage() {
  const t = useTranslations('business.compliance')
  const { currentBusiness } = useBusiness()
  
  if (!currentBusiness) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a business to access compliance.</p>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <AlertTriangle className="h-4 w-4 mr-2" />
              {t('viewOverdue')}
            </Button>
            <Button variant="outline">
              <CheckCircle className="h-4 w-4 mr-2" />
              {t('runAudit')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('addRequirement')}
            </Button>
          </div>
        }
      />
      <ComplianceOverview businessId={currentBusiness.id} />
    </div>
  )
}
