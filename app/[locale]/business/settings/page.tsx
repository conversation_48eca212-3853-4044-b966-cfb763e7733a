

import { SettingsLayout } from '@/components/layouts';
import { PageHeader } from '@/components/common/pageHeader';
import { getTranslations } from 'next-intl/server';
import { BusinessGroupSettings, BusinessSettings, UserSettings } from '@/components/settings';


export default async function SettingsPage() {
  const t = await getTranslations('settings');
  const {type }  = {} as any;

  const renderSettings = () => {
    switch (type) {
      case 'user':
        return <UserSettings />;
      case 'business':
        return <BusinessSettings />;
      case 'businessGroup':
        return <BusinessGroupSettings />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader title={t('title')} description={t('description')} />
      <SettingsLayout>{renderSettings()}</SettingsLayout>
    </div>
  );
}