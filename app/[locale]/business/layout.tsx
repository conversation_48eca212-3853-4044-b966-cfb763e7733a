'use client'

import { BusinessWorkspaceLayout } from '@/components/layouts/businessWorkspaceLayout'
import { useBusiness } from '@/lib/contexts/business-context'

export default function BusinessLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { currentBusiness } = useBusiness()
  
  return (
    <BusinessWorkspaceLayout businessId={currentBusiness?.id}>
      {children}
    </BusinessWorkspaceLayout>
  )
}