'use client'

import { DashboardOverview } from '@/components/dashboard';
import { BusinessSelector } from '@/components/businesses/businessSelector'
import { PageHeader } from '@/components/common';
import { useTranslations } from 'next-intl'
import { useUser } from '@clerk/nextjs'
import { useEffect, useState } from 'react'

interface Business {
  id: string
  name: string
  type: string
  logo?: string
}

export default function DashboardPage() {
  const t = useTranslations('dashboard')
  const { user } = useUser()
  const [businesses, setBusinesses] = useState<Business[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // TODO: Fetch user's businesses from API
    // For now, using mock data
    const mockBusinesses: Business[] = [
      {
        id: 'business-1',
        name: 'Acme Corporation',
        type: 'Technology',
      },
      {
        id: 'business-2',
        name: 'Green Valley Retail',
        type: 'Retail',
      },
    ]

    setBusinesses(mockBusinesses)
    setLoading(false)
  }, [user])

  if (loading) {
    return (
      <div className="space-y-6">
        <PageHeader
          title={t('title')}
          description={t('description')}
        />
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your businesses...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
      />

      <div className="grid gap-8 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <DashboardOverview />
        </div>

        <div>
          <h3 className="text-lg font-medium mb-4">Your Businesses</h3>
          <BusinessSelector businesses={businesses} />
        </div>
      </div>
    </div>
  )
}
