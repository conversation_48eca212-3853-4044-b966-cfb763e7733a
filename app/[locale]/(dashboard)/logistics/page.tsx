'use client'

import { LogisticsOverview } from '@/components/logistics/logisticsOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Truck, MapPin, Package, Plane } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'

export default function LogisticsPage() {
  const t = useTranslations('business.logistics')
  const { currentBusiness } = useBusiness()
  
  if (!currentBusiness) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a business to access logistics.</p>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <MapPin className="h-4 w-4 mr-2" />
              {t('trackShipments')}
            </Button>
            <Button variant="outline">
              <Package className="h-4 w-4 mr-2" />
              {t('createShipment')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('addCarrier')}
            </Button>
          </div>
        }
      />
      <LogisticsOverview businessId={currentBusiness.id} />
    </div>
  )
}
