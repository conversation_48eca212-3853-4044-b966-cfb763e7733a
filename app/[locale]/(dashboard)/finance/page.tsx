'use client'

import { FinanceOverview } from '@/components/finance/financeOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Download, CreditCard, Receipt } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'

export default function FinancePage() {
  const t = useTranslations('business.finance')
  const { currentBusiness } = useBusiness()

  if (!currentBusiness) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a business to access finance.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              {t('exportReports')}
            </Button>
            <Button variant="outline">
              <Receipt className="h-4 w-4 mr-2" />
              {t('addExpense')}
            </Button>
            <Button>
              <CreditCard className="h-4 w-4 mr-2" />
              {t('createInvoice')}
            </Button>
          </div>
        }
      />
      <FinanceOverview businessId={currentBusiness.id} />
    </div>
  )
}
