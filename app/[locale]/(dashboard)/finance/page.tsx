'use client'

import { FinanceOverview } from '@/components/finance/financeOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus } from 'lucide-react'
import { useTranslations } from 'next-intl'

export default function FinancePage() {
  const t = useTranslations('finance')
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            {t('addTransaction')}
          </Button>
        }
      />
      <FinanceOverview />
    </div>
  )
}
