'use client'

import { InvoiceEditor } from '@/components/finance/invoiceEditor'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Save, Send, ArrowLeft, Eye } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'
import { Link } from '@/i18n/navigation'
import { useBusiness } from '@/lib/contexts/business-context'

export default function InvoiceEditPage() {
  const t = useTranslations('business.finance.invoice')
  const params = useParams()
  const invoiceId = params.invoiceId as string
  const { currentBusiness } = useBusiness()
  
  if (!currentBusiness) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a business to edit invoices.</p>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/finance">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('backToFinance')}
          </Button>
        </Link>
      </div>
      
      <PageHeader
        title={t('editTitle')}
        description={t('editDescription')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Eye className="h-4 w-4 mr-2" />
              {t('preview')}
            </Button>
            <Button variant="outline">
              <Save className="h-4 w-4 mr-2" />
              {t('saveDraft')}
            </Button>
            <Button>
              <Send className="h-4 w-4 mr-2" />
              {t('sendInvoice')}
            </Button>
          </div>
        }
      />
      
      <InvoiceEditor businessId={currentBusiness.id} invoiceId={invoiceId} />
    </div>
  )
}
