'use client'

import { CustomerDetails } from '@/components/crm/customerDetails'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Edit, Mail, Phone, ArrowLeft } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useParams } from 'next/navigation'
import { Link } from '@/i18n/navigation'
import { useBusiness } from '@/lib/contexts/business-context'

export default function CustomerViewPage() {
  const t = useTranslations('business.crm.customer')
  const params = useParams()
  const customerId = params.customerId as string
  const { currentBusiness } = useBusiness()
  
  if (!currentBusiness) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a business to access customer details.</p>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/crm">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('backToContacts')}
          </Button>
        </Link>
      </div>
      
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Phone className="h-4 w-4 mr-2" />
              {t('call')}
            </Button>
            <Button variant="outline">
              <Mail className="h-4 w-4 mr-2" />
              {t('email')}
            </Button>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              {t('edit')}
            </Button>
          </div>
        }
      />
      
      <CustomerDetails businessId={currentBusiness.id} customerId={customerId} />
    </div>
  )
}
