'use client'

import { CrmOverview } from '@/components/crm/crmOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus } from 'lucide-react'
import { useTranslations } from 'next-intl'

export default function CrmPage() {
  const t = useTranslations('crm')
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            {t('addContact')}
          </Button>
        }
      />
      <CrmOverview />
    </div>
  )
}
