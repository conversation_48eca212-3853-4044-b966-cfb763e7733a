'use client'

import { BusinessList } from '@/components/businesses/businessList'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus } from 'lucide-react'
import { Link } from '@/i18n/navigation'
import { useTranslations } from 'next-intl'

export default function BusinessesPage() {
  const t = useTranslations('businesses')
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <Button asChild>
            <Link href="/businesses/new">
              <Plus className="h-4 w-4 mr-2" />
              {t('addBusiness')}
            </Link>
          </Button>
        }
      />
      <BusinessList />
    </div>
  )
}
