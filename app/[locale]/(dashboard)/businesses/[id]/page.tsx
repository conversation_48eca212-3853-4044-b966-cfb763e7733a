import { BusinessDetails } from '@/components/businesses/businessDetails'
import { PageHeader } from '@/components/common/pageHeader'
import { notFound } from 'next/navigation'

interface BusinessPageProps {
  params: Promise<{
    id: string
    locale: string
  }>
}

export default async function BusinessPage({ params }: BusinessPageProps) {
  const { id } = await params

  // TODO: Fetch business data
  const business = null // await getBusinessById(id)
  
  if (!business) {
    notFound()
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title={business?.name || 'Business Details'}
        description="View and manage business information"
      />
      <BusinessDetails businessId={id} />
    </div>
  )
}
