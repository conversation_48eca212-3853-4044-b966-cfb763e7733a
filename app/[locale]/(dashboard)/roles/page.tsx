'use client'

import { RolesOverview } from '@/components/roles/rolesOverview'
import { PageHeader } from '@/components/common/pageHeader'
import { Button } from '@/components/common/button'
import { Plus, Shield, Users } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'

export default function RolesPage() {
  const t = useTranslations('business.roles')
  const { currentBusiness } = useBusiness()

  if (!currentBusiness) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a business to access roles.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
        action={
          <div className="flex gap-2">
            <Button variant="outline">
              <Shield className="h-4 w-4 mr-2" />
              {t('permissions')}
            </Button>
            <Button variant="outline">
              <Users className="h-4 w-4 mr-2" />
              {t('assignRoles')}
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('createRole')}
            </Button>
          </div>
        }
      />
      <RolesOverview businessId={currentBusiness.id} />
    </div>
  )
}
