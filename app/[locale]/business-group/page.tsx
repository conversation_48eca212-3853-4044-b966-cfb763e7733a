'use client'

import { PageHeader } from '@/components/common/pageHeader'
import { useTranslations } from 'next-intl'
import { Building2, Users, BarChart3, Settings } from 'lucide-react'

export default function BusinessGroupPage() {
  const t = useTranslations('businessGroup')
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border shadow-sm p-6">
          <div className="flex items-center gap-3 mb-4">
            <Building2 className="h-8 w-8 text-blue-600" />
            <h3 className="text-lg font-semibold">Businesses</h3>
          </div>
          <p className="text-gray-600 mb-4">
            Manage all businesses in your group
          </p>
          <div className="text-2xl font-bold text-blue-600">5</div>
          <div className="text-sm text-gray-500">Active businesses</div>
        </div>
        
        <div className="bg-white rounded-lg border shadow-sm p-6">
          <div className="flex items-center gap-3 mb-4">
            <Users className="h-8 w-8 text-green-600" />
            <h3 className="text-lg font-semibold">Members</h3>
          </div>
          <p className="text-gray-600 mb-4">
            Group members and permissions
          </p>
          <div className="text-2xl font-bold text-green-600">24</div>
          <div className="text-sm text-gray-500">Total members</div>
        </div>
        
        <div className="bg-white rounded-lg border shadow-sm p-6">
          <div className="flex items-center gap-3 mb-4">
            <BarChart3 className="h-8 w-8 text-purple-600" />
            <h3 className="text-lg font-semibold">Analytics</h3>
          </div>
          <p className="text-gray-600 mb-4">
            Cross-business insights
          </p>
          <div className="text-2xl font-bold text-purple-600">$125K</div>
          <div className="text-sm text-gray-500">Total revenue</div>
        </div>
      </div>
    </div>
  )
}