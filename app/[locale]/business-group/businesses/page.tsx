'use client'

import { PageHeader } from '@/components/common/pageHeader'
import { useTranslations } from 'next-intl'
import { Building2 } from 'lucide-react'

export default function BusinessGroupBusinessesPage() {
  const t = useTranslations('businessGroup.businesses')
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
      />
      
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">Business Management</h3>
        <p className="text-gray-600">
          Manage all businesses within your business group.
        </p>
      </div>
    </div>
  )
}