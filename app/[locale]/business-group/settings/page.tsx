'use client'

import { PageHeader } from '@/components/common/pageHeader'
import { useTranslations } from 'next-intl'
import { Settings } from 'lucide-react'

export default function BusinessGroupSettingsPage() {
  const t = useTranslations('businessGroup.settings')
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
      />
      
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">Group Settings</h3>
        <p className="text-gray-600">
          Configure settings for your business group.
        </p>
      </div>
    </div>
  )
}