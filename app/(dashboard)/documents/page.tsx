import { DocumentManager } from '@/components/documents/document-manager'
import { PageHeader } from '@/components/ui/page-header'
import { But<PERSON> } from '@/components/ui/button'
import { Upload } from 'lucide-react'

export default function DocumentsPage() {
  return (
    <div className="space-y-6">
      <PageHeader
        title="Documents"
        description="Manage business documents and files"
        action={
          <Button>
            <Upload className="h-4 w-4 mr-2" />
            Upload Document
          </Button>
        }
      />
      <DocumentManager />
    </div>
  )
}
