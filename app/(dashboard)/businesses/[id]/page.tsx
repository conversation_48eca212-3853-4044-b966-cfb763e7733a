import { BusinessDetails } from '@/components/businesses'
import { PageHeader } from '@/components/common'
import { notFound } from 'next/navigation'

interface BusinessPageProps {
  params: {
    id: string
  }
}

export default async function BusinessPage({ params }: BusinessPageProps) {
  // TODO: Fetch business data
  const business: { name: string } = { name: 'Bello Limited' } // await getBusinessById(params.id)
  
  if (!business) {
    notFound()
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title={business?.name || 'Business Details'}
        description="View and manage business information"
      />
      <BusinessDetails businessId={params.id} />
    </div>
  )
}
