import { DashboardLayout } from '@/components/layouts/dashboard-layout'
import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'

export default async function DashboardLayoutPage({
  children,
}: {
  children: React.ReactNode
}) {
  const { userId } = await auth()
  
  if (!userId) {
    redirect('/auth/sign-in')
  }

  return <DashboardLayout>{children}</DashboardLayout>
}
