'use client'

import { useTranslations } from 'next-intl'

interface MarketingOverviewProps {
  businessId: string
}

export function MarketingOverview({ businessId }: MarketingOverviewProps) {
  const t = useTranslations('business.marketing')
  
  return (
    <div className="space-y-6">
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h3 className="text-lg font-medium mb-4">{t('overview')}</h3>
        <div className="grid gap-4 md:grid-cols-4">
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Active Campaigns</h4>
            <p className="text-2xl font-bold">5</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Email Subscribers</h4>
            <p className="text-2xl font-bold">2,450</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Open Rate</h4>
            <p className="text-2xl font-bold">24.5%</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Click Rate</h4>
            <p className="text-2xl font-bold">3.2%</p>
          </div>
        </div>
      </div>
    </div>
  )
}
