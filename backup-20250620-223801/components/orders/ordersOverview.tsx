'use client'

import { useTranslations } from 'next-intl'

interface OrdersOverviewProps {
  businessId: string
}

export function OrdersOverview({ businessId }: OrdersOverviewProps) {
  const t = useTranslations('business.orders')
  
  return (
    <div className="space-y-6">
      {/* Order Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('totalOrders')}</h3>
          <div className="text-2xl font-bold">2,350</div>
          <p className="text-xs text-muted-foreground">+180 this month</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('pendingOrders')}</h3>
          <div className="text-2xl font-bold">23</div>
          <p className="text-xs text-muted-foreground">Needs attention</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('completedToday')}</h3>
          <div className="text-2xl font-bold">45</div>
          <p className="text-xs text-muted-foreground">+12 from yesterday</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('totalRevenue')}</h3>
          <div className="text-2xl font-bold">$45,231</div>
          <p className="text-xs text-muted-foreground">+20.1% from last month</p>
        </div>
      </div>

      {/* Recent Orders */}
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <div className="p-6">
          <h3 className="text-lg font-medium mb-4">{t('recentOrders')}</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <p className="font-medium">#ORD-001234</p>
                <p className="text-sm text-muted-foreground">John Doe • 2 items</p>
              </div>
              <div className="text-right">
                <p className="font-medium">$299.99</p>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  Processing
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <p className="font-medium">#ORD-001233</p>
                <p className="text-sm text-muted-foreground">Jane Smith • 1 item</p>
              </div>
              <div className="text-right">
                <p className="font-medium">$149.99</p>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Completed
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <p className="font-medium">#ORD-001232</p>
                <p className="text-sm text-muted-foreground">Bob Johnson • 3 items</p>
              </div>
              <div className="text-right">
                <p className="font-medium">$89.97</p>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Shipped
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
