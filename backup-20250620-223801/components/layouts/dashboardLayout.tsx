'use client'

import { UserButton } from '@clerk/nextjs'
import { useTranslations } from 'next-intl'
import { Link } from '@/i18n/navigation'
import { LanguageSwitcherCompact } from '@/components/languageSwitcher'
import { BusinessSwitcher } from '@/components/business/businessSwitcher'
import {
  LayoutDashboard,
  Building2,
  Settings,
  Store,
  ShoppingCart,
  Package,
  Users,
  DollarSign,
  FileCheck,
  FileText,
  Shield,
  Calendar,
  UserCheck,
  Megaphone,
  BarChart3,
  Zap,
  Truck,
  Lock,
  CheckSquare,
  Network,
  Rocket
} from 'lucide-react'

export function DashboardLayout({ children }: { children: React.ReactNode }) {
  const t = useTranslations('navigation')

  const navigation = [
    { name: t('dashboard'), href: '/dashboard' as const, icon: LayoutDashboard },
    { name: t('businesses'), href: '/businesses' as const, icon: Building2 },
    { name: t('businessGroups'), href: '/business-group' as const, icon: Network },
  ]

  // Business-specific navigation (shown when in business context)
  const businessNavigation = [
    { name: 'Business Dashboard', href: '/business' as const, icon: LayoutDashboard },
    { name: t('onboard'), href: '/business/onboard' as const, icon: Rocket },
    { name: t('store'), href: '/business/store' as const, icon: Store },
    { name: t('orders'), href: '/business/orders' as const, icon: ShoppingCart },
    { name: t('inventory'), href: '/business/inventory' as const, icon: Package },
    { name: t('crm'), href: '/business/crm' as const, icon: Users },
    { name: t('finance'), href: '/business/finance' as const, icon: DollarSign },
    { name: t('compliance'), href: '/business/compliance' as const, icon: FileCheck },
    { name: t('documents'), href: '/business/documents' as const, icon: FileText },
    { name: t('secureVault'), href: '/business/secure-vault' as const, icon: Lock },
    { name: t('planner'), href: '/business/planner' as const, icon: CheckSquare },
    { name: t('hr'), href: '/business/hr' as const, icon: UserCheck },
    { name: t('roles'), href: '/business/roles' as const, icon: Shield },
    { name: t('marketing'), href: '/business/marketing' as const, icon: Megaphone },
    { name: t('analytics'), href: '/business/analytics' as const, icon: BarChart3 },
    { name: t('integrations'), href: '/business/integrations' as const, icon: Zap },
    { name: t('logistics'), href: '/business/logistics' as const, icon: Truck },
    { name: t('settings'), href: '/business/settings' as const, icon: Settings },
  ]
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg">
        <div className="flex h-16 items-center px-6">
          <h1 className="text-xl font-semibold">VentureDirection</h1>
        </div>

        {/* Business Switcher */}
        <div className="px-4 py-4 border-b border-gray-200">
          <BusinessSwitcher />
        </div>

        <nav className="mt-4 px-4">
          <ul className="space-y-2">
            {navigation.map((item) => (
              <li key={item.name}>
                <Link
                  href={item.href as any}
                  className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900"
                >
                  <item.icon className="mr-3 h-5 w-5" />
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </div>

      {/* Main content */}
      <div className="pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 items-center justify-between bg-white px-6 shadow-sm">
          <div className="flex items-center gap-4">
            <LanguageSwitcherCompact />
          </div>
          <UserButton />
        </div>

        {/* Page content */}
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  )
}
