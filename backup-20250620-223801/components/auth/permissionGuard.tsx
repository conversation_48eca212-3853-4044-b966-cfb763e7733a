'use client'

import { ReactNode } from 'react'
import { useVentureUser } from '@/lib/hooks/useVentureUser'
import { AlertCircle, Lock } from 'lucide-react'

interface PermissionGuardProps {
  permission?: string
  role?: string
  resource?: string
  action?: string
  businessId?: string
  children: ReactNode
  fallback?: ReactNode
  showFallback?: boolean
}

export function PermissionGuard({
  permission,
  role,
  resource,
  action,
  businessId,
  children,
  fallback,
  showFallback = true
}: PermissionGuardProps) {
  const { hasPermission, hasRole, canAccess, isLoading } = useVentureUser()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
        <span className="ml-2 text-sm text-gray-600">Checking permissions...</span>
      </div>
    )
  }

  let hasAccess = false

  if (permission) {
    hasAccess = hasPermission(permission, businessId)
  } else if (role) {
    hasAccess = hasRole(role, businessId)
  } else if (resource && action) {
    hasAccess = canAccess(resource, action, businessId)
  }

  if (hasAccess) {
    return <>{children}</>
  }

  if (!showFallback) {
    return null
  }

  if (fallback) {
    return <>{fallback}</>
  }

  return (
    <div className="flex items-center justify-center p-8 text-center">
      <div className="max-w-md">
        <Lock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
        <p className="text-gray-600">
          You don't have permission to access this feature. Contact your administrator if you believe this is an error.
        </p>
      </div>
    </div>
  )
}

interface RoleGuardProps {
  role: string
  businessId?: string
  children: ReactNode
  fallback?: ReactNode
  showFallback?: boolean
}

export function RoleGuard({
  role,
  businessId,
  children,
  fallback,
  showFallback = true
}: RoleGuardProps) {
  return (
    <PermissionGuard
      role={role}
      businessId={businessId}
      fallback={fallback}
      showFallback={showFallback}
    >
      {children}
    </PermissionGuard>
  )
}

interface AdminGuardProps {
  businessId?: string
  children: ReactNode
  fallback?: ReactNode
  showFallback?: boolean
}

export function AdminGuard({
  businessId,
  children,
  fallback,
  showFallback = true
}: AdminGuardProps) {
  const { user, isLoading } = useVentureUser()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
        <span className="ml-2 text-sm text-gray-600">Checking permissions...</span>
      </div>
    )
  }

  const isAdmin = businessId 
    ? user?.businessMemberships.find(m => m.businessId === businessId)?.userRole.toLowerCase().includes('admin')
    : user?.isBusinessAdmin

  if (isAdmin) {
    return <>{children}</>
  }

  if (!showFallback) {
    return null
  }

  if (fallback) {
    return <>{fallback}</>
  }

  return (
    <div className="flex items-center justify-center p-8 text-center">
      <div className="max-w-md">
        <AlertCircle className="h-12 w-12 text-amber-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Admin Access Required</h3>
        <p className="text-gray-600">
          This feature requires administrator privileges. Contact your business owner for access.
        </p>
      </div>
    </div>
  )
}

interface OwnerGuardProps {
  businessId?: string
  children: ReactNode
  fallback?: ReactNode
  showFallback?: boolean
}

export function OwnerGuard({
  businessId,
  children,
  fallback,
  showFallback = true
}: OwnerGuardProps) {
  const { user, isOwnerOfBusiness, isLoading } = useVentureUser()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
        <span className="ml-2 text-sm text-gray-600">Checking permissions...</span>
      </div>
    )
  }

  const isOwner = businessId 
    ? isOwnerOfBusiness(businessId)
    : user?.isBusinessOwner

  if (isOwner) {
    return <>{children}</>
  }

  if (!showFallback) {
    return null
  }

  if (fallback) {
    return <>{fallback}</>
  }

  return (
    <div className="flex items-center justify-center p-8 text-center">
      <div className="max-w-md">
        <Lock className="h-12 w-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Owner Access Required</h3>
        <p className="text-gray-600">
          This feature is restricted to business owners only.
        </p>
      </div>
    </div>
  )
}
