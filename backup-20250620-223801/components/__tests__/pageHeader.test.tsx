import { render, screen } from '@testing-library/react'
import { PageHeader } from '../common/pageHeader'

describe('PageHeader Component', () => {
  it('renders title correctly', () => {
    render(<PageHeader title="Test Title" />)
    
    expect(screen.getByText('Test Title')).toBeInTheDocument()
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Test Title')
  })

  it('renders description when provided', () => {
    render(
      <PageHeader 
        title="Test Title" 
        description="This is a test description" 
      />
    )
    
    expect(screen.getByText('This is a test description')).toBeInTheDocument()
  })

  it('does not render description when not provided', () => {
    render(<PageHeader title="Test Title" />)
    
    expect(screen.queryByText(/description/i)).not.toBeInTheDocument()
  })

  it('renders action component when provided', () => {
    const ActionComponent = () => <button>Action Button</button>
    
    render(
      <PageHeader 
        title="Test Title" 
        action={<ActionComponent />} 
      />
    )
    
    expect(screen.getByText('Action Button')).toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(
      <PageHeader 
        title="Test Title" 
        className="custom-header-class" 
      />
    )
    
    const header = screen.getByText('Test Title').closest('div')
    expect(header).toHaveClass('custom-header-class')
  })

  it('has correct default styling', () => {
    render(<PageHeader title="Test Title" />)
    
    const container = screen.getByText('Test Title').closest('div')
    expect(container).toHaveClass('flex', 'items-center', 'justify-between')
    
    const title = screen.getByRole('heading', { level: 1 })
    expect(title).toHaveClass('text-2xl', 'font-semibold', 'tracking-tight')
  })

  it('renders complete header with all props', () => {
    render(
      <PageHeader 
        title="Complete Header"
        description="Full description text"
        action={<button>Complete Action</button>}
        className="complete-class"
      />
    )
    
    expect(screen.getByText('Complete Header')).toBeInTheDocument()
    expect(screen.getByText('Full description text')).toBeInTheDocument()
    expect(screen.getByText('Complete Action')).toBeInTheDocument()
    
    const container = screen.getByText('Complete Header').closest('div')
    expect(container).toHaveClass('complete-class')
  })
})
