'use client'

import { useTranslations } from 'next-intl'

interface AnalyticsOverviewProps {
  businessId: string
}

export function AnalyticsOverview({ businessId }: AnalyticsOverviewProps) {
  const t = useTranslations('business.analytics')
  
  return (
    <div className="space-y-6">
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h3 className="text-lg font-medium mb-4">{t('overview')}</h3>
        <div className="grid gap-4 md:grid-cols-4">
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Page Views</h4>
            <p className="text-2xl font-bold">45,231</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Unique Visitors</h4>
            <p className="text-2xl font-bold">12,450</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Bounce Rate</h4>
            <p className="text-2xl font-bold">32.1%</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Avg Session</h4>
            <p className="text-2xl font-bold">4:32</p>
          </div>
        </div>
      </div>
    </div>
  )
}
