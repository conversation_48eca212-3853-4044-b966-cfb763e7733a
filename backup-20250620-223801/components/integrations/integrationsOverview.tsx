'use client'

import { useTranslations } from 'next-intl'

interface IntegrationsOverviewProps {
  businessId: string
}

export function IntegrationsOverview({ businessId }: IntegrationsOverviewProps) {
  const t = useTranslations('business.integrations')
  
  return (
    <div className="space-y-6">
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h3 className="text-lg font-medium mb-4">{t('overview')}</h3>
        <div className="grid gap-4 md:grid-cols-3">
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Active Integrations</h4>
            <p className="text-2xl font-bold">12</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Available</h4>
            <p className="text-2xl font-bold">45</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">API Calls</h4>
            <p className="text-2xl font-bold">1.2M</p>
          </div>
        </div>
      </div>
    </div>
  )
}
