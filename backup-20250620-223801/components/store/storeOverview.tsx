'use client'

import { useTranslations } from 'next-intl'

interface StoreOverviewProps {
  businessId: string
}

export function StoreOverview({ businessId }: StoreOverviewProps) {
  const t = useTranslations('business.store')
  
  return (
    <div className="space-y-6">
      {/* Store Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('totalProducts')}</h3>
          <div className="text-2xl font-bold">1,234</div>
          <p className="text-xs text-muted-foreground">+12 this week</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('storeViews')}</h3>
          <div className="text-2xl font-bold">45,231</div>
          <p className="text-xs text-muted-foreground">+20.1% from last month</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('conversionRate')}</h3>
          <div className="text-2xl font-bold">3.2%</div>
          <p className="text-xs text-muted-foreground">+0.5% from last month</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('averageOrderValue')}</h3>
          <div className="text-2xl font-bold">$89.50</div>
          <p className="text-xs text-muted-foreground">+$12.30 from last month</p>
        </div>
      </div>

      {/* Products Table */}
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <div className="p-6">
          <h3 className="text-lg font-medium mb-4">{t('recentProducts')}</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-gray-200 rounded"></div>
                <div>
                  <p className="font-medium">Premium Headphones</p>
                  <p className="text-sm text-muted-foreground">Electronics</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium">$299.99</p>
                <p className="text-sm text-muted-foreground">In Stock: 45</p>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-gray-200 rounded"></div>
                <div>
                  <p className="font-medium">Wireless Mouse</p>
                  <p className="text-sm text-muted-foreground">Electronics</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium">$49.99</p>
                <p className="text-sm text-muted-foreground">In Stock: 120</p>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-gray-200 rounded"></div>
                <div>
                  <p className="font-medium">Coffee Mug</p>
                  <p className="text-sm text-muted-foreground">Home & Garden</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium">$19.99</p>
                <p className="text-sm text-red-500">Low Stock: 3</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
