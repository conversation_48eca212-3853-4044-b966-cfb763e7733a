'use client'

import { useBusinessUser } from '@/lib/contexts/business-user-context'
import { useBusinessPermissions, useBusinessContext } from '@/lib/hooks/useBusinessPermissions'
import { Badge } from '@/components/common/badge'
import { Button } from '@/components/common/button'
import { 
  Building2, 
  Users, 
  Settings, 
  DollarSign, 
  Shield,
  Crown,
  CheckCircle,
  XCircle
} from 'lucide-react'

export function BusinessContextExample() {
  const { 
    user,
    businessName,
    userRole,
    isOwner,
    isAdmin,
    permissions,
    roles,
    refresh
  } = useBusinessUser()

  const businessPermissions = useBusinessPermissions()
  const businessContext = useBusinessContext()

  if (!user) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">No user context available</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Business Context Header */}
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Business Context
          </h2>
          <Button onClick={refresh} variant="outline" size="sm">
            Refresh
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <h3 className="font-medium text-gray-900">Business</h3>
            <p className="text-gray-600">{businessName}</p>
          </div>
          <div>
            <h3 className="font-medium text-gray-900">Your Role</h3>
            <div className="flex items-center gap-2">
              <p className="text-gray-600">{userRole}</p>
              {isOwner && (
                <Badge variant="warning">
                  <Crown className="h-3 w-3 mr-1" />
                  Owner
                </Badge>
              )}
              {isAdmin && !isOwner && (
                <Badge variant="info">
                  <Shield className="h-3 w-3 mr-1" />
                  Admin
                </Badge>
              )}
            </div>
          </div>
          <div>
            <h3 className="font-medium text-gray-900">Permissions</h3>
            <p className="text-gray-600">{permissions.length} permissions</p>
          </div>
        </div>
      </div>

      {/* Permission Checks */}
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Permission Checks</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* User Management */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900 flex items-center gap-2">
              <Users className="h-4 w-4" />
              User Management
            </h4>
            <div className="space-y-1 text-sm">
              <PermissionCheck 
                label="View Users" 
                hasPermission={businessPermissions.canViewUsers} 
              />
              <PermissionCheck 
                label="Manage Users" 
                hasPermission={businessPermissions.canManageUsers} 
              />
              <PermissionCheck 
                label="Invite Users" 
                hasPermission={businessPermissions.canInviteUsers} 
              />
              <PermissionCheck 
                label="Remove Users" 
                hasPermission={businessPermissions.canRemoveUsers} 
              />
            </div>
          </div>

          {/* Finance */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900 flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Finance
            </h4>
            <div className="space-y-1 text-sm">
              <PermissionCheck 
                label="View Finance" 
                hasPermission={businessPermissions.canViewFinance} 
              />
              <PermissionCheck 
                label="Manage Finance" 
                hasPermission={businessPermissions.canManageFinance} 
              />
              <PermissionCheck 
                label="Create Invoices" 
                hasPermission={businessPermissions.canCreateInvoices} 
              />
              <PermissionCheck 
                label="Manage Payments" 
                hasPermission={businessPermissions.canManagePayments} 
              />
            </div>
          </div>

          {/* Settings */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900 flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </h4>
            <div className="space-y-1 text-sm">
              <PermissionCheck 
                label="View Settings" 
                hasPermission={businessPermissions.canViewSettings} 
              />
              <PermissionCheck 
                label="Manage Settings" 
                hasPermission={businessPermissions.canManageSettings} 
              />
              <PermissionCheck 
                label="Manage Business" 
                hasPermission={businessPermissions.canManageBusiness} 
              />
            </div>
          </div>

          {/* Orders & Inventory */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900">Orders & Inventory</h4>
            <div className="space-y-1 text-sm">
              <PermissionCheck 
                label="View Orders" 
                hasPermission={businessPermissions.canViewOrders} 
              />
              <PermissionCheck 
                label="Manage Orders" 
                hasPermission={businessPermissions.canManageOrders} 
              />
              <PermissionCheck 
                label="View Inventory" 
                hasPermission={businessPermissions.canViewInventory} 
              />
              <PermissionCheck 
                label="Manage Inventory" 
                hasPermission={businessPermissions.canManageInventory} 
              />
            </div>
          </div>
        </div>
      </div>

      {/* Raw Data */}
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Raw Context Data</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Roles</h4>
            <div className="space-y-1">
              {roles.map((role, index) => (
                <Badge key={index} variant="info" className="mr-2">
                  {role}
                </Badge>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Permissions</h4>
            <div className="max-h-32 overflow-y-auto">
              <div className="space-y-1">
                {permissions.slice(0, 10).map((permission, index) => (
                  <div key={index} className="text-xs text-gray-600 font-mono">
                    {permission}
                  </div>
                ))}
                {permissions.length > 10 && (
                  <div className="text-xs text-gray-500 italic">
                    +{permissions.length - 10} more...
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function PermissionCheck({ label, hasPermission }: { label: string; hasPermission: boolean }) {
  return (
    <div className="flex items-center justify-between">
      <span className="text-gray-600">{label}</span>
      {hasPermission ? (
        <CheckCircle className="h-4 w-4 text-green-500" />
      ) : (
        <XCircle className="h-4 w-4 text-red-400" />
      )}
    </div>
  )
}
