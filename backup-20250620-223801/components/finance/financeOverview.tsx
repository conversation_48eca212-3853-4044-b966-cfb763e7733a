'use client'

import { useTranslations } from 'next-intl'

interface FinanceOverviewProps {
  businessId: string
}

export function FinanceOverview({ businessId }: FinanceOverviewProps) {
  const t = useTranslations('business.finance')
  
  return (
    <div className="space-y-6">
      {/* Financial Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('totalRevenue')}</h3>
          <div className="text-2xl font-bold">$45,231.89</div>
          <p className="text-xs text-muted-foreground">+20.1% from last month</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('outstandingInvoices')}</h3>
          <div className="text-2xl font-bold">$12,450</div>
          <p className="text-xs text-muted-foreground">15 invoices pending</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('expenses')}</h3>
          <div className="text-2xl font-bold">$8,230</div>
          <p className="text-xs text-muted-foreground">This month</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('profit')}</h3>
          <div className="text-2xl font-bold">$37,001</div>
          <p className="text-xs text-muted-foreground">+15.2% from last month</p>
        </div>
      </div>

      {/* Recent Invoices */}
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <div className="p-6">
          <h3 className="text-lg font-medium mb-4">{t('recentInvoices')}</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <p className="font-medium">INV-001234</p>
                <p className="text-sm text-muted-foreground">John Doe • Due: Mar 20, 2024</p>
              </div>
              <div className="text-right">
                <p className="font-medium">$1,299.99</p>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  Pending
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <p className="font-medium">INV-001233</p>
                <p className="text-sm text-muted-foreground">Jane Smith • Due: Mar 18, 2024</p>
              </div>
              <div className="text-right">
                <p className="font-medium">$849.99</p>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Paid
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <p className="font-medium">INV-001232</p>
                <p className="text-sm text-muted-foreground">Bob Johnson • Due: Mar 15, 2024</p>
              </div>
              <div className="text-right">
                <p className="font-medium">$2,199.99</p>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  Overdue
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
