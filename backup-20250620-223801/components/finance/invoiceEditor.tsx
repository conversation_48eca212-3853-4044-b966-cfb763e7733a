'use client'

import { useTranslations } from 'next-intl'

interface InvoiceEditorProps {
  businessId: string
  invoiceId: string
}

export function InvoiceEditor({ businessId, invoiceId }: InvoiceEditorProps) {
  const t = useTranslations('business.finance.invoice')
  
  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
            <h3 className="text-lg font-medium mb-4">{t('invoiceDetails')}</h3>
            
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium mb-2">{t('invoiceNumber')}</label>
                  <input 
                    type="text" 
                    value="INV-001234" 
                    className="w-full p-2 border rounded-md"
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">{t('dueDate')}</label>
                  <input 
                    type="date" 
                    className="w-full p-2 border rounded-md"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">{t('customer')}</label>
                <select className="w-full p-2 border rounded-md">
                  <option>John Doe</option>
                  <option>Jane Smith</option>
                  <option>Bob Johnson</option>
                </select>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">{t('lineItems')}</h4>
                <div className="space-y-2">
                  <div className="grid gap-2 md:grid-cols-4 p-2 border rounded">
                    <input placeholder="Description" className="p-2 border rounded" />
                    <input placeholder="Quantity" type="number" className="p-2 border rounded" />
                    <input placeholder="Rate" type="number" className="p-2 border rounded" />
                    <div className="p-2 text-right font-medium">$299.99</div>
                  </div>
                  <div className="grid gap-2 md:grid-cols-4 p-2 border rounded">
                    <input placeholder="Description" className="p-2 border rounded" />
                    <input placeholder="Quantity" type="number" className="p-2 border rounded" />
                    <input placeholder="Rate" type="number" className="p-2 border rounded" />
                    <div className="p-2 text-right font-medium">$149.99</div>
                  </div>
                </div>
                <button className="mt-2 text-sm text-blue-600 hover:text-blue-800">
                  + Add Line Item
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div>
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
            <h3 className="font-medium mb-4">{t('invoiceSummary')}</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>$449.98</span>
              </div>
              <div className="flex justify-between">
                <span>Tax (8.5%):</span>
                <span>$38.25</span>
              </div>
              <div className="flex justify-between border-t pt-2 font-medium">
                <span>Total:</span>
                <span>$488.23</span>
              </div>
            </div>
            
            <div className="mt-6">
              <h4 className="font-medium mb-2">{t('status')}</h4>
              <select className="w-full p-2 border rounded-md">
                <option>Draft</option>
                <option>Sent</option>
                <option>Paid</option>
                <option>Overdue</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
