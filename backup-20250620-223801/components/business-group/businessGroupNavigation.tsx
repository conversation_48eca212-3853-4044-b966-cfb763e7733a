'use client'

import { useTranslations } from 'next-intl'
import { Link } from '@/i18n/navigation'
import { usePathname } from 'next/navigation'
import { 
  LayoutDashboard,
  Building2,
  Users,
  Settings,
  BarChart3,
  CreditCard,
  ArrowLeft
} from 'lucide-react'

const iconMap = {
  LayoutDashboard,
  Building2,
  Users,
  Settings,
  BarChart3,
  CreditCard,
  ArrowLeft
}

interface NavigationItem {
  name: string
  href: string
  icon: keyof typeof iconMap
  description?: string
}

export function BusinessGroupNavigation() {
  const t = useTranslations('navigation')
  const pathname = usePathname()

  const navigation: NavigationItem[] = [
    { 
      name: 'Group Overview', 
      href: '/business-group', 
      icon: 'LayoutDashboard',
      description: 'Business group dashboard and metrics'
    },
    { 
      name: 'Businesses', 
      href: '/business-group/businesses', 
      icon: 'Building2',
      description: 'Manage businesses in your group'
    },
    { 
      name: 'Members', 
      href: '/business-group/members', 
      icon: 'Users',
      description: 'Group member management'
    },
    { 
      name: 'Analytics', 
      href: '/business-group/analytics', 
      icon: 'BarChart3',
      description: 'Cross-business insights'
    },
    { 
      name: 'Billing', 
      href: '/business-group/billing', 
      icon: 'CreditCard',
      description: 'Group billing and subscriptions'
    },
    { 
      name: 'Settings', 
      href: '/business-group/settings', 
      icon: 'Settings',
      description: 'Group configuration'
    },
  ]

  return (
    <div className="bg-white border-r border-gray-200 w-64 min-h-screen">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <Link href="/dashboard" className="flex items-center gap-2 text-sm text-gray-500 hover:text-gray-700 mb-3">
          <ArrowLeft className="h-4 w-4" />
          Back to Dashboard
        </Link>
        
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded bg-purple-500 flex items-center justify-center">
            <Building2 className="h-5 w-5 text-white" />
          </div>
          <div>
            <h2 className="font-semibold text-gray-900">Business Group</h2>
            <p className="text-sm text-gray-500">Group Management</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-3 py-4">
        {navigation.map((item) => {
          const IconComponent = iconMap[item.icon]
          const isActive = pathname === item.href
          
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                isActive
                  ? 'bg-purple-100 text-purple-900'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
              title={item.description}
            >
              <IconComponent className="mr-3 h-5 w-5 flex-shrink-0" />
              {item.name}
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="text-xs text-gray-500">
          <p>Business Group</p>
          <p className="mt-1">Multi-business management</p>
        </div>
      </div>
    </div>
  )
}
