'use client'

import { useTranslations } from 'next-intl'

interface BusinessSettingsProps {
  businessId: string
}

export function BusinessSettings({ businessId }: BusinessSettingsProps) {
  const t = useTranslations('business.settings')
  
  return (
    <div className="space-y-6">
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h3 className="text-lg font-medium mb-4">{t('businessInfo')}</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Business Name</label>
            <input type="text" className="w-full p-2 border rounded-md" placeholder="Enter business name" />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Industry</label>
            <select className="w-full p-2 border rounded-md">
              <option>Technology</option>
              <option>Retail</option>
              <option>Healthcare</option>
              <option>Finance</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Business Address</label>
            <textarea className="w-full p-2 border rounded-md" rows={3} placeholder="Enter business address"></textarea>
          </div>
        </div>
      </div>
    </div>
  )
}
