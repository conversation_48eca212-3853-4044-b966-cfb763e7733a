'use client'

import { useTranslations } from 'next-intl'

interface CrmOverviewProps {
  businessId: string
}

export function CrmOverview({ businessId }: CrmOverviewProps) {
  const t = useTranslations('business.crm')
  
  return (
    <div className="space-y-6">
      {/* CRM Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('totalContacts')}</h3>
          <div className="text-2xl font-bold">2,350</div>
          <p className="text-xs text-muted-foreground">+180 this month</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('activeLeads')}</h3>
          <div className="text-2xl font-bold">145</div>
          <p className="text-xs text-muted-foreground">23 hot leads</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('conversionRate')}</h3>
          <div className="text-2xl font-bold">12.5%</div>
          <p className="text-xs text-muted-foreground">+2.1% from last month</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('avgDealSize')}</h3>
          <div className="text-2xl font-bold">$1,250</div>
          <p className="text-xs text-muted-foreground">+$150 from last month</p>
        </div>
      </div>

      {/* Recent Contacts */}
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <div className="p-6">
          <h3 className="text-lg font-medium mb-4">{t('recentContacts')}</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-4">
                <div className="h-10 w-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
                  JD
                </div>
                <div>
                  <p className="font-medium">John Doe</p>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              <div className="text-right">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Customer
                </span>
                <p className="text-sm text-muted-foreground mt-1">Last contact: 2 days ago</p>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-4">
                <div className="h-10 w-10 bg-purple-500 rounded-full flex items-center justify-center text-white font-medium">
                  JS
                </div>
                <div>
                  <p className="font-medium">Jane Smith</p>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              <div className="text-right">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  Lead
                </span>
                <p className="text-sm text-muted-foreground mt-1">Last contact: 1 week ago</p>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-4">
                <div className="h-10 w-10 bg-green-500 rounded-full flex items-center justify-center text-white font-medium">
                  BJ
                </div>
                <div>
                  <p className="font-medium">Bob Johnson</p>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              <div className="text-right">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  Hot Lead
                </span>
                <p className="text-sm text-muted-foreground mt-1">Last contact: Today</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
