'use client'

import { useTranslations } from 'next-intl'

interface CustomerDetailsProps {
  businessId: string
  customerId: string
}

export function CustomerDetails({ businessId, customerId }: CustomerDetailsProps) {
  const t = useTranslations('business.crm.customer')
  
  return (
    <div className="space-y-6">
      {/* Customer Info */}
      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
            <div className="flex items-center gap-4 mb-6">
              <div className="h-16 w-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-xl font-medium">
                JD
              </div>
              <div>
                <h2 className="text-2xl font-bold">John Doe</h2>
                <p className="text-muted-foreground">Premium Customer</p>
              </div>
            </div>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h3 className="font-medium mb-2">{t('contactInfo')}</h3>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Email:</span> <EMAIL></p>
                  <p><span className="font-medium">Phone:</span> +****************</p>
                  <p><span className="font-medium">Address:</span> 123 Main St, City, State 12345</p>
                </div>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">{t('customerStats')}</h3>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Total Orders:</span> 24</p>
                  <p><span className="font-medium">Total Spent:</span> $2,450.00</p>
                  <p><span className="font-medium">Avg Order:</span> $102.08</p>
                  <p><span className="font-medium">Last Order:</span> 2 days ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div>
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
            <h3 className="font-medium mb-4">{t('quickActions')}</h3>
            <div className="space-y-2">
              <button className="w-full text-left p-2 rounded hover:bg-gray-50 text-sm">
                Send Email
              </button>
              <button className="w-full text-left p-2 rounded hover:bg-gray-50 text-sm">
                Schedule Call
              </button>
              <button className="w-full text-left p-2 rounded hover:bg-gray-50 text-sm">
                Create Order
              </button>
              <button className="w-full text-left p-2 rounded hover:bg-gray-50 text-sm">
                Add Note
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Order History */}
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <div className="p-6">
          <h3 className="text-lg font-medium mb-4">{t('orderHistory')}</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <p className="font-medium">#ORD-001234</p>
                <p className="text-sm text-muted-foreground">March 15, 2024</p>
              </div>
              <div className="text-right">
                <p className="font-medium">$299.99</p>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Delivered
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <p className="font-medium">#ORD-001198</p>
                <p className="text-sm text-muted-foreground">February 28, 2024</p>
              </div>
              <div className="text-right">
                <p className="font-medium">$149.99</p>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Delivered
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
