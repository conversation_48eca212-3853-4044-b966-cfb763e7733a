'use client'

import { cn } from '@/lib/utils'

interface VentureLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'full' | 'icon' | 'text'
  className?: string
}

export function VentureLogo({ 
  size = 'md', 
  variant = 'full',
  className 
}: VentureLogoProps) {
  const sizeClasses = {
    sm: 'h-6',
    md: 'h-8',
    lg: 'h-12',
    xl: 'h-16'
  }

  const iconSizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  }

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-3xl',
    xl: 'text-4xl'
  }

  if (variant === 'icon') {
    return (
      <div className={cn(
        'rounded-lg bg-venture-500 flex items-center justify-center shadow-sm',
        iconSizeClasses[size],
        className
      )}>
        <svg
          viewBox="0 0 24 24"
          fill="none"
          className="h-3/5 w-3/5 text-white"
        >
          <path
            d="M3 3h18v18H3V3z"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M9 9h6v6H9V9z"
            fill="currentColor"
          />
          <path
            d="M7 7v10M17 7v10M7 7h10M7 17h10"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
          />
        </svg>
      </div>
    )
  }

  if (variant === 'text') {
    return (
      <span className={cn(
        'font-bold text-venture-600',
        textSizeClasses[size],
        className
      )}>
        VentureDirection
      </span>
    )
  }

  return (
    <div className={cn('flex items-center gap-3', className)}>
      <div className={cn(
        'rounded-lg bg-venture-500 flex items-center justify-center shadow-sm',
        iconSizeClasses[size]
      )}>
        <svg
          viewBox="0 0 24 24"
          fill="none"
          className="h-3/5 w-3/5 text-white"
        >
          <path
            d="M3 3h18v18H3V3z"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M9 9h6v6H9V9z"
            fill="currentColor"
          />
          <path
            d="M7 7v10M17 7v10M7 7h10M7 17h10"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
          />
        </svg>
      </div>
      <span className={cn(
        'font-bold text-venture-600',
        textSizeClasses[size]
      )}>
        VentureDirection
      </span>
    </div>
  )
}

// Simplified logo for headers and navigation
export function VentureLogoSimple({ className }: { className?: string }) {
  return (
    <VentureLogo 
      size="sm" 
      variant="full" 
      className={className}
    />
  )
}

// Icon-only logo for compact spaces
export function VentureIcon({ size = 'md', className }: { size?: 'sm' | 'md' | 'lg' | 'xl', className?: string }) {
  return (
    <VentureLogo 
      size={size} 
      variant="icon" 
      className={className}
    />
  )
}

// Text-only logo for minimal designs
export function VentureText({ size = 'md', className }: { size?: 'sm' | 'md' | 'lg' | 'xl', className?: string }) {
  return (
    <VentureLogo 
      size={size} 
      variant="text" 
      className={className}
    />
  }
