'use client'

import { cn } from '@/lib/utils'
import { cva, type VariantProps } from 'class-variance-authority'

const badgeVariants = cva(
  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-venture-500 focus:ring-offset-2',
  {
    variants: {
      variant: {
        default: 'bg-venture-100 text-venture-800 hover:bg-venture-200',
        secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
        success: 'bg-success-100 text-success-800 hover:bg-success-200',
        warning: 'bg-warning-100 text-warning-800 hover:bg-warning-200',
        error: 'bg-error-100 text-error-800 hover:bg-error-200',
        info: 'bg-venture-100 text-venture-800 hover:bg-venture-200',
        outline: 'border border-venture-200 text-venture-600 hover:bg-venture-50',
        solid: 'bg-venture-500 text-white hover:bg-venture-600',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

export function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}
