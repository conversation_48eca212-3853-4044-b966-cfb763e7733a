'use client'

import { ventureColors } from '@/lib/design-system/colors'

export function ColorPalette() {
  return (
    <div className="p-6 space-y-8">
      <div>
        <h2 className="text-2xl font-bold text-venture-600 mb-4">VentureDirection Color Palette</h2>
        <p className="text-gray-600 mb-6">Blue and white color scheme for professional business management</p>
      </div>

      {/* Primary Blue Palette */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Primary Blue Palette</h3>
        <div className="grid grid-cols-5 md:grid-cols-10 gap-2">
          {Object.entries(ventureColors.primary).map(([shade, color]) => (
            <div key={shade} className="text-center">
              <div 
                className="h-16 w-full rounded-lg shadow-sm border border-gray-200 mb-2"
                style={{ backgroundColor: color }}
              />
              <div className="text-xs font-mono text-gray-600">{shade}</div>
              <div className="text-xs font-mono text-gray-500">{color}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Neutral Grays */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Neutral Grays</h3>
        <div className="grid grid-cols-5 md:grid-cols-10 gap-2">
          {Object.entries(ventureColors.neutral).map(([shade, color]) => (
            <div key={shade} className="text-center">
              <div 
                className="h-16 w-full rounded-lg shadow-sm border border-gray-200 mb-2"
                style={{ backgroundColor: color }}
              />
              <div className="text-xs font-mono text-gray-600">{shade}</div>
              <div className="text-xs font-mono text-gray-500">{color}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Semantic Colors */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Semantic Colors</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {/* Success */}
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Success</h4>
            <div className="space-y-2">
              {Object.entries(ventureColors.success).map(([shade, color]) => (
                <div key={shade} className="flex items-center gap-2">
                  <div 
                    className="h-8 w-8 rounded shadow-sm border border-gray-200"
                    style={{ backgroundColor: color }}
                  />
                  <div className="text-xs font-mono text-gray-600">{shade}: {color}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Warning */}
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Warning</h4>
            <div className="space-y-2">
              {Object.entries(ventureColors.warning).map(([shade, color]) => (
                <div key={shade} className="flex items-center gap-2">
                  <div 
                    className="h-8 w-8 rounded shadow-sm border border-gray-200"
                    style={{ backgroundColor: color }}
                  />
                  <div className="text-xs font-mono text-gray-600">{shade}: {color}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Error */}
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Error</h4>
            <div className="space-y-2">
              {Object.entries(ventureColors.error).map(([shade, color]) => (
                <div key={shade} className="flex items-center gap-2">
                  <div 
                    className="h-8 w-8 rounded shadow-sm border border-gray-200"
                    style={{ backgroundColor: color }}
                  />
                  <div className="text-xs font-mono text-gray-600">{shade}: {color}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Info */}
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Info</h4>
            <div className="space-y-2">
              {Object.entries(ventureColors.info).map(([shade, color]) => (
                <div key={shade} className="flex items-center gap-2">
                  <div 
                    className="h-8 w-8 rounded shadow-sm border border-gray-200"
                    style={{ backgroundColor: color }}
                  />
                  <div className="text-xs font-mono text-gray-600">{shade}: {color}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Usage Examples */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Usage Examples</h3>
        <div className="space-y-4">
          {/* Buttons */}
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Buttons</h4>
            <div className="flex flex-wrap gap-2">
              <button className="px-4 py-2 bg-venture-500 text-white rounded-lg hover:bg-venture-600 transition-colors">
                Primary
              </button>
              <button className="px-4 py-2 border border-venture-200 text-venture-600 rounded-lg hover:bg-venture-50 transition-colors">
                Secondary
              </button>
              <button className="px-4 py-2 text-venture-600 hover:bg-venture-50 rounded-lg transition-colors">
                Ghost
              </button>
              <button className="px-4 py-2 bg-success-500 text-white rounded-lg hover:bg-success-600 transition-colors">
                Success
              </button>
              <button className="px-4 py-2 bg-error-500 text-white rounded-lg hover:bg-error-600 transition-colors">
                Error
              </button>
            </div>
          </div>

          {/* Cards */}
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Cards</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                <h5 className="font-medium text-gray-900 mb-2">Default Card</h5>
                <p className="text-gray-600 text-sm">White background with subtle border</p>
              </div>
              <div className="bg-venture-50 border border-venture-200 rounded-lg p-4">
                <h5 className="font-medium text-venture-900 mb-2">Primary Card</h5>
                <p className="text-venture-700 text-sm">Light blue background</p>
              </div>
              <div className="bg-venture-500 text-white rounded-lg p-4 shadow-md">
                <h5 className="font-medium mb-2">Accent Card</h5>
                <p className="text-venture-100 text-sm">Primary blue background</p>
              </div>
            </div>
          </div>

          {/* Badges */}
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Badges</h4>
            <div className="flex flex-wrap gap-2">
              <span className="px-2 py-1 bg-venture-100 text-venture-800 rounded-full text-xs font-medium">
                Primary
              </span>
              <span className="px-2 py-1 bg-success-100 text-success-800 rounded-full text-xs font-medium">
                Success
              </span>
              <span className="px-2 py-1 bg-warning-100 text-warning-800 rounded-full text-xs font-medium">
                Warning
              </span>
              <span className="px-2 py-1 bg-error-100 text-error-800 rounded-full text-xs font-medium">
                Error
              </span>
              <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-medium">
                Neutral
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
