'use client'

import { useTranslations } from 'next-intl'

export function DashboardOverview() {
  const t = useTranslations('dashboard')
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <div className="flex flex-row items-center justify-between space-y-0 pb-2">
            <h3 className="tracking-tight text-sm font-medium">{t('totalBusinesses')}</h3>
          </div>
          <div className="text-2xl font-bold">0</div>
          <p className="text-xs text-muted-foreground">
            No businesses registered yet
          </p>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <div className="flex flex-row items-center justify-between space-y-0 pb-2">
            <h3 className="tracking-tight text-sm font-medium">{t('pendingCompliance')}</h3>
          </div>
          <div className="text-2xl font-bold">0</div>
          <p className="text-xs text-muted-foreground">
            No pending compliance items
          </p>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <div className="flex flex-row items-center justify-between space-y-0 pb-2">
            <h3 className="tracking-tight text-sm font-medium">{t('totalDocuments')}</h3>
          </div>
          <div className="text-2xl font-bold">0</div>
          <p className="text-xs text-muted-foreground">
            No documents uploaded
          </p>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <div className="flex flex-row items-center justify-between space-y-0 pb-2">
            <h3 className="tracking-tight text-sm font-medium">Active Users</h3>
          </div>
          <div className="text-2xl font-bold">1</div>
          <p className="text-xs text-muted-foreground">
            You are the first user!
          </p>
        </div>
      </div>

      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h3 className="text-lg font-medium mb-4">{t('overview')}</h3>
        <p className="text-muted-foreground mb-4">
          {t('description')}
        </p>
        <div className="space-y-2">
          <p className="text-sm">
            <strong>Next steps:</strong>
          </p>
          <ul className="text-sm text-muted-foreground space-y-1 ml-4">
            <li>• Add your first business</li>
            <li>• Set up compliance templates</li>
            <li>• Upload important documents</li>
            <li>• Configure notification preferences</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
