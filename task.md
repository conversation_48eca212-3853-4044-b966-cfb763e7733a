# 🧩 VentureDirection Feature Task Descriptions with Subtasks, Priority & Dependencies

> This document outlines the development tasks per module, including subtasks, execution order, priorities, and dependency mapping. Use for sprint planning and task coordination.

---

### 1. Sales & Store (Assigned to: Alice)
**Priority:** 🔴 High — Foundation for commerce.  
**Depends on:** Integrations (for payment), Inventory (for stock deduction), CRM (customer linkage)

**Main Goal:** Enable storefront and product-based commerce with order management.

**Subtasks (Order of Dev):**
1. [ ] Create product schema and DB model
2. [ ] Build product creation and edit UI (with form validation)
3. [ ] Display product list with filters (active/inactive)
4. [ ] Implement storefront preview page (client-facing)
5. [ ] Create shareable payment links for each product
6. [ ] Design order dashboard (status, totals)
7. [ ] Link orders to products and (optionally) customers
8. [ ] Enable basic analytics for product performance

---

### 2. Finance (Assigned to: Ben)
**Priority:** 🔴 High — Required for monetization and analytics.  
**Depends on:** Sales (orders), Integrations (for payment data), Analytics

**Main Goal:** Manage invoices, expenses, and financial reporting.

**Subtasks (Order of Dev):**
1. [ ] Design invoice creation form and Zod schema
2. [ ] Build recurring invoice logic (cron/trigger)
3. [ ] Create expense tracker with category tags
4. [ ] Add invoice PDF export and print capability
5. [ ] Implement revenue/expense dashboard graph
6. [ ] Add tax summary view with date filters
7. [ ] Add export (CSV, PDF) for reports

---

### 3. CRM (Assigned to: Chloe)
**Priority:** 🟡 Medium — Supports retention, future marketing.  
**Depends on:** Sales (order history), Marketing (campaigns), Notes (customer logs)

**Main Goal:** Build customer tracking and engagement system.

**Subtasks (Order of Dev):**
1. [ ] Create customer model and UI form
2. [ ] Add CRM list with search and tag filter
3. [ ] Allow custom tags and lead sources
4. [ ] Implement customer view with timeline/logs
5. [ ] Add follow-up reminder UI and scheduling logic
6. [ ] Track activity: order count, last contact, notes

---

### 4. Planner & Notes (Assigned to: Daniel)
**Priority:** 🟡 Medium — Supports founders, strategy, and daily work.  
**Depends on:** HR (for task assignment), CRM (for notes), Compliance (for deadlines)

**Main Goal:** Support task planning, notes, and execution tracking.

**Subtasks (Order of Dev):**
1. [ ] Create Task model and schema
2. [ ] Build Kanban board UI for tasks
3. [ ] Add task assignment and status update logic
4. [ ] Enable due dates and overdue alerts
5. [ ] Create general-purpose note component with tags
6. [ ] Enable file attachment in notes (PDFs, images)

---

### 5. Compliance (Assigned to: Esther)
**Priority:** 🔴 High — Core differentiator feature.  
**Depends on:** Document Vault (for uploads), CRM (for business info), Country templates

**Main Goal:** Allow users to track and fulfill legal compliance.

**Subtasks (Order of Dev):**
1. [ ] Fetch active compliance templates by country
2. [ ] Render compliance checklist form dynamically
3. [ ] Save response JSON to compliance object
4. [ ] Implement progress tracking (with due dates)
5. [ ] Enable document upload per rule if required
6. [ ] Display compliance alert card in dashboard

---

### 6. HR & Roles (Assigned to: Felix)
**Priority:** 🟡 Medium — Needed for business scaling.  
**Depends on:** Multi-Business (for scope assignment), Document Vault (for contracts)

**Main Goal:** Manage employee info and access control.

**Subtasks (Order of Dev):**
1. [ ] Create employee form and schema
2. [ ] Build employee list and search UI
3. [ ] View employee detail page (with role, docs)
4. [ ] Link `RoleAssignment` to business scopes
5. [ ] Enable user role editing + revoking
6. [ ] Add optional leave calendar / log

---

### 7. Marketing & Campaigns (Assigned to: Grace)
**Priority:** 🟢 Low — Enhances business reach (post-MVP).  
**Depends on:** CRM (audience), Integrations (WhatsApp, Email), Analytics (tracking)

**Main Goal:** Empower businesses to run and measure marketing.

**Subtasks (Order of Dev):**
1. [ ] Build campaign creation form (email/WhatsApp)
2. [ ] Allow custom message editor with preview
3. [ ] Implement scheduler for campaign send time
4. [ ] Connect WhatsApp / Email API integrations
5. [ ] Build coupon form with limit/date rules
6. [ ] Show delivery stats (sent, opened, failed)

---

### 8. Analytics (Assigned to: Henry)
**Priority:** 🔴 High — Needed for visibility and business health.  
**Depends on:** Sales, Finance, CRM, Integrations, Mongo Log

**Main Goal:** Provide insights into business activity.

**Subtasks (Order of Dev):**
1. [ ] Setup base KPI tiles (revenue, customers, orders)
2. [ ] Build time-series charts for sales & expenses
3. [ ] Add filters: range (7d/30d), category, businessId
4. [ ] Use Mongo `Log` model to generate login charts
5. [ ] Add trend deltas (↑/↓ from previous period)
6. [ ] Allow export to CSV

---

### 9. Integrations & API (Assigned to: Ife)
**Priority:** 🔴 High — Required for external system connection.  
**Blocks:** Sales, Finance, Marketing, Analytics (needs integration tokens and endpoints)

**Main Goal:** Allow platform and third-party integration.

**Subtasks (Order of Dev):**
1. [ ] Create integration management page
2. [ ] List supported platforms (Stripe, Paystack, Meta)
3. [ ] Store and edit integration config (JSON)
4. [ ] Build API token issue and revoke interface
5. [ ] Show API access logs filtered by business/user
6. [ ] List webhook events from `IntegrationLog`
7. [ ] Enable manual re-sync + webhook test

---

### 10. Multi-Business & Franchise (Unassigned)
**Priority:** 🔴 High — Needed for enterprise and B2B use cases.  
**Blocks:** HR, CRM, Finance, Settings

**Main Goal:** Allow one user to manage multiple businesses or branches.

**Subtasks (Order of Dev):**
1. [ ] Create business group model (parent/subsidiary)
2. [ ] Build switcher UI across businesses
3. [ ] Implement shared analytics across all linked businesses
4. [ ] Assign scopes and roles per business context
5. [ ] Handle team assignment to multiple businesses
6. [ ] Sync settings inheritance (logo, timezone, language)

---

### 11. Document Vault (Unassigned)
**Priority:** 🟡 Medium — Required for compliance and storage.  
**Blocks:** Compliance, HR

**Main Goal:** Store and manage business-critical files securely.

**Subtasks (Order of Dev):**
1. [ ] Create Document and SecureVault upload UI
2. [ ] Enable file metadata input (title, category, tag)
3. [ ] Link uploaded docs to compliance or HR modules
4. [ ] Build Vault view for credentials and sensitive info
5. [ ] Implement file versioning and access control (optional)
6. [ ] Support previews (PDF/image) and downloads

---

### 12. Inventory & Procurement (Unassigned)
**Priority:** 🟡 Medium — Needed for physical product sellers.  
**Depends on:** Sales (product), Finance (purchases), Analytics

**Main Goal:** Track items, suppliers, stock movements.

**Subtasks (Order of Dev):**
1. [ ] Create product and supplier models
2. [ ] Add incoming stock log (with quantity, date)
3. [ ] Enable low stock alert thresholds
4. [ ] Build procurement tracker with restock reminders
5. [ ] Connect orders to stock deduction logic
6. [ ] Generate inventory performance reports

