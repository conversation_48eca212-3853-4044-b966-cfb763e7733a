#!/bin/bash

# Script to add missing business models to the database
# Run this from the project root directory

echo "🗄️ Adding missing business models to database..."

# Check if Prisma is available
if ! command -v npx prisma &> /dev/null; then
    echo "❌ Prisma CLI not found. Please install dependencies first:"
    echo "   npm install"
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create it with DATABASE_URL"
    exit 1
fi

# Check if DATABASE_URL is set
if ! grep -q "DATABASE_URL" .env; then
    echo "❌ DATABASE_URL not found in .env file"
    exit 1
fi

echo "📋 Models being added:"
echo "  ✅ Store & Products:"
echo "     - Product (with variants, pricing, inventory)"
echo "     - ProductVariant (size, color, etc.)"
echo ""
echo "  ✅ Orders & Sales:"
echo "     - Order (with customer, payment, shipping)"
echo "     - OrderItem (order line items)"
echo ""
echo "  ✅ Finance & Payments:"
echo "     - Payment (Stripe, PayPal, etc.)"
echo "     - Invoice (with items and status)"
echo "     - InvoiceItem (invoice line items)"
echo ""
echo "  ✅ CRM & Customers:"
echo "     - Customer (with contact details)"
echo "     - CustomerInteraction (calls, emails, meetings)"
echo "     - Lead (sales pipeline)"
echo ""
echo "  ✅ Marketing & Analytics:"
echo "     - Campaign (email, social, ads)"
echo "     - AnalyticsEvent (tracking events)"
echo "     - BusinessMetric (KPIs and metrics)"
echo ""
echo "  ✅ Logistics & Inventory:"
echo "     - InventoryMovement (stock tracking)"
echo "     - Supplier (vendor management)"
echo "     - PurchaseOrder (procurement)"
echo "     - PurchaseOrderItem (PO line items)"
echo ""

# Confirm before proceeding
read -p "🤔 Do you want to proceed with the migration? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Migration cancelled"
    exit 1
fi

echo "🔄 Generating Prisma migration..."

# Generate migration
npx prisma migrate dev --name "add-business-models" --create-only

if [ $? -ne 0 ]; then
    echo "❌ Failed to generate migration"
    exit 1
fi

echo "✅ Migration generated successfully!"
echo ""
echo "📝 Migration includes:"
echo "  - All business feature models"
echo "  - Proper foreign key relationships"
echo "  - Optimized database indexes"
echo "  - Enum types for status fields"
echo ""

# Ask if user wants to apply the migration
read -p "🚀 Apply the migration to database now? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🔄 Applying migration to database..."
    
    npx prisma migrate deploy
    
    if [ $? -eq 0 ]; then
        echo "✅ Migration applied successfully!"
        
        # Generate Prisma client
        echo "🔄 Generating Prisma client..."
        npx prisma generate
        
        if [ $? -eq 0 ]; then
            echo "✅ Prisma client generated successfully!"
            echo ""
            echo "🎉 All business models are now available!"
            echo ""
            echo "📋 Next steps:"
            echo "1. Restart your development server"
            echo "2. Import the new models in your code:"
            echo "   import { Product, Order, Customer, ... } from '@prisma/client'"
            echo "3. Create API routes for the new models"
            echo "4. Build UI components for each feature"
            echo ""
            echo "🔧 Available models:"
            echo "  - Product, ProductVariant"
            echo "  - Order, OrderItem"
            echo "  - Payment, Invoice, InvoiceItem"
            echo "  - Customer, CustomerInteraction, Lead"
            echo "  - Campaign, AnalyticsEvent, BusinessMetric"
            echo "  - InventoryMovement, Supplier, PurchaseOrder"
        else
            echo "❌ Failed to generate Prisma client"
            exit 1
        fi
    else
        echo "❌ Failed to apply migration"
        exit 1
    fi
else
    echo "⏸️ Migration generated but not applied"
    echo "📝 To apply later, run:"
    echo "   npx prisma migrate deploy"
    echo "   npx prisma generate"
fi

echo ""
echo "🎯 Database schema now supports all business features!"
echo "   /business/store     - Product & inventory management"
echo "   /business/orders    - Order processing & fulfillment"
echo "   /business/finance   - Payments & invoicing"
echo "   /business/crm       - Customer relationship management"
echo "   /business/marketing - Campaigns & analytics"
echo "   /business/logistics - Inventory & supplier management"
