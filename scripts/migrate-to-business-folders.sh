#!/bin/bash

# Migration script to move dashboard routes to business folder structure
# Run this from the project root directory

echo "🚀 Starting migration to business folder structure..."

# Create the new business folder structure
echo "📁 Creating business folder structure..."

# Create main business folder
mkdir -p app/\[locale\]/business

# Create business group folder
mkdir -p app/\[locale\]/business-group

# Move existing dashboard routes to business folder
echo "📦 Moving business feature pages to /business folder..."

# List of pages to move to business folder
BUSINESS_PAGES=(
    "onboard"
    "store" 
    "orders"
    "inventory"
    "crm"
    "finance"
    "compliance"
    "documents"
    "secure-vault"
    "planner"
    "hr"
    "roles"
    "marketing"
    "analytics"
    "integrations"
    "logistics"
    "settings"
)

# Move each business page
for page in "${BUSINESS_PAGES[@]}"; do
    if [ -d "app/[locale]/(dashboard)/$page" ]; then
        echo "  Moving $page..."
        mv "app/[locale]/(dashboard)/$page" "app/[locale]/business/"
    elif [ -d "app/[locale]/$page" ]; then
        echo "  Moving $page from root..."
        mv "app/[locale]/$page" "app/[locale]/business/"
    else
        echo "  ⚠️  $page not found, skipping..."
    fi
done

# Create business group pages
echo "📋 Creating business group pages..."

# Create business group pages
cat > app/\[locale\]/business-group/page.tsx << 'EOF'
'use client'

import { PageHeader } from '@/components/common/pageHeader'
import { useTranslations } from 'next-intl'
import { Building2, Users, BarChart3, Settings } from 'lucide-react'

export default function BusinessGroupPage() {
  const t = useTranslations('businessGroup')
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={
          <div className="flex items-center gap-3">
            <Building2 className="h-6 w-6 text-blue-600" />
            {t('title')}
          </div>
        }
        description={t('description')}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border shadow-sm p-6">
          <div className="flex items-center gap-3 mb-4">
            <Building2 className="h-8 w-8 text-blue-600" />
            <h3 className="text-lg font-semibold">Businesses</h3>
          </div>
          <p className="text-gray-600 mb-4">
            Manage all businesses in your group
          </p>
          <div className="text-2xl font-bold text-blue-600">5</div>
          <div className="text-sm text-gray-500">Active businesses</div>
        </div>
        
        <div className="bg-white rounded-lg border shadow-sm p-6">
          <div className="flex items-center gap-3 mb-4">
            <Users className="h-8 w-8 text-green-600" />
            <h3 className="text-lg font-semibold">Members</h3>
          </div>
          <p className="text-gray-600 mb-4">
            Group members and permissions
          </p>
          <div className="text-2xl font-bold text-green-600">24</div>
          <div className="text-sm text-gray-500">Total members</div>
        </div>
        
        <div className="bg-white rounded-lg border shadow-sm p-6">
          <div className="flex items-center gap-3 mb-4">
            <BarChart3 className="h-8 w-8 text-purple-600" />
            <h3 className="text-lg font-semibold">Analytics</h3>
          </div>
          <p className="text-gray-600 mb-4">
            Cross-business insights
          </p>
          <div className="text-2xl font-bold text-purple-600">$125K</div>
          <div className="text-sm text-gray-500">Total revenue</div>
        </div>
      </div>
    </div>
  )
}
EOF

# Create business group businesses page
mkdir -p app/\[locale\]/business-group/businesses
cat > app/\[locale\]/business-group/businesses/page.tsx << 'EOF'
'use client'

import { PageHeader } from '@/components/common/pageHeader'
import { useTranslations } from 'next-intl'
import { Building2 } from 'lucide-react'

export default function BusinessGroupBusinessesPage() {
  const t = useTranslations('businessGroup.businesses')
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
      />
      
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">Business Management</h3>
        <p className="text-gray-600">
          Manage all businesses within your business group.
        </p>
      </div>
    </div>
  )
}
EOF

# Create business group settings page
mkdir -p app/\[locale\]/business-group/settings
cat > app/\[locale\]/business-group/settings/page.tsx << 'EOF'
'use client'

import { PageHeader } from '@/components/common/pageHeader'
import { useTranslations } from 'next-intl'
import { Settings } from 'lucide-react'

export default function BusinessGroupSettingsPage() {
  const t = useTranslations('businessGroup.settings')
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={t('title')}
        description={t('description')}
      />
      
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">Group Settings</h3>
        <p className="text-gray-600">
          Configure settings for your business group.
        </p>
      </div>
    </div>
  )
}
EOF

# Update main business page (dashboard)
echo "📝 Creating main business dashboard page..."
cat > app/\[locale\]/business/page.tsx << 'EOF'
'use client'

import { PageHeader } from '@/components/common/pageHeader'
import { BusinessDashboard } from '@/components/business/businessDashboard'
import { OnboardingChecklist } from '@/components/onboarding/onboardingChecklist'
import { useTranslations } from 'next-intl'
import { useBusinessUser } from '@/lib/contexts/business-user-context'
import { useOnboarding } from '@/lib/hooks/useOnboarding'

export default function BusinessPage() {
  const t = useTranslations('business.dashboard')
  const { businessName } = useBusinessUser()
  const { onboardingStatus } = useOnboarding()
  
  const showOnboarding = onboardingStatus && !onboardingStatus.isFullyOnboarded
  
  return (
    <div className="space-y-6">
      <PageHeader
        title={`${businessName} Dashboard`}
        description={t('description')}
      />
      
      {/* Show onboarding checklist if not fully onboarded */}
      {showOnboarding && (
        <OnboardingChecklist compact />
      )}
      
      <BusinessDashboard />
    </div>
  )
}
EOF

# Create business layout
echo "🎨 Creating business layout..."
cat > app/\[locale\]/business/layout.tsx << 'EOF'
'use client'

import { BusinessWorkspaceLayout } from '@/components/layouts/businessWorkspaceLayout'
import { useBusiness } from '@/lib/contexts/business-context'

export default function BusinessLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { currentBusiness } = useBusiness()
  
  return (
    <BusinessWorkspaceLayout businessId={currentBusiness?.id}>
      {children}
    </BusinessWorkspaceLayout>
  )
}
EOF

# Create business group layout
echo "🎨 Creating business group layout..."
cat > app/\[locale\]/business-group/layout.tsx << 'EOF'
'use client'

import { ReactNode } from 'react'
import { PageHeader } from '@/components/common/pageHeader'
import { Building2 } from 'lucide-react'

interface BusinessGroupLayoutProps {
  children: ReactNode
}

export default function BusinessGroupLayout({ children }: BusinessGroupLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="p-6">
        {children}
      </div>
    </div>
  )
}
EOF

# Update navigation translations
echo "🌐 Adding navigation translations..."
cat >> messages/en.json << 'EOF'
  "businessGroup": {
    "title": "Business Group",
    "description": "Manage your business group and member businesses",
    "businesses": {
      "title": "Group Businesses",
      "description": "Manage all businesses in your group"
    },
    "settings": {
      "title": "Group Settings", 
      "description": "Configure your business group settings"
    }
  }
EOF

echo "✅ Migration completed!"
echo ""
echo "📋 Next steps:"
echo "1. Update any hardcoded links to use new /business/* paths"
echo "2. Update navigation components to use new routes"
echo "3. Test all business features work with new folder structure"
echo "4. Update any API routes that reference old paths"
echo "5. Run the application and verify everything works"
echo ""
echo "🎯 New route structure:"
echo "  /dashboard          - General dashboard (business selection)"
echo "  /business/*         - All business features (requires business context)"
echo "  /business-group/*   - Business group management"
echo ""
echo "🔧 Middleware automatically protects:"
echo "  /business/*         - Requires activeBusinessId"
echo "  /business-group/*   - Requires activeBusinessGroupId"
EOF

# Make the script executable
chmod +x scripts/migrate-to-business-folders.sh
