#!/bin/bash

echo "🔄 Restarting development server with fresh Tailwind CSS..."

# Kill any existing Next.js processes
echo "🛑 Stopping existing processes..."
pkill -f "next dev" || true

# Clear Next.js cache
echo "🧹 Clearing Next.js cache..."
rm -rf .next

# Clear Tailwind CSS cache if it exists
echo "🎨 Clearing Tailwind cache..."
rm -rf node_modules/.cache

echo "✅ Cache cleared!"
echo "🚀 Starting development server..."
echo "Visit http://localhost:3000/design-system to see the colors!"

# Start development server
yarn dev
