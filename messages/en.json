{"common": {"appName": "VentureDirection", "dashboard": "Dashboard", "businesses": "Businesses", "compliance": "Compliance", "documents": "Documents", "settings": "Settings", "signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "back": "Back", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "sort": "Sort", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "confirm": "Confirm", "yes": "Yes", "no": "No", "close": "Close", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset"}, "navigation": {"home": "Home", "dashboard": "Dashboard", "businesses": "Businesses", "compliance": "Compliance", "documents": "Documents", "settings": "Settings", "profile": "Profile", "help": "Help", "logout": "Logout"}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "phone": "Phone Number", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "createAccount": "Create Account", "welcomeBack": "Welcome Back", "getStarted": "Get Started"}, "dashboard": {"title": "Dashboard", "description": "Welcome to your business management dashboard", "overview": "Overview", "recentActivity": "Recent Activity", "quickActions": "Quick Actions", "statistics": "Statistics", "totalBusinesses": "Total Businesses", "pendingCompliance": "Pending Compliance", "overdueCompliance": "Overdue Compliance", "totalDocuments": "Total Documents"}, "businesses": {"title": "Businesses", "description": "Manage your businesses and their compliance", "addBusiness": "Add Business", "createBusiness": "Create Business", "businessName": "Business Name", "businessType": "Business Type", "country": "Country", "registrationNumber": "Registration Number", "incorporationDate": "Incorporation Date", "registrationStatus": "Registration Status", "systemStatus": "System Status", "businessStatus": "Business Status", "industry": "Industry", "industries": "Industries", "noBusinessesFound": "No businesses found", "businessCreated": "Business created successfully", "businessUpdated": "Business updated successfully", "businessDeleted": "Business deleted successfully", "businessDetails": "Business Details", "businessOverview": "Business Overview", "selectBusiness": "Select a Business", "selectBusinessDescription": "Choose a business to access its workspace and features", "manageBusinesses": "Manage All Businesses"}, "compliance": {"title": "Compliance", "description": "Track and manage regulatory compliance across all businesses", "complianceOverview": "Compliance Overview", "pendingCompliance": "Pending Compliance", "overdueCompliance": "Overdue Compliance", "completedCompliance": "Completed Compliance", "upcomingDeadlines": "Upcoming Deadlines", "complianceTemplate": "Compliance Template", "dueDate": "Due Date", "status": "Status", "penalty": "Penalty", "regulatoryBody": "Regulatory Body", "category": "Category", "frequency": "Frequency", "responses": "Responses", "submitCompliance": "Submit Compliance", "complianceSubmitted": "Compliance submitted successfully", "complianceUpdated": "Compliance updated successfully"}, "documents": {"title": "Documents", "description": "Manage business documents and files", "uploadDocument": "Upload Document", "documentTitle": "Document Title", "documentCategory": "Document Category", "fileSize": "File Size", "uploadDate": "Upload Date", "uploadedBy": "Uploaded By", "noDocumentsFound": "No documents found", "documentUploaded": "Document uploaded successfully", "documentDeleted": "Document deleted successfully", "downloadDocument": "Download Document", "shareDocument": "Share Document"}, "settings": {"title": "Settings", "description": "Manage your account and application preferences", "accountSettings": "Account <PERSON><PERSON>", "profileSettings": "Profile Settings", "notificationSettings": "Notification Settings", "languageSettings": "Language Settings", "timezoneSettings": "Timezone Settings", "securitySettings": "Security Settings", "privacySettings": "Privacy Settings", "language": "Language", "timezone": "Timezone", "theme": "Theme", "notifications": "Notifications", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "settingsUpdated": "Settings updated successfully"}, "errors": {"generic": "Something went wrong. Please try again.", "networkError": "Network error. Please check your connection.", "unauthorized": "You are not authorized to perform this action.", "forbidden": "Access denied.", "notFound": "The requested resource was not found.", "validationError": "Please check your input and try again.", "serverError": "Server error. Please try again later."}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "minLength": "Must be at least {min} characters", "maxLength": "Must be no more than {max} characters", "passwordMismatch": "Passwords do not match"}, "business": {"navigation": {"dashboard": "Dashboard", "store": "Store", "orders": "Orders", "inventory": "Inventory", "crm": "CRM", "finance": "Finance", "compliance": "Compliance", "documents": "Documents", "secureVault": "<PERSON><PERSON>", "planner": "Planner", "hr": "HR", "roles": "Roles", "marketing": "Marketing", "analytics": "Analytics", "integrations": "Integrations", "businessGroups": "Business Groups", "logistics": "Logistics", "settings": "Settings", "profile": "Profile", "backToDashboard": "Back to Dashboard", "businessWorkspace": "Business Workspace"}, "dashboard": {"title": "Business Dashboard", "description": "Overview of your business performance and activities", "totalRevenue": "Total Revenue", "totalOrders": "Total Orders", "activeCustomers": "Active Customers", "pendingTasks": "Pending Tasks", "recentActivity": "Recent Activity", "quickActions": "Quick Actions"}, "store": {"title": "Store Management", "description": "Manage your online store, products, and sales", "totalProducts": "Total Products", "storeViews": "Store Views", "conversionRate": "Conversion Rate", "averageOrderValue": "Average Order Value", "recentProducts": "Recent Products", "viewStore": "View Store", "addProduct": "Add Product"}, "orders": {"title": "Order Management", "description": "Track and manage customer orders and fulfillment", "totalOrders": "Total Orders", "pendingOrders": "Pending Orders", "completedToday": "Completed Today", "totalRevenue": "Total Revenue", "recentOrders": "Recent Orders", "exportOrders": "Export Orders", "createOrder": "Create Order"}, "inventory": {"title": "Inventory Management", "description": "Track stock levels, manage suppliers, and handle procurement", "totalItems": "Total Items", "lowStockItems": "Low Stock Items", "totalValue": "Total Value", "turnoverRate": "Turnover Rate", "lowStockAlerts": "Low Stock Alerts", "lowStock": "Low Stock", "stockAdjustment": "Stock Adjustment", "addItem": "Add Item"}, "crm": {"title": "Customer Relationship Management", "description": "Manage customer relationships, leads, and sales pipeline", "totalContacts": "Total Contacts", "activeLeads": "Active Leads", "conversionRate": "Conversion Rate", "avgDealSize": "Avg <PERSON> Size", "recentContacts": "Recent Contacts", "sendCampaign": "Send Campaign", "importContacts": "Import Contacts", "addContact": "Add Contact", "customer": {"title": "Customer Details", "description": "View and manage customer information", "contactInfo": "Contact Information", "customerStats": "Customer Statistics", "quickActions": "Quick Actions", "orderHistory": "Order History", "backToContacts": "Back to Contacts", "call": "Call", "email": "Email", "edit": "Edit"}}, "finance": {"title": "Financial Management", "description": "Track revenue, expenses, invoicing, and financial reports", "totalRevenue": "Total Revenue", "outstandingInvoices": "Outstanding Invoices", "expenses": "Expenses", "profit": "Profit", "recentInvoices": "Recent Invoices", "exportReports": "Export Reports", "addExpense": "Add Expense", "createInvoice": "Create Invoice", "addTransaction": "Add Transaction", "invoice": {"editTitle": "Edit Invoice", "editDescription": "Modify invoice details and line items", "invoiceDetails": "Invoice Details", "invoiceNumber": "Invoice Number", "dueDate": "Due Date", "customer": "Customer", "lineItems": "Line Items", "invoiceSummary": "Invoice Summary", "status": "Status", "preview": "Preview", "saveDraft": "Save Draft", "sendInvoice": "Send Invoice", "backToFinance": "Back to Finance"}}, "compliance": {"title": "Compliance Management", "description": "Track regulatory requirements and ensure business compliance", "viewOverdue": "View Overdue", "runAudit": "<PERSON>", "addRequirement": "Add Requirement"}, "documents": {"title": "Document Management", "description": "Store, organize, and manage business documents securely", "search": "Search", "createFolder": "Create Folder", "uploadDocument": "Upload Document"}, "secureVault": {"title": "<PERSON><PERSON>", "description": "Securely store passwords, certificates, and sensitive credentials", "overview": "Secure Vault Overview", "securityAudit": "Security Audit", "generatePassword": "Generate Password", "addCredential": "Add Credential"}, "planner": {"title": "Task & Project Planner", "description": "Organize tasks, projects, and team collaboration", "overview": "Planner Overview", "viewCalendar": "View Calendar", "createProject": "Create Project", "addTask": "Add Task"}, "hr": {"title": "Human Resources", "description": "Manage employees, payroll, and HR processes", "overview": "HR Overview", "timeOff": "Time Off", "payroll": "Payroll", "addEmployee": "Add Employee"}, "roles": {"title": "Roles & Permissions", "description": "Manage user access, roles, and security permissions", "overview": "Roles Overview", "permissions": "Permissions", "assignRoles": "Assign Roles", "createRole": "Create Role"}, "marketing": {"title": "Marketing & Campaigns", "description": "Create and manage marketing campaigns and customer outreach", "overview": "Marketing Overview", "analytics": "Analytics", "audience": "Audience", "createCampaign": "Create Campaign"}, "analytics": {"title": "Analytics & Insights", "description": "Business intelligence, reports, and performance metrics", "overview": "Analytics Overview", "dateRange": "Date Range", "filters": "Filters", "exportReport": "Export Report"}, "integrations": {"title": "Integrations & API", "description": "Connect with third-party services and manage API settings", "overview": "Integrations Overview", "browse": "Browse", "apiSettings": "API Settings", "addIntegration": "Add Integration"}, "businessGroups": {"title": "Business Groups", "description": "Manage multi-business operations and franchise networks", "overview": "Business Groups Overview", "manageAccess": "Manage Access", "groupSettings": "Group Settings", "createGroup": "Create Group"}, "logistics": {"title": "Logistics & Shipping", "description": "Manage shipping, delivery, and logistics operations", "overview": "Logistics Overview", "trackShipments": "Track Shipments", "createShipment": "Create Shipment", "addCarrier": "Add Carrier"}, "settings": {"title": "Business Settings", "description": "Configure business preferences and system settings", "businessInfo": "Business Information", "reset": "Reset", "dangerZone": "Danger Zone", "saveChanges": "Save Changes"}, "profile": {"title": "User Profile", "description": "Manage your personal profile and preferences", "personalInfo": "Personal Information", "changePhoto": "Change Photo", "security": "Security", "saveProfile": "Save Profile"}}}