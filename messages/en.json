{"common": {"appName": "VentureDirection", "dashboard": "Dashboard", "businesses": "Businesses", "compliance": "Compliance", "documents": "Documents", "settings": "Settings", "signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "back": "Back", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "sort": "Sort", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "confirm": "Confirm", "yes": "Yes", "no": "No", "close": "Close", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset"}, "navigation": {"home": "Home", "dashboard": "Dashboard", "businesses": "Businesses", "compliance": "Compliance", "documents": "Documents", "settings": "Settings", "profile": "Profile", "help": "Help", "logout": "Logout"}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "phone": "Phone Number", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "createAccount": "Create Account", "welcomeBack": "Welcome Back", "getStarted": "Get Started"}, "dashboard": {"title": "Dashboard", "description": "Welcome to your business management dashboard", "overview": "Overview", "recentActivity": "Recent Activity", "quickActions": "Quick Actions", "statistics": "Statistics", "totalBusinesses": "Total Businesses", "pendingCompliance": "Pending Compliance", "overdueCompliance": "Overdue Compliance", "totalDocuments": "Total Documents"}, "businesses": {"title": "Businesses", "description": "Manage your businesses and their compliance", "addBusiness": "Add Business", "createBusiness": "Create Business", "businessName": "Business Name", "businessType": "Business Type", "country": "Country", "registrationNumber": "Registration Number", "incorporationDate": "Incorporation Date", "registrationStatus": "Registration Status", "systemStatus": "System Status", "businessStatus": "Business Status", "industry": "Industry", "industries": "Industries", "noBusinessesFound": "No businesses found", "businessCreated": "Business created successfully", "businessUpdated": "Business updated successfully", "businessDeleted": "Business deleted successfully", "businessDetails": "Business Details", "businessOverview": "Business Overview"}, "compliance": {"title": "Compliance", "description": "Track and manage regulatory compliance across all businesses", "complianceOverview": "Compliance Overview", "pendingCompliance": "Pending Compliance", "overdueCompliance": "Overdue Compliance", "completedCompliance": "Completed Compliance", "upcomingDeadlines": "Upcoming Deadlines", "complianceTemplate": "Compliance Template", "dueDate": "Due Date", "status": "Status", "penalty": "Penalty", "regulatoryBody": "Regulatory Body", "category": "Category", "frequency": "Frequency", "responses": "Responses", "submitCompliance": "Submit Compliance", "complianceSubmitted": "Compliance submitted successfully", "complianceUpdated": "Compliance updated successfully"}, "documents": {"title": "Documents", "description": "Manage business documents and files", "uploadDocument": "Upload Document", "documentTitle": "Document Title", "documentCategory": "Document Category", "fileSize": "File Size", "uploadDate": "Upload Date", "uploadedBy": "Uploaded By", "noDocumentsFound": "No documents found", "documentUploaded": "Document uploaded successfully", "documentDeleted": "Document deleted successfully", "downloadDocument": "Download Document", "shareDocument": "Share Document"}, "settings": {"title": "Settings", "description": "Manage your account and application preferences", "accountSettings": "Account <PERSON><PERSON>", "profileSettings": "Profile Settings", "notificationSettings": "Notification Settings", "languageSettings": "Language Settings", "timezoneSettings": "Timezone Settings", "securitySettings": "Security Settings", "privacySettings": "Privacy Settings", "language": "Language", "timezone": "Timezone", "theme": "Theme", "notifications": "Notifications", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "settingsUpdated": "Settings updated successfully"}, "errors": {"generic": "Something went wrong. Please try again.", "networkError": "Network error. Please check your connection.", "unauthorized": "You are not authorized to perform this action.", "forbidden": "Access denied.", "notFound": "The requested resource was not found.", "validationError": "Please check your input and try again.", "serverError": "Server error. Please try again later."}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "minLength": "Must be at least {min} characters", "maxLength": "Must be no more than {max} characters", "passwordMismatch": "Passwords do not match"}}