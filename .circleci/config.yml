version: 2.1

orbs:
  node: circleci/node@7.1.0

commands:
  setup-github-cli:
    description: Install and cache GitHub CLI
    steps:
      - restore_cache:
          keys:
            - gh-cli-{{ .Environment.CIRCLECI_CACHE_VERSION }}-v2
      - run:
          name: Install GitHub CLI
          command: |
            if ! command -v gh >/dev/null 2>&1; then
              sudo apt-get update && sudo apt-get install -y gh
            fi
      - save_cache:
          key: gh-cli-{{ .Environment.CIRCLECI_CACHE_VERSION }}-v2
          paths:
            - /usr/bin/gh
            - /usr/share/man/man1/gh.1.gz

  extract-pr-info:
    description: Extract pull request and commit info
    steps:
      - run:
          name: Extract Pull Request and Commit Info
          command: |
            pr_number="${CIRCLE_PULL_REQUEST##*/}"
            if [ -z "$pr_number" ]; then
              echo "No PR found, setting PR_NUMBER to 0"
              echo 'export PR_NUMBER="0"' >> "$BASH_ENV"
            else
              echo "export PR_NUMBER=\"$pr_number\"" >> "$BASH_ENV"
              pr_title=$(gh pr view "$pr_number" --json title --jq '.title' || echo "PR Title Not Found")
              echo "export PR_TITLE=\"$pr_title\"" >> "$BASH_ENV"
            fi
            last_committer_name=$(git log -1 --pretty=format:'%an')
            last_committer_github=$(gh api repos/${CIRCLE_PROJECT_USERNAME}/${CIRCLE_PROJECT_REPONAME}/commits/$(git log -1 --format=%H) --jq '.author.login' || echo "Unknown")
            echo "export LAST_COMMITTER_NAME=\"$last_committer_name\"" >> "$BASH_ENV"
            echo "export LAST_COMMITTER_GITHUB=\"$last_committer_github\"" >> "$BASH_ENV"
            source "$BASH_ENV"
            echo "PR Info: CIRCLE_PULL_REQUEST=$CIRCLE_PULL_REQUEST, PR_NUMBER=$PR_NUMBER, PR_TITLE=$PR_TITLE"
            echo "Committer: LAST_COMMITTER_NAME=$LAST_COMMITTER_NAME, LAST_COMMITTER_GITHUB=$LAST_COMMITTER_GITHUB"

  notify-on-failure:
    description: Notify last committer on job failure
    parameters:
      failure_file:
        type: string
        default: "failure.txt"
    steps:
      - run:
          name: Notify Last Committer
          command: |
            if [ -n "$NOTIFICATION_URL" ]; then
              message="failed-pipeline"
              failure_reason=$(cat << parameters.failure_file >> 2>/dev/null || echo "Unknown error")
              payload=$(jq -n \
                --arg username "$LAST_COMMITTER_GITHUB" \
                --arg message "$message" \
                --arg CIRCLE_PULL_REQUEST "$CIRCLE_PULL_REQUEST" \
                --arg pr_number "$PR_NUMBER" \
                --arg pr_title "$PR_TITLE" \
                --arg last_committer "$LAST_COMMITTER_NAME" \
                --arg failure_reason "$failure_reason" \
                --arg CIRCLE_BRANCH "$CIRCLE_BRANCH" \
                --arg CIRCLE_PROJECT_REPONAME "$CIRCLE_PROJECT_REPONAME" \
                --arg CIRCLE_PROJECT_USERNAME "$CIRCLE_PROJECT_USERNAME" \
                --arg CIRCLE_REPOSITORY_URL "$CIRCLE_REPOSITORY_URL" \
                --arg CIRCLE_SHA1 "$CIRCLE_SHA1" \
                '{username: $username, message: $message, vars: {CIRCLE_PULL_REQUEST: $CIRCLE_PULL_REQUEST, PR_TITLE: $pr_title, PR_NUMBER: $pr_number, LAST_COMMITTER_NAME: $last_committer, LAST_COMMITTER_GITHUB: $username, CIRCLE_BRANCH: $CIRCLE_BRANCH, CIRCLE_PROJECT_REPONAME: $CIRCLE_PROJECT_REPONAME, CIRCLE_PROJECT_USERNAME: $CIRCLE_PROJECT_USERNAME, CIRCLE_REPOSITORY_URL: $CIRCLE_REPOSITORY_URL, CIRCLE_SHA1: $CIRCLE_SHA1, failure_reason: $failure_reason}}')
              echo "Sending failure notification: $payload"
              curl -X POST -H 'Content-Type: application/json' --data "$payload" "$NOTIFICATION_URL"
            else
              echo "No failure webhook URL configured."
            fi

  setup-node:
    description: Install Node dependencies and cache
    steps:
      - restore_cache:
          keys:
            - yarn-deps-{{ checksum "yarn.lock" }}-v2
      - node/install-packages:
          pkg-manager: yarn
      - save_cache:
          paths:
            - ~/.yarn/cache
            - node_modules
          key: yarn-deps-{{ checksum "yarn.lock" }}-v2

  setup-vercel:
    description: Install and cache Vercel CLI
    steps:
      - restore_cache:
          keys:
            - vercel-cli-{{ .Environment.CIRCLECI_CACHE_VERSION }}-v2
      - run:
          name: Install Vercel CLI
          command: |
            if ! command -v vercel >/dev/null 2>&1; then
              yarn global add vercel@latest
            fi
      - save_cache:
          key: vercel-cli-{{ .Environment.CIRCLECI_CACHE_VERSION }}-v2
          paths:
            - ~/.yarn/global/node_modules/vercel
            - ~/.yarn/bin/vercel

  wait-for-postgres:
    description: Ensure PostgreSQL is ready
    steps:
      - run:
          name: Wait for PostgreSQL
          command: |
            for i in {1..10}; do
              if nc -z localhost 5432; then
                echo "PostgreSQL is ready!"
                exit 0
              fi
              echo "Waiting for PostgreSQL... Attempt $i/10"
              sleep 3
            done
            echo "PostgreSQL failed to start"
            exit 1

jobs:
  check-pr:
    docker:
      - image: cimg/node:lts
    steps:
      - checkout
      - setup-github-cli
      - extract-pr-info

  run-tests:
    docker:
      - image: cimg/node:lts
      - image: postgres:13
        environment:
          POSTGRES_DB: venturedirection
          POSTGRES_USER: ventureDirectionUser
          POSTGRES_PASSWORD: ventureDirectionPassword
    steps:
      - checkout
      - setup-github-cli
      - extract-pr-info
      - setup-node
      - wait-for-postgres
      - run:
          name: Set Environment
          command: |
            echo 'export DATABASE_URL=postgres://ventureDirectionUser:ventureDirectionPassword@localhost:5432/venturedirection' >> $BASH_ENV
            echo 'export NODE_ENV=CI' >> $BASH_ENV
            echo 'export POSTGRES_DATABASE_URL=postgres://ventureDirectionUser:ventureDirectionPassword@localhost:5432/venturedirection' >> $BASH_ENV
            echo 'export POSTGRES_DIRECT_URL=postgres://ventureDirectionUser:ventureDirectionPassword@localhost:5432/venturedirection' >> $BASH_ENV
      - run:
          name: Setup and Seed Database with Prisma
          command: |
            yarn prisma generate
            echo "Database migration and seeding are not yet implemented"
            #yarn prisma migrate deploy
            #yarn prisma:seed
            yarn prisma db push
      - run:
          name: Run Unit Tests
          command: |
            yarn test:ci 2> >(tee unit_test_failure.txt)
      - run:
          name: Notify on Test Failure
          command: |
            if [ -n "$NOTIFICATION_URL" ]; then
              message="failed-pipeline"
              failure_reason=$(cat unit_test_failure.txt 2>/dev/null || echo "Unit test failure")
              payload=$(jq -n \
                --arg username "$LAST_COMMITTER_GITHUB" \
                --arg message "$message" \
                --arg CIRCLE_PULL_REQUEST "$CIRCLE_PULL_REQUEST" \
                --arg pr_number "$PR_NUMBER" \
                --arg pr_title "$PR_TITLE" \
                --arg last_committer "$LAST_COMMITTER_NAME" \
                --arg failure_reason "$failure_reason" \
                --arg CIRCLE_BRANCH "$CIRCLE_BRANCH" \
                --arg CIRCLE_PROJECT_REPONAME "$CIRCLE_PROJECT_REPONAME" \
                --arg CIRCLE_PROJECT_USERNAME "$CIRCLE_PROJECT_USERNAME" \
                --arg CIRCLE_REPOSITORY_URL "$CIRCLE_REPOSITORY_URL" \
                --arg CIRCLE_SHA1 "$CIRCLE_SHA1" \
                '{username: $username, message: $message, vars: {CIRCLE_PULL_REQUEST: $CIRCLE_PULL_REQUEST, PR_TITLE: $pr_title, PR_NUMBER: $pr_number, LAST_COMMITTER_NAME: $last_committer, LAST_COMMITTER_GITHUB: $username, CIRCLE_BRANCH: $CIRCLE_BRANCH, CIRCLE_PROJECT_REPONAME: $CIRCLE_PROJECT_REPONAME, CIRCLE_PROJECT_USERNAME: $CIRCLE_PROJECT_USERNAME, CIRCLE_REPOSITORY_URL: $CIRCLE_REPOSITORY_URL, CIRCLE_SHA1: $CIRCLE_SHA1, failure_reason: $failure_reason}}')
              echo "Sending failure notification: $payload"
              curl -X POST -H 'Content-Type: application/json' --data "$payload" "$NOTIFICATION_URL"
            else
              echo "No failure webhook URL configured."
            fi
          when: on_fail

  preview-deployment:
    docker:
      - image: cimg/node:lts
    steps:
      - checkout
      - setup-github-cli
      - extract-pr-info
      - setup-node
      - setup-vercel
      - run:
          name: Pull Vercel Environment
          command: npx vercel pull --yes --environment=preview --token=$VERCEL_TOKEN
      - run:
          name: Load Vercel Env Vars
          command: source .vercel/.env.preview.local
      - run:
          name: Build Preview
          command: npx vercel build --token=$VERCEL_TOKEN 2> >(tee build_failure.txt)
      - run:
          name: Notify on Build Failure
          command: |
            if [ -n "$NOTIFICATION_URL" ]; then
              message="failed-pipeline"
              failure_reason=$(cat build_failure.txt 2>/dev/null || echo "Build failure")
              payload=$(jq -n \
                --arg username "$LAST_COMMITTER_GITHUB" \
                --arg message "$message" \
                --arg CIRCLE_PULL_REQUEST "$CIRCLE_PULL_REQUEST" \
                --arg pr_number "$PR_NUMBER" \
                --arg pr_title "$PR_TITLE" \
                --arg last_committer "$LAST_COMMITTER_NAME" \
                --arg failure_reason "$failure_reason" \
                --arg CIRCLE_BRANCH "$CIRCLE_BRANCH" \
                --arg CIRCLE_PROJECT_REPONAME "$CIRCLE_PROJECT_REPONAME" \
                --arg CIRCLE_PROJECT_USERNAME "$CIRCLE_PROJECT_USERNAME" \
                --arg CIRCLE_REPOSITORY_URL "$CIRCLE_REPOSITORY_URL" \
                --arg CIRCLE_SHA1 "$CIRCLE_SHA1" \
                '{username: $username, message: $message, vars: {CIRCLE_PULL_REQUEST: $CIRCLE_PULL_REQUEST, PR_TITLE: $pr_title, PR_NUMBER: $pr_number, LAST_COMMITTER_NAME: $last_committer, LAST_COMMITTER_GITHUB: $username, CIRCLE_BRANCH: $CIRCLE_BRANCH, CIRCLE_PROJECT_REPONAME: $CIRCLE_PROJECT_REPONAME, CIRCLE_PROJECT_USERNAME: $CIRCLE_PROJECT_USERNAME, CIRCLE_REPOSITORY_URL: $CIRCLE_REPOSITORY_URL, CIRCLE_SHA1: $CIRCLE_SHA1, failure_reason: $failure_reason}}')
              echo "Sending failure notification: $payload"
              curl -X POST -H 'Content-Type: application/json' --data "$payload" "$NOTIFICATION_URL"
            else
              echo "No notification webhook URL configured."
            fi
          when: on_fail
      - run:
          name: Deploy Preview
          command: |
            preview_url=$(npx vercel deploy --prebuilt --token=$VERCEL_TOKEN --archive=tgz)
            echo "export PREVIEW_URL=$preview_url" >> $BASH_ENV
      - run:
          name: Notify Preview Deployment
          command: |
            [ -z "$PR_NUMBER" ] && PR_NUMBER=$(echo "$CIRCLE_PULL_REQUEST" | grep -oE '[0-9]+$')
            COMMIT_AUTHOR=$(gh api /repos/"$CIRCLE_PROJECT_USERNAME"/"$CIRCLE_PROJECT_REPONAME"/commits/"$CIRCLE_SHA1" --jq '.author.login')
            JSON_PAYLOAD=$(jq -n \
              --arg pr_number "$PR_NUMBER" \
              --arg preview_url "$PREVIEW_URL" \
              --arg branch_name "$CIRCLE_BRANCH" \
              --arg repo_name "$CIRCLE_PROJECT_REPONAME" \
              --arg repo_owner "$CIRCLE_PROJECT_USERNAME" \
              --arg author "$COMMIT_AUTHOR" \
              '{pr_number: $pr_number, preview_url: $preview_url, branch_name: $branch_name, repo_name: $repo_name, repo_owner: $repo_owner, author: $author}')
            echo "Preview Deployment Payload: $JSON_PAYLOAD"
            [ "$PR_NUMBER" != "0" ] && curl -X POST "$NOTIFICATION_URL" -H "Content-Type: application/json" -d "$JSON_PAYLOAD"

  production-deployment:
    docker:
      - image: cimg/node:lts
    steps:
      - checkout
      - run:
          name: Skip Dependabot Commits
          command: |
            AUTHOR=$(git log -1 --pretty=format:'%ae')
            if [[ "$AUTHOR" == "dependabot[bot]@users.noreply.github.com" ]]; then
              echo "Skipping production deployment for Dependabot"
              circleci-agent step halt
            fi
      - setup-node
      - setup-vercel
      - run:
          name: Pull Vercel Production Env
          command: npx vercel pull --yes --environment=production --token=$VERCEL_TOKEN
      - run:
          name: Load Vercel Env Vars
          command: source .vercel/.env.production.local
      - run:
          name: Build Production
          command: npx vercel build --prod --token=$VERCEL_TOKEN
      - run:
          name: Deploy Production
          command: |
            production_url=$(npx vercel deploy --prebuilt --prod --token=$VERCEL_TOKEN --archive=tgz)
            echo "Production deployment: $production_url"

workflows:
  ci-cd:
    jobs:
      - check-pr
      - run-tests:
          requires:
            - check-pr
      - preview-deployment:
          requires:
            - run-tests
          filters:
            branches:
              ignore:
                - main
                - /^dependabot\/.*/
      - production-deployment:
          requires:
            - run-tests
          filters:
            branches:
              only:
                - main