# VentureDirection Database Models

## 📊 Overview

This document describes all the database models that support VentureDirection's business features. Each model is designed to handle real-world business operations with proper relationships, indexing, and data integrity.

## 🏪 Store & Products

### Product Model
**Purpose**: Core product catalog management

**Key Features**:
- SKU and barcode tracking
- Inventory management with low stock alerts
- Pricing with cost tracking and discounts
- Product variants (size, color, etc.)
- SEO optimization fields
- Multi-image support

**Relationships**:
- Belongs to Business
- Has many ProductVariants
- Has many OrderItems
- Has many InventoryMovements

### ProductVariant Model
**Purpose**: Product variations (size, color, style)

**Key Features**:
- Option-based variants (JSON storage)
- Individual pricing and inventory
- Separate SKU tracking
- Weight and dimension tracking

## 📦 Orders & Sales

### Order Model
**Purpose**: Customer order management and fulfillment

**Key Features**:
- Order lifecycle tracking (pending → shipped → delivered)
- Multi-channel support (online, POS, phone)
- Payment status tracking
- Shipping address management
- Tax and discount calculations

**Relationships**:
- Belongs to Business and Customer
- Has many OrderItems and Payments
- Can have associated Invoices

### OrderItem Model
**Purpose**: Individual line items within orders

**Key Features**:
- Product snapshot at time of order
- Quantity and pricing tracking
- Links to products and variants

## 💰 Finance & Payments

### Payment Model
**Purpose**: Payment processing and tracking

**Key Features**:
- Multi-gateway support (Stripe, PayPal, etc.)
- Payment method tracking
- External payment ID storage
- Gateway response logging

### Invoice Model
**Purpose**: Invoicing and billing management

**Key Features**:
- Invoice lifecycle (draft → sent → paid)
- Multiple invoice types (invoice, quote, receipt)
- Due date tracking and overdue detection
- Tax and discount calculations

### InvoiceItem Model
**Purpose**: Line items within invoices

**Key Features**:
- Flexible item descriptions
- Quantity and pricing
- Optional product linking

## 👥 CRM & Customers

### Customer Model
**Purpose**: Customer relationship management

**Key Features**:
- Individual and business customer types
- Multiple address storage
- Contact preferences and opt-ins
- Tax ID and VAT number support
- Customer lifecycle tracking

**Relationships**:
- Belongs to Business
- Has many Orders, Payments, Invoices
- Has many CustomerInteractions

### CustomerInteraction Model
**Purpose**: Customer communication tracking

**Key Features**:
- Multi-channel interaction logging
- Scheduled and completed interactions
- Outcome tracking
- File attachment support

### Lead Model
**Purpose**: Sales pipeline management

**Key Features**:
- Lead qualification tracking
- Budget and timeline information
- Lead scoring system
- Conversion tracking

## 📢 Marketing & Analytics

### Campaign Model
**Purpose**: Marketing campaign management

**Key Features**:
- Multi-channel campaigns (email, SMS, social)
- Audience targeting and segmentation
- Budget and goal tracking
- Performance metrics (impressions, clicks, conversions)

### AnalyticsEvent Model
**Purpose**: Business event tracking and analytics

**Key Features**:
- Custom event tracking
- UTM parameter support
- Geographic data capture
- Session and user tracking

### BusinessMetric Model
**Purpose**: KPI and business metrics storage

**Key Features**:
- Time-series metric storage
- Multiple time periods (hour, day, month, etc.)
- Dimensional data support
- Automated metric calculation

## 🚚 Logistics & Inventory

### InventoryMovement Model
**Purpose**: Stock movement tracking

**Key Features**:
- Inbound and outbound tracking
- Multiple movement types (purchase, sale, adjustment)
- Cost tracking per movement
- Location-based inventory

### Supplier Model
**Purpose**: Vendor and supplier management

**Key Features**:
- Contact information management
- Payment terms tracking
- Tax ID and VAT support
- Supplier status management

### PurchaseOrder Model
**Purpose**: Procurement and purchasing

**Key Features**:
- PO lifecycle tracking
- Expected vs actual delivery dates
- Partial receiving support
- Cost and tax calculations

### PurchaseOrderItem Model
**Purpose**: Purchase order line items

**Key Features**:
- Item description and SKU
- Quantity ordered vs received
- Unit cost tracking

## 🔗 Model Relationships

### Business-Centric Design
All models are scoped to a specific business, ensuring proper data isolation in multi-tenant scenarios.

### Key Relationship Patterns

```
Business
├── Products (1:many)
│   ├── ProductVariants (1:many)
│   └── InventoryMovements (1:many)
├── Orders (1:many)
│   ├── OrderItems (1:many)
│   └── Payments (1:many)
├── Customers (1:many)
│   ├── Orders (1:many)
│   └── CustomerInteractions (1:many)
├── Invoices (1:many)
│   └── InvoiceItems (1:many)
├── Suppliers (1:many)
│   └── PurchaseOrders (1:many)
└── Campaigns (1:many)
```

## 📊 Database Indexes

### Performance Optimization
Each model includes strategic indexes for common query patterns:

- **Business scoping**: All models indexed by `businessId`
- **Status filtering**: Status fields indexed for quick filtering
- **Date ranges**: Timestamp fields indexed for reporting
- **Search fields**: Email, phone, SKU fields indexed
- **Unique constraints**: Prevent duplicate data

### Example Index Strategy

```sql
-- Product indexes
@@index([businessId])
@@index([businessId, status])
@@index([businessId, isActive])
@@index([sku])
@@index([category])

-- Order indexes
@@index([businessId])
@@index([businessId, status])
@@index([businessId, createdAt])
@@index([customerId])
```

## 🔧 Enum Types

### Status Management
Consistent status enums across models:

- **ProductStatus**: DRAFT, ACTIVE, ARCHIVED, OUT_OF_STOCK
- **OrderStatus**: PENDING, CONFIRMED, PROCESSING, SHIPPED, DELIVERED, CANCELLED
- **PaymentStatus**: PENDING, PAID, FAILED, REFUNDED
- **InvoiceStatus**: DRAFT, SENT, VIEWED, PAID, OVERDUE
- **CustomerStatus**: ACTIVE, INACTIVE, PROSPECT, BLOCKED
- **LeadStatus**: NEW, CONTACTED, QUALIFIED, CONVERTED, LOST

## 🚀 Usage Examples

### Creating a Product

```typescript
const product = await prisma.product.create({
  data: {
    businessId: "business-123",
    name: "Premium T-Shirt",
    description: "High-quality cotton t-shirt",
    sku: "TSHIRT-001",
    price: 29.99,
    costPrice: 15.00,
    inventoryQuantity: 100,
    status: "ACTIVE",
    isActive: true,
    variants: {
      create: [
        {
          businessId: "business-123",
          name: "Large - Red",
          sku: "TSHIRT-001-L-RED",
          options: { size: "Large", color: "Red" },
          inventoryQuantity: 25
        }
      ]
    }
  }
})
```

### Creating an Order

```typescript
const order = await prisma.order.create({
  data: {
    businessId: "business-123",
    customerId: "customer-456",
    orderNumber: "ORD-2024-001",
    status: "PENDING",
    subtotal: 59.98,
    taxAmount: 4.80,
    totalAmount: 64.78,
    items: {
      create: [
        {
          businessId: "business-123",
          productId: "product-789",
          productName: "Premium T-Shirt",
          productSku: "TSHIRT-001-L-RED",
          quantity: 2,
          unitPrice: 29.99,
          totalPrice: 59.98
        }
      ]
    }
  }
})
```

## 🎯 Best Practices

1. **Always scope by business**: Include `businessId` in all queries
2. **Use transactions**: For operations affecting multiple models
3. **Implement soft deletes**: For important business data
4. **Track changes**: Use audit logs for sensitive operations
5. **Optimize queries**: Use appropriate indexes and select only needed fields
6. **Validate data**: Implement proper validation at application level
7. **Handle concurrency**: Use optimistic locking for inventory updates
