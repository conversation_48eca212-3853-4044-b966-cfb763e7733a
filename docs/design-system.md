# VentureDirection Design System

## 🎨 Brand Colors - Blue & White

VentureDirection uses a professional blue and white color scheme that conveys trust, reliability, and business professionalism.

### Primary Color Palette

**Main Brand Blue: `#3b82f6` (venture-500)**

```css
venture-50:  #eff6ff  /* Very light blue - backgrounds, hover states */
venture-100: #dbeafe  /* Light blue - subtle backgrounds */
venture-200: #bfdbfe  /* Lighter blue - borders, dividers */
venture-300: #93c5fd  /* Medium light blue - disabled states */
venture-400: #60a5fa  /* Medium blue - secondary actions */
venture-500: #3b82f6  /* Main brand blue - primary actions, logos */
venture-600: #2563eb  /* Dark blue - text, emphasis */
venture-700: #1d4ed8  /* Darker blue - hover states */
venture-800: #1e40af  /* Very dark blue - active states */
venture-900: #1e3a8a  /* Darkest blue - high contrast text */
```

### Usage Guidelines

#### Primary Actions
- **Buttons**: Use `venture-500` for primary buttons
- **Links**: Use `venture-600` for text links
- **Icons**: Use `venture-500` for primary icons

#### Backgrounds
- **Page Background**: Pure white `#ffffff`
- **Card Background**: Pure white `#ffffff`
- **Hover States**: `venture-50` for subtle hover effects
- **Active States**: `venture-100` for active selections

#### Text Colors
- **Headings**: `gray-900` for maximum contrast
- **Body Text**: `gray-600` for readable content
- **Secondary Text**: `gray-500` for less important content
- **Primary Text**: `venture-600` for emphasized content

## 🎯 Component Standards

### Buttons

```tsx
// Primary button
<Button variant="default">Primary Action</Button>

// Secondary button  
<Button variant="outline">Secondary Action</Button>

// Ghost button
<Button variant="ghost">Subtle Action</Button>

// Success/Error buttons
<Button variant="success">Success Action</Button>
<Button variant="destructive">Delete Action</Button>
```

**Button Variants:**
- `default`: Blue background, white text
- `outline`: White background, blue border and text
- `ghost`: Transparent background, blue text
- `success`: Green background, white text
- `destructive`: Red background, white text

### Badges

```tsx
// Primary badge
<Badge variant="default">Primary</Badge>

// Status badges
<Badge variant="success">Active</Badge>
<Badge variant="warning">Pending</Badge>
<Badge variant="error">Error</Badge>
```

### Cards

```tsx
// Standard card
<div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
  Card content
</div>

// Primary accent card
<div className="bg-venture-50 border border-venture-200 rounded-lg p-6">
  Primary content
</div>
```

## 🏗️ Layout Standards

### Business Header
- **Background**: Pure white
- **Border**: Light gray (`gray-200`)
- **Logo**: VentureDirection blue icon
- **Text**: Dark gray for readability

### Business Sidebar
- **Background**: Pure white
- **Active Item**: Blue background (`venture-500`) with white text
- **Hover State**: Light blue background (`venture-50`)
- **Text**: Gray for inactive, blue for active

### Navigation
- **Active State**: Blue background with white text
- **Hover State**: Light blue background
- **Border**: Light gray dividers

## 🎨 Logo Usage

### VentureLogo Component

```tsx
// Full logo with icon and text
<VentureLogo size="md" variant="full" />

// Icon only
<VentureLogo size="sm" variant="icon" />

// Text only
<VentureLogo size="lg" variant="text" />
```

**Logo Variants:**
- `full`: Icon + text (default)
- `icon`: Icon only for compact spaces
- `text`: Text only for minimal designs

**Logo Sizes:**
- `sm`: Small (24px height)
- `md`: Medium (32px height) - default
- `lg`: Large (48px height)
- `xl`: Extra large (64px height)

## 🎯 Semantic Colors

### Success (Green)
- `success-50`: `#f0fdf4` - Light backgrounds
- `success-500`: `#22c55e` - Primary success color
- `success-600`: `#16a34a` - Darker success

### Warning (Amber)
- `warning-50`: `#fffbeb` - Light backgrounds
- `warning-500`: `#f59e0b` - Primary warning color
- `warning-600`: `#d97706` - Darker warning

### Error (Red)
- `error-50`: `#fef2f2` - Light backgrounds
- `error-500`: `#ef4444` - Primary error color
- `error-600`: `#dc2626` - Darker error

## 📱 Responsive Design

### Breakpoints
- `sm`: 640px and up
- `md`: 768px and up
- `lg`: 1024px and up
- `xl`: 1280px and up
- `2xl`: 1536px and up

### Mobile-First Approach
- Start with mobile design
- Use responsive utilities: `md:`, `lg:`, etc.
- Ensure touch targets are at least 44px

## 🔧 Implementation

### Tailwind CSS Classes

```css
/* Primary colors */
bg-venture-500     /* Main brand blue background */
text-venture-600   /* Dark blue text */
border-venture-200 /* Light blue border */
hover:bg-venture-50 /* Light blue hover */

/* Semantic colors */
bg-success-500     /* Success background */
text-error-600     /* Error text */
bg-warning-100     /* Warning light background */

/* Neutral colors */
bg-white           /* Pure white background */
text-gray-900      /* Dark text */
border-gray-200    /* Light border */
```

### CSS Custom Properties

```css
:root {
  --venture-50: #eff6ff;
  --venture-500: #3b82f6;
  --venture-600: #2563eb;
  --white: #ffffff;
  --gray-200: #e2e8f0;
  --gray-600: #475569;
  --gray-900: #0f172a;
}
```

## ✅ Best Practices

### Do's
- ✅ Use `venture-500` for primary actions
- ✅ Use white backgrounds for cards and content
- ✅ Use gray text for body content
- ✅ Maintain consistent spacing (4px grid)
- ✅ Use semantic colors for status indicators
- ✅ Test color contrast for accessibility

### Don'ts
- ❌ Don't use multiple primary colors
- ❌ Don't use pure black text (use gray-900)
- ❌ Don't mix different blue shades randomly
- ❌ Don't forget hover and active states
- ❌ Don't use colors without semantic meaning

## 🎨 Color Accessibility

### Contrast Ratios
- **Normal text**: Minimum 4.5:1 contrast ratio
- **Large text**: Minimum 3:1 contrast ratio
- **UI elements**: Minimum 3:1 contrast ratio

### Tested Combinations
- ✅ `venture-600` on white: 7.2:1 (excellent)
- ✅ `gray-900` on white: 18.7:1 (excellent)
- ✅ `gray-600` on white: 5.7:1 (good)
- ✅ White on `venture-500`: 5.9:1 (good)

## 🚀 Getting Started

1. **Import the design system**:
   ```tsx
   import { ventureColors } from '@/lib/design-system/colors'
   ```

2. **Use standardized components**:
   ```tsx
   import { Button } from '@/components/common/button'
   import { Badge } from '@/components/common/badge'
   import { VentureLogo } from '@/components/common/ventureLogo'
   ```

3. **Apply consistent styling**:
   ```tsx
   <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
     <VentureLogo size="sm" />
     <Button variant="default">Get Started</Button>
   </div>
   ```

This design system ensures VentureDirection maintains a professional, consistent, and accessible blue and white brand identity across all components and pages.
