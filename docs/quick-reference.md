# VentureDirection Hooks & Contexts Quick Reference

## 🚀 Quick Start Guide

### Most Common Patterns

```typescript
// 1. Global user context (works anywhere)
const { user, hasPermission, hasRole } = useVentureUser()

// 2. Business-scoped context (requires BusinessUserProvider)
const { businessName, userRole, isOwner, canManageUsers } = useBusinessUser()

// 3. Business management
const { currentBusiness, businesses, switchBusiness } = useBusiness()

// 4. Permission checks
const { canViewFinance, canManageUsers, isAdmin } = useBusinessPermissions()

// 5. Onboarding
const { onboardingStatus, updateProgress } = useOnboarding()
```

## 📋 Hook Selection Guide

| Use Case | Hook | Context Required |
|----------|------|------------------|
| Basic user info | `useVentureUserBasic()` | None |
| Global permissions | `useVentureUser()` | None |
| Business features | `useBusinessUser()` | BusinessUserProvider |
| Business management | `useBusiness()` | BusinessProvider |
| Permission-heavy UI | `useBusinessPermissions()` | BusinessUserProvider |
| Onboarding flows | `useOnboarding()` | BusinessProvider |
| Single permission | `usePermission('users.manage')` | None |
| Single role | `useRole('admin')` | None |

## 🏗️ Context Hierarchy

```
App
├── BusinessProvider (global business management)
│   ├── Dashboard (business selection)
│   └── BusinessUserProvider (business-scoped context)
│       ├── BusinessHeader
│       ├── BusinessSidebar  
│       └── Business Pages (orders, finance, etc.)
```

## 🔐 Authentication Patterns

### Basic User Data
```typescript
function UserProfile() {
  const { user, isLoading } = useVentureUser()
  
  if (isLoading) return <Spinner />
  if (!user) return <LoginPrompt />
  
  return <UserInfo user={user} />
}
```

### Permission Checks
```typescript
function ProtectedFeature() {
  const { hasPermission } = useVentureUser()
  
  if (!hasPermission('users.manage')) {
    return <AccessDenied />
  }
  
  return <UserManagement />
}
```

### Business Context
```typescript
function BusinessPage() {
  const { currentBusiness } = useBusiness()
  
  if (!currentBusiness) {
    return <SelectBusiness />
  }
  
  return (
    <BusinessUserProvider businessId={currentBusiness.id}>
      <BusinessWorkspace />
    </BusinessUserProvider>
  )
}
```

## 🏢 Business Patterns

### Business-Scoped Features
```typescript
function BusinessFeature() {
  const { 
    businessName, 
    canManageUsers, 
    isOwner 
  } = useBusinessUser()
  
  return (
    <div>
      <h1>{businessName}</h1>
      {canManageUsers && <UserManagement />}
      {isOwner && <OwnerControls />}
    </div>
  )
}
```

### Permission-Based Navigation
```typescript
function BusinessNav() {
  const { 
    canViewOrders,
    canViewFinance,
    isAdmin 
  } = useBusinessPermissions()
  
  return (
    <nav>
      {canViewOrders && <NavItem href="/orders">Orders</NavItem>}
      {canViewFinance && <NavItem href="/finance">Finance</NavItem>}
      {isAdmin && <NavItem href="/admin">Admin</NavItem>}
    </nav>
  )
}
```

### Layout Integration
```typescript
function BusinessLayout({ businessId, children }) {
  return (
    <BusinessUserProvider businessId={businessId}>
      <div className="business-layout">
        <BusinessHeader />
        <BusinessSidebar />
        <main>{children}</main>
      </div>
    </BusinessUserProvider>
  )
}
```

## 📋 Onboarding Patterns

### Progress Display
```typescript
function OnboardingWidget() {
  const { onboardingStatus } = useOnboarding()
  
  if (!onboardingStatus || onboardingStatus.isFullyOnboarded) {
    return null
  }
  
  return (
    <div>
      <ProgressBar value={onboardingStatus.completionPercentage} />
      <p>{onboardingStatus.completedSteps} of {onboardingStatus.totalSteps} complete</p>
    </div>
  )
}
```

### Step Completion
```typescript
function FeaturePage() {
  const { updateProgress } = useOnboarding()
  
  const handleFeatureSetup = async () => {
    // Setup logic...
    await setupFeature()
    
    // Mark onboarding step complete
    await updateProgress('store', true)
  }
  
  return <FeatureSetup onComplete={handleFeatureSetup} />
}
```

## 🔒 Permission Constants

### Common Permissions
```typescript
import { BUSINESS_PERMISSIONS } from '@/lib/types/business-user-context'

// User Management
BUSINESS_PERMISSIONS.USERS_VIEW
BUSINESS_PERMISSIONS.USERS_MANAGE
BUSINESS_PERMISSIONS.USERS_INVITE

// Finance
BUSINESS_PERMISSIONS.FINANCE_VIEW
BUSINESS_PERMISSIONS.FINANCE_MANAGE
BUSINESS_PERMISSIONS.INVOICES_CREATE

// Orders
BUSINESS_PERMISSIONS.ORDERS_VIEW
BUSINESS_PERMISSIONS.ORDERS_MANAGE

// Settings
BUSINESS_PERMISSIONS.SETTINGS_VIEW
BUSINESS_PERMISSIONS.SETTINGS_MANAGE
```

### Common Roles
```typescript
import { BUSINESS_ROLES } from '@/lib/types/business-user-context'

BUSINESS_ROLES.OWNER
BUSINESS_ROLES.ADMIN
BUSINESS_ROLES.MANAGER
BUSINESS_ROLES.MEMBER
BUSINESS_ROLES.VIEWER
```

## 🚨 Common Pitfalls

### 1. Context Requirements
```typescript
// ❌ Will throw error
function MyComponent() {
  const { businessName } = useBusinessUser() // No provider!
}

// ✅ Correct
<BusinessUserProvider businessId="123">
  <MyComponent />
</BusinessUserProvider>
```

### 2. Permission Scope
```typescript
// ❌ Unclear scope
const { hasPermission } = useVentureUser()
const canManage = hasPermission('users.manage') // Which business?

// ✅ Business-scoped
const { hasPermission } = useBusinessUser()
const canManage = hasPermission('users.manage') // Current business
```

### 3. Loading States
```typescript
// ❌ No loading state
const { user } = useVentureUser()
return user ? <UserContent /> : <LoginPrompt />

// ✅ Proper loading
const { user, isLoading } = useVentureUser()
if (isLoading) return <Spinner />
return user ? <UserContent /> : <LoginPrompt />
```

## 🎯 Performance Tips

### 1. Use Specific Hooks
```typescript
// ✅ Fast - basic data only
const { user } = useVentureUserBasic()

// ✅ Fast - single permission
const { hasPermission } = usePermission('users.manage')

// ⚠️ Slower - full context
const { user } = useVentureUser({ includePermissions: true })
```

### 2. Leverage Caching
```typescript
// ✅ Uses 5-minute cache by default
const { user } = useVentureUser()

// ✅ Custom cache timeout
const { user } = useVentureUser({ cacheTimeout: 10 * 60 * 1000 })

// ✅ Manual refresh when needed
const { refetch } = useVentureUser()
await refetch()
```

### 3. Business Context Optimization
```typescript
// ✅ Single context for entire business workspace
<BusinessUserProvider businessId={businessId}>
  <BusinessHeader />     {/* No additional API calls */}
  <BusinessSidebar />    {/* No additional API calls */}
  <BusinessContent />    {/* No additional API calls */}
</BusinessUserProvider>
```

## 🔧 TypeScript Support

### Type-Safe Permission Checks
```typescript
const { hasPermission } = useBusinessUser()
const canManage: boolean = hasPermission('users.manage')

const { user } = useVentureUser()
const businessId: string | undefined = user?.currentBusinessId
```

### Interface Imports
```typescript
import type { 
  VentureUser,
  BusinessUserContextData,
  OnboardingFeatureModule 
} from '@/lib/types/venture-user'
```

## 📖 Full Documentation

- [Complete Hooks & Contexts Guide](./hooks-and-contexts.md)
- [useVentureUser Hook](./hooks/useVentureUser.md)
- [useBusinessUser Hook](./hooks/useBusinessUser.md)
- [useBusinessPermissions Hook](./hooks/useBusinessPermissions.md)
- [useOnboarding Hook](./hooks/useOnboarding.md)
- [BusinessUserProvider Context](./contexts/business-user-context.md)
