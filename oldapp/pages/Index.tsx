
import React from 'react';
import MainNav from '@/components/navigation/MainNav';
import Footer from '@/components/navigation/Footer';
import Hero from '@/components/home/<USER>';
import FeatureSection from '@/components/home/<USER>';
import CountriesSection from '@/components/home/<USER>';
import TestimonialsSection from '@/components/home/<USER>';
import PricingSection from '@/components/home/<USER>';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const Index = () => {
  return (
    <>
      <MainNav />
      
      <main>
        <Hero />
        <FeatureSection />
        <CountriesSection />
        
        {/* CTA Section */}
        <div className="bg-brand-600 text-white py-16">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold mb-6">Ready to start your international business journey?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join thousands of entrepreneurs who've successfully launched their businesses with GlobalInc.
            </p>
            <Button asChild size="lg" variant="outline" className="bg-white text-brand-600 hover:bg-gray-100 hover:text-brand-700">
              <Link to="/register">Get Started Today</Link>
            </Button>
          </div>
        </div>
        
        <PricingSection />
        <TestimonialsSection />
        
        {/* FAQ Section */}
        <div className="bg-white py-16 sm:py-24">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="lg:text-center mb-12">
              <h2 className="text-base text-brand-600 font-semibold tracking-wide uppercase">FAQ</h2>
              <p className="mt-2 text-3xl font-extrabold text-gray-900 sm:text-4xl">
                Frequently Asked Questions
              </p>
            </div>
            
            <div className="max-w-3xl mx-auto divide-y divide-gray-200">
              {[
                {
                  q: "How does the incorporation process work?",
                  a: "Our platform guides you through the incorporation process step-by-step, handling document preparation, submission to relevant authorities, and follow-up. We use official API integrations where available to streamline the process."
                },
                {
                  q: "How long does it take to incorporate a company?",
                  a: "Timeframes vary by country, ranging from 1-3 business days in Estonia to 7-10 business days in France. Once we have all your information, we handle the process as quickly as the local regulations allow."
                },
                {
                  q: "Do I need to travel to the country to incorporate?",
                  a: "No, our platform allows you to complete the entire process remotely for most jurisdictions. We'll let you know if there are any in-person requirements for specific countries."
                },
                {
                  q: "What compliance requirements will I need to meet?",
                  a: "Each country has different compliance requirements. Our platform keeps track of deadlines specific to your business and sends reminders before they're due, helping you stay compliant."
                },
                {
                  q: "Can I manage multiple businesses in different countries?",
                  a: "Yes, our platform allows you to manage multiple businesses across different jurisdictions from a single dashboard."
                }
              ].map((item, i) => (
                <div key={i} className="py-6">
                  <h3 className="text-lg font-medium text-gray-900">{item.q}</h3>
                  <p className="mt-2 text-gray-600">{item.a}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </>
  );
};

export default Index;
