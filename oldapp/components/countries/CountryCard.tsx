
import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

interface CountryCardProps {
  name: string;
  flag: string;
  description: string;
  processingTime: string;
  price: string;
  slug: string;
}

const CountryCard = ({
  name,
  flag,
  description,
  processingTime,
  price,
  slug
}: CountryCardProps) => {
  return (
    <Card className="h-full flex flex-col hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center gap-3">
        <div className="w-10 h-10 overflow-hidden rounded-full flex-shrink-0 border">
          <img 
            src={flag} 
            alt={`${name} flag`} 
            className="w-full h-full object-cover"
          />
        </div>
        <div>
          <CardTitle>{name}</CardTitle>
          <CardDescription>Business Incorporation</CardDescription>
        </div>
      </CardH<PERSON>er>
      <CardContent className="flex-grow">
        <p className="text-sm text-gray-600 mb-4">{description}</p>
        <div className="flex flex-col gap-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Processing Time:</span>
            <span className="font-medium">{processingTime}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Starting From:</span>
            <span className="font-medium text-brand-600">{price}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button asChild className="w-full bg-brand-600 hover:bg-brand-700">
          <Link to={`/incorporate/${slug}`}>
            Start Incorporation
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

export default CountryCard;
