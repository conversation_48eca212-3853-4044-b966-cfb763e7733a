import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck, faTimes } from '@fortawesome/free-solid-svg-icons';
import styles from './styles.module.css';

// Define types for the Select component props
interface SelectProps {
  name: string;
  placeholder?: string;
  validity?: boolean | null; // Can be true, false, or null
  options: string[]; // Assuming options is an array of strings
  onInput?: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  onClick?: (event: React.MouseEvent<HTMLSelectElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLSelectElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLSelectElement>) => void;
}

// Define the options function with proper typing
const options = (optionArray: string[]) =>
  optionArray.map((option: string, i: number) => (
    <option key={i} value={option}>
      {option}
    </option>
  ));

const Select: React.FC<SelectProps> = (props) => {
  let validClass = '';

  if (props.validity === true) validClass = ' valid';
  if (props.validity === false) validClass = ' invalid';

  return (
    <div className="input-group">
      <select
        className={`form-control${validClass}`}
        defaultValue=""
        name={props.name}
        onInput={props.onInput}
        onClick={props.onClick}
        onFocus={props.onFocus}
        onBlur={props.onBlur}
      >
        <option value="" disabled>
          {props.placeholder || 'Select your option'}
        </option>
        {options(props.options)}
      </select>
      <span className="input-group-addon">
        <FontAwesomeIcon className="icon valid" icon={faCheck} />
        <FontAwesomeIcon className="icon invalid" icon={faTimes} />
      </span>
    </div>
  );
};

export default Select;
