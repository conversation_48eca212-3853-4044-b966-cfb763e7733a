import React from 'react';
import styles from './styles.module.css';

// Define the props interface
interface TabProps {
  identifier: string; // Assuming identifier is a string
  activeTab: string; // Assuming activeTab is also a string
  children: React.ReactNode; // children can be any valid React node
}

const Tab: React.FC<TabProps> = (props) => {
  return (
    <div className={props.identifier === props.activeTab ? "currentTab" : "tab"}>
      {props.children}
    </div>
  );
};

export default Tab;

