import React from 'react';
import styles from './styles.module.css';

interface FooterProps {
  className?: string;
}

const Footer: React.FC<FooterProps> = ({ className }) => (
  <footer className={`tm-site-footer ${className || ''}`}>
    <div className="black-bg tm-footer-text">
      <a href="/about">About VentureDirection</a>
      {' '}|{' '}
      <a href="/contact">Contact Us</a>
      <span className="sparkstrand-link">
        Site crafted by{' '}
        <a 
          href="https://www.sparkstrand.com" 
          className="tm-footer-link" 
          target="_blank"
        >
          Spark Strand
        </a>
      </span>
    </div>
  </footer>
);

export default Footer;
