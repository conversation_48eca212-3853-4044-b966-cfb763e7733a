import React from 'react'

const RecruitmentTeamSettings: React.FC = () => {
    return (
      <div >
        <div className='border-b border-gray-200 mb-6'>
        <h2 className="text-xl font-semibold mb-2">Recruitment &amp; Team</h2>
        <p className="text-sm text-gray-600 mb-4">
          Please customize recruitment &amp; hiring settings here
        </p>
        </div>
        <div className='border-b border-gray-200 mb-6'>
        <h2 className="text-xl font-semibold mb-2">Customize hiring stages</h2>
        </div>
        <div className='border-b border-gray-200 mb-6'>
        <h2 className="text-xl font-semibold mb-2">Enable automated reminders for pending applications</h2>
        </div>
          <div>
            <button
              type="button"
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Save
            </button>
          </div>
      </div>
    )
  }

export default RecruitmentTeamSettings