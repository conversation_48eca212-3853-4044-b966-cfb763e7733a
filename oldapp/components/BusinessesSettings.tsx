import React from 'react'

const BusinessesSettings: React.FC = () => {
    const businesses = [
      {
        name: 'Market Place',
        registrationStatus: 'Registered',
        linkedDocs: [
          '/document1.svg',
          '/document2.svg',
        ],
      },
      {
        name: "Sammy's Rental Service",
        registrationStatus: 'Registered',
        linkedDocs: [
          '/sample-doc3.png',
          '/sample-doc4.png',
        ],
      },
    ]
  
    return (
      <div>
        <h2 className="text-xl font-semibold mb-2">Businesses</h2>
        <p className="text-sm text-gray-600 mb-6">
          Please update your business settings here
        </p>
  
        <div className="space-y-8">
  
          <div>
            <h3 className="text-sm font-medium mb-2">Business Details</h3>
            <div className="space-y-2">
              {businesses.map((biz) => (
                <div key={biz.name} className="flex items-center justify-between">
                  <span className="text-sm">{biz.name}</span>
                  <button
                    onClick={() => alert(`Edit ${biz.name}`)}
                    className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100"
                  >
                    Edit
                  </button>
                </div>
              ))}
            </div>
          </div>
  
          {/* Registration Status */}
          <div>
            <h3 className="text-sm font-medium mb-2">Registration Status</h3>
            <div className="space-y-2">
              {businesses.map((biz) => (
                <div key={biz.name} className="flex items-center space-x-2">
                  <span className="inline-block w-4 h-4 bg-green-500 rounded-full" />
                  <span className="text-sm">
                    {biz.name} ({biz.registrationStatus})
                  </span>
                </div>
              ))}
            </div>
          </div>
  
          <div>
            <h3 className="text-sm font-medium mb-2">Linked Documents</h3>
            <div className="space-y-4">
              {businesses.map((biz) => (
                <div key={biz.name}>
                  <div className="text-sm font-medium mb-1">{biz.name}</div>
                  <div className="flex items-center space-x-4">
                    {biz.linkedDocs.map((doc, i) => (
                      <div key={i} className="w-16 h-20 bg-gray-100 flex items-center justify-center border border-gray-200 rounded">
                        <img src={doc} alt="document" className="object-cover w-full h-full" />
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
  
          <div>
            <h3 className="text-sm font-medium mb-2">Business Deactivation</h3>
            <div className="space-y-2">
              {businesses.map((biz) => (
                <div key={biz.name} className="flex items-center justify-between">
                  <span className="text-sm">{biz.name}</span>
                  <button
                    onClick={() => alert(`Close Business: ${biz.name}`)}
                    className="px-3 py-1 text-sm bg-red-50 text-red-600 border border-red-200 rounded-md hover:bg-red-100"
                  >
                    Close Business
                  </button>
                </div>
              ))}
            </div>
          </div>
          <div className="flex justify-end space-x-3 pb-10">
            <button
              type="button"
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
              onClick={() => alert('Cancel')}
            >
              Cancel
            </button>
            <button
              type="button"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              onClick={() => alert('Save')}
            >
              Save
            </button>
          </div>
        </div>
      </div>
    )
}  

export default BusinessesSettings