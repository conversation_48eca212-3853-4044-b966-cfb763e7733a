import Head from 'next/head';
import styles from './styles.module.css';

// Define the props interface
interface HeaderProps {
  title?: string; // Optional title prop
}

const Header: React.FC<HeaderProps> = (props) => (
  <>
    <Head>
      <title>
        Venture Direction {props.title && ' - '} {props.title}
      </title>
      <link rel="icon" href="/favicon.ico" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    </Head>
    <div className={styles.logo} />
    <nav className="black-bg nav">
      <ul>
        <li><a href="/">Home</a></li>
        <li><a href="https://blog.venturedirection.com/">Blog</a></li>
        {/* <li>
          <a href="/" data-id="cold">Register a new business</a>
        </li> */}
        <li><a href="/register">Sign Up</a></li>
        <li><a href="/login">Login</a></li>
        <li><a href="/verify">Verify</a></li>
      </ul>
    </nav>
  </>
);

export default Header;
