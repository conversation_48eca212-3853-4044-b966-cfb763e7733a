"use client"

import Link from "next/link"
import { useTranslations } from "next-intl"
import { LanguageSwitcher } from "./language-switcher"

export function Header() {
  const t = useTranslations("Header")

  return (
    <header className="border-b">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-6">
          <Link href="/" className="font-bold text-xl">
            {t("title")}
          </Link>
          <nav className="hidden md:flex items-center gap-6">
            <Link href="/companies" className="text-sm font-medium">
              {t("companies")}
            </Link>
            <Link href="/filings" className="text-sm font-medium">
              {t("filings")}
            </Link>
            <Link href="/reminders" className="text-sm font-medium">
              {t("reminders")}
            </Link>
          </nav>
        </div>
        <div className="flex items-center gap-4">
          <LanguageSwitcher />
          {/* Add user profile or other header elements here */}
        </div>
      </div>
    </header>
  )
}
