import React from 'react'

const PaymentSettings: React.FC = () => {
    return (
      <div>
        <h2 className="text-xl font-semibold mb-2">Payment Settings</h2>
        <p className="text-sm text-gray-600 mb-6">
          Manage your subscription and payment methods here
        </p>
  
        <div className="space-y-8">
          <div>
            <label className="block text-sm font-medium mb-1">
              Subscription Plan
            </label>
          </div>
  
          <div>
            <label className="block text-sm font-medium mb-1">
              Payment Methods
            </label>
            <div className="bg-white rounded-md p-4 border border-gray-200 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-6 bg-gray-300 flex items-center justify-center rounded-sm">
                  <span className="text-xs font-bold">MC</span>
                </div>
                <p className="text-sm">
                  Ends with 3345 <span className="text-gray-400">03/28</span>
                </p>
              </div>
              <button
                className="text-red-500 text-sm hover:text-red-700 transition"
                onClick={() => alert('Delete Payment Method')}
              >
                Delete
              </button>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              Billing History
            </label>
            <div className="bg-white rounded-md p-4 border border-gray-200 space-y-2 text-sm">
              <div>Mon, 14 July 2025 15:27:43 GMT+2 — N7,000</div>
              <div>Wed, 15 May 2025 15:27:43 GMT+2 — N7,000</div>
              <div>Sun, 20 Apr 2025 12:00:00 GMT+2 — N7,000</div>
            </div>
          </div>
  
          <div>
            <label className="block text-sm font-medium mb-1">
              Auto-Renewal Settings
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="disableRenewal"
                className="h-4 w-4"
              />
              <label htmlFor="disableRenewal" className="text-sm">
                Disable
              </label>
            </div>
            <p className="text-sm text-gray-500 mt-1">
              Check to disable automatic renewal of subscription at the end of billing cycle
            </p>
          </div>
  
          <div className="flex justify-end space-x-2">
            <button
              type="button"
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
              onClick={() => alert('Cancelled')}
            >
              Cancel
            </button>
            <button
              type="button"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              onClick={() => alert('Saved')}
            >
              Save
            </button>
          </div>
        </div>
      </div>
    )
  }

export default PaymentSettings