import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck, faTimes } from '@fortawesome/free-solid-svg-icons';
import styles from './styles.module.css';

// Define the props interface
interface TextboxProps {
  rows?: number;
  name: string;
  placeholder?: string;
  validity?: boolean | null;
  onInput?: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onClick?: (event: React.MouseEvent<HTMLTextAreaElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLTextAreaElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLTextAreaElement>) => void;
}

const Textbox: React.FC<TextboxProps> = (props) => {
  let validClass = '';

  if (props.validity === true) validClass = ' valid';
  if (props.validity === false) validClass = ' invalid';

  return (
    <div className="input-group">
      <textarea
        rows={props.rows || 6} // Use 6 as default value if rows is not provided
        name={props.name}
        className={`form-control${validClass}`}
        placeholder={props.placeholder}
        required
        onInput={props.onInput}
        onClick={props.onClick}
        onFocus={props.onFocus}
        onBlur={props.onBlur}
      ></textarea>
      <span className="input-group-addon">
        <FontAwesomeIcon className="icon valid" icon={faCheck} />
        <FontAwesomeIcon className="icon invalid" icon={faTimes} />
      </span>
    </div>
  );
};

export default Textbox;
