import React from 'react';
import styles from './styles.module.css';

// Define the props interface
interface ContentContainerProps {
  children: React.ReactNode; // Define children prop as ReactNode
  className?: string; // Optional className prop
}

const ContentContainer: React.FC<ContentContainerProps> = (props) => (
  <main className={`black-bg content-container ${props.className || ''}`}>
    {props.children}
  </main>
);

export default ContentContainer;

