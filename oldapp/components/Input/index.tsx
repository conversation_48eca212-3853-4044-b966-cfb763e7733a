import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck, faTimes } from '@fortawesome/free-solid-svg-icons';
import styles from './styles.module.css';

// Define the props interface
interface InputProps {
  type?: string;           // Optional type prop (e.g., 'text', 'password', etc.)
  name?: string;           // Optional name prop
  placeholder?: string;    // Optional placeholder prop
  validity?: boolean;      // Optional validity prop (true/false)
  onInput?: React.ChangeEventHandler<HTMLInputElement>; // Optional onInput event handler
  onClick?: React.MouseEventHandler<HTMLInputElement>;   // Optional onClick event handler
  onFocus?: React.FocusEventHandler<HTMLInputElement>;    // Optional onFocus event handler
  onBlur?: React.FocusEventHandler<HTMLInputElement>;     // Optional onBlur event handler
}

const Input: React.FC<InputProps> = (props) => {
  let validClass = '';

  if (props.validity === true) validClass = ' valid';
  if (props.validity === false) validClass = ' invalid';

  return (
    <div className="input-group">
      <input
        type={props.type || 'text'}
        name={props.name || (props.placeholder && props.placeholder.toLowerCase().replace('business ', ''))}
        className={'form-control' + validClass}
        placeholder={props.placeholder}
        required
        onInput={props.onInput}
        onClick={props.onClick}
        onFocus={props.onFocus}
        onBlur={props.onBlur}
      />
      <span className="input-group-addon">
        <FontAwesomeIcon className="icon valid" icon={faCheck} />
        <FontAwesomeIcon className="icon invalid" icon={faTimes} />
      </span>
    </div>
  );
};

export default Input;
