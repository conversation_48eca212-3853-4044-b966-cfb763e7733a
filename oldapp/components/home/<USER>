
import React from 'react';

const testimonials = [
  {
    content: "GlobalInc streamlined our UK company registration process. The compliance reminders are invaluable to our business operations.",
    author: "<PERSON>",
    role: "Tech Startup Founder",
    image: "https://randomuser.me/api/portraits/women/11.jpg"
  },
  {
    content: "Setting up my business in Nigeria was seamless with GlobalInc. The integrated CAC service saved us weeks of paperwork.",
    author: "Chijioke Okonkwo",
    role: "E-commerce Entrepreneur",
    image: "https://randomuser.me/api/portraits/men/32.jpg"
  },
  {
    content: "The platform helped us manage our Irish subsidiary without the usual compliance headaches. Highly recommended!",
    author: "<PERSON>",
    role: "Finance Director",
    image: "https://randomuser.me/api/portraits/women/44.jpg"
  }
];

const TestimonialsSection = () => {
  return (
    <div className="bg-white py-16 sm:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="lg:text-center mb-12">
          <h2 className="text-base text-brand-600 font-semibold tracking-wide uppercase">Testimonials</h2>
          <p className="mt-2 text-3xl font-extrabold text-gray-900 sm:text-4xl">
            Trusted by entrepreneurs worldwide
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-3">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-8 relative">
              <svg
                className="absolute top-0 left-0 transform -translate-y-6 -translate-x-2 h-16 w-16 text-gray-200"
                fill="currentColor"
                viewBox="0 0 32 32"
                aria-hidden="true"
              >
                <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
              </svg>
              <div className="relative">
                <p className="text-gray-600 mb-6">{testimonial.content}</p>
                <div className="flex items-center">
                  <img 
                    src={testimonial.image} 
                    alt={testimonial.author}
                    className="h-12 w-12 rounded-full mr-4"
                  />
                  <div>
                    <p className="font-medium text-gray-900">{testimonial.author}</p>
                    <p className="text-gray-500 text-sm">{testimonial.role}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TestimonialsSection;
