
import React from 'react';
import { Calendar, FileText, Globe, Bell, Database, Send } from 'lucide-react';

const features = [
  {
    title: 'Global Incorporation',
    description: 'Register your business in multiple countries with streamlined processes adapted to local requirements.',
    icon: Globe,
  },
  {
    title: 'Compliance Management',
    description: 'Stay on top of filing requirements, annual reports, and other regulatory obligations across jurisdictions.',
    icon: FileText,
  },
  {
    title: 'Smart Reminders',
    description: 'Never miss important deadlines with our automated reminder system for compliance tasks.',
    icon: Bell,
  },
  {
    title: 'Calendar Integration',
    description: 'Keep track of all important dates and deadlines in one central business calendar.',
    icon: Calendar,
  },
  {
    title: 'Document Management',
    description: 'Store, organize and easily access all your business documents in one secure location.',
    icon: Database,
  },
  {
    title: 'Social Media Integration',
    description: 'Manage your business social media accounts directly from our platform.',
    icon: Send,
  },
];

const FeatureSection = () => {
  return (
    <div className="bg-white py-16 sm:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="lg:text-center">
          <h2 className="text-base text-brand-600 font-semibold tracking-wide uppercase">Features</h2>
          <p className="mt-2 text-3xl font-extrabold text-gray-900 sm:text-4xl">
            Everything you need to start and run your business
          </p>
          <p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
            Our platform simplifies business formation and management across borders.
          </p>
        </div>

        <div className="mt-16">
          <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {features.map((feature, index) => (
              <div key={index} className="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-brand-600 rounded-lg border border-gray-200 hover:border-brand-200 hover:shadow-md transition-all">
                <div className="inline-flex rounded-lg bg-brand-50 p-3 text-brand-600 group-hover:bg-brand-100 transition-colors">
                  <feature.icon className="h-6 w-6" aria-hidden="true" />
                </div>
                <div className="mt-6">
                  <h3 className="text-lg font-medium text-gray-900">
                    {feature.title}
                  </h3>
                  <p className="mt-2 text-base text-gray-500">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeatureSection;
