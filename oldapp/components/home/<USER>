
import React from 'react';
import CountryCard from '../countries/CountryCard';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const featuredCountries = [
  {
    name: 'United Kingdom',
    flag: 'https://flagcdn.com/gb.svg',
    description: 'Form a UK Limited Company with our streamlined process integrated with Companies House.',
    processingTime: '3-5 days',
    price: '£99',
    slug: 'uk'
  },
  {
    name: 'Nigeria',
    flag: 'https://flagcdn.com/ng.svg',
    description: 'Register your Nigerian business with our CAC API integration for a smooth process.',
    processingTime: '5-7 days',
    price: '₦75,000',
    slug: 'nigeria'
  },
  {
    name: 'France',
    flag: 'https://flagcdn.com/fr.svg',
    description: 'Establish your French business with our guided incorporation process.',
    processingTime: '7-10 days',
    price: '€199',
    slug: 'france'
  },
  {
    name: 'Ireland',
    flag: 'https://flagcdn.com/ie.svg',
    description: 'Incorporate an Irish Limited Company with our simplified registration process.',
    processingTime: '5-7 days',
    price: '€149',
    slug: 'ireland'
  }
];

const CountriesSection = () => {
  return (
    <div className="bg-gray-50 py-16 sm:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="lg:text-center mb-12">
          <h2 className="text-base text-brand-600 font-semibold tracking-wide uppercase">Countries</h2>
          <p className="mt-2 text-3xl font-extrabold text-gray-900 sm:text-4xl">
            Start your business anywhere
          </p>
          <p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
            Choose from multiple jurisdictions for your business incorporation.
          </p>
        </div>

        <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
          {featuredCountries.map((country) => (
            <CountryCard
              key={country.name}
              name={country.name}
              flag={country.flag}
              description={country.description}
              processingTime={country.processingTime}
              price={country.price}
              slug={country.slug}
            />
          ))}
        </div>

        <div className="mt-12 text-center">
          <Button asChild className="bg-brand-600 hover:bg-brand-700">
            <Link to="/countries">View All Countries</Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CountriesSection;
