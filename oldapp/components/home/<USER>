
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const Hero = () => {
  return (
    <div className="relative pt-32 pb-20 lg:pt-40 lg:pb-28">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-70" />
      </div>
      
      <div className="relative container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl font-extrabold tracking-tight text-gray-900 sm:text-5xl md:text-6xl">
            <span className="block">Start your business</span>
            <span className="block text-brand-600">anywhere in the world</span>
          </h1>
          <p className="mt-6 text-xl text-gray-600">
            GlobalInc helps entrepreneurs form and manage businesses across multiple countries 
            with simplified incorporation, compliance management, and integrated business tools.
          </p>
          <div className="mt-10 flex justify-center gap-4">
            <Button asChild className="px-8 py-6 text-lg bg-brand-600 hover:bg-brand-700">
              <Link to="/countries">Start Incorporating</Link>
            </Button>
            <Button asChild variant="outline" className="px-8 py-6 text-lg">
              <Link to="/features">Explore Features</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
