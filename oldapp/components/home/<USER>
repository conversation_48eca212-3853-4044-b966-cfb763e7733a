
import React from 'react';
import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';
import { Link } from 'react-router-dom';

const plans = [
  {
    name: 'Starter',
    price: '9.99',
    description: 'Perfect for solopreneurs and small businesses',
    features: [
      'One business registration',
      'Basic compliance reminders',
      'Document storage (5GB)',
      'Email support',
    ],
    cta: 'Start Now',
    popular: false,
    link: '/register',
  },
  {
    name: 'Growth',
    price: '29.99',
    description: 'Ideal for growing businesses with multiple needs',
    features: [
      'Three business registrations',
      'Advanced compliance management',
      'Document storage (20GB)',
      'Priority email support',
      'Social media integrations',
      'Financial reporting tools',
    ],
    cta: 'Get Started',
    popular: true,
    link: '/register',
  },
  {
    name: 'Enterprise',
    price: '99.99',
    description: 'For established businesses with complex requirements',
    features: [
      'Unlimited business registrations',
      'Full compliance automation',
      'Document storage (100GB)',
      'Dedicated account manager',
      'API access',
      'Custom integrations',
      'Multi-user access',
    ],
    cta: 'Contact Sales',
    popular: false,
    link: '/contact',
  }
];

const PricingSection = () => {
  return (
    <div className="bg-gray-50 py-16 sm:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-base text-brand-600 font-semibold tracking-wide uppercase">Pricing</h2>
          <p className="mt-2 text-3xl font-extrabold text-gray-900 sm:text-4xl">
            Simple, transparent pricing
          </p>
          <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
            Choose the plan that fits your business needs
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-3">
          {plans.map((plan, index) => (
            <div 
              key={index} 
              className={`relative rounded-lg overflow-hidden border ${
                plan.popular ? 'border-brand-500 shadow-lg' : 'border-gray-200'
              }`}
            >
              {plan.popular && (
                <div className="absolute top-0 right-0 bg-brand-500 text-white px-4 py-1 text-sm font-medium">
                  Most Popular
                </div>
              )}
              <div className="p-8 bg-white">
                <h3 className="text-lg font-medium text-gray-900">{plan.name}</h3>
                <div className="mt-4">
                  <span className="text-4xl font-extrabold">${plan.price}</span>
                  <span className="text-base font-medium text-gray-500">/month</span>
                </div>
                <p className="mt-4 text-gray-600">{plan.description}</p>

                <Button 
                  asChild
                  className={`mt-8 w-full ${
                    plan.popular ? 'bg-brand-600 hover:bg-brand-700' : ''
                  }`}
                  variant={plan.popular ? "default" : "outline"}
                >
                  <Link to={plan.link}>{plan.cta}</Link>
                </Button>
              </div>
              
              <div className="px-8 py-8 bg-gray-50 border-t border-gray-100">
                <ul className="space-y-4">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <div className="flex-shrink-0">
                        <Check className="h-5 w-5 text-brand-500" />
                      </div>
                      <p className="ml-3 text-sm text-gray-700">{feature}</p>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PricingSection;
