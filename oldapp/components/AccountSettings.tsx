import React, { useState } from "react";
import { FiAlertCircle } from "react-icons/fi";

const AccountSettings: React.FC = () => {
  // Example state
  const [domain, setDomain] = useState("sidothu.com");
  const [domainId, setDomainId] = useState("X-AE-A-19");
  const [phone, setPhone] = useState("+44 (*************");

  // Example checkboxes for Dashboard Customization
  const [dashboardOptions, setDashboardOptions] = useState({
    businessOverview: true,
    complianceRegulatory: false,
    resourcePlanning: false,
    financialGrowth: false,
  });

  // Notifications
  const [notifications, setNotifications] = useState({
    email: true,
    sound: false,
    complianceAlerts: true,
  });

  // Simple handlers
  const handleDashboardChange = (key: string) => {
    setDashboardOptions((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  const handleNotificationChange = (key: string) => {
    setNotifications((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  return (
    <div>
      <div className="flex items-center justify-between space-x-4 border-b border-gray-200 mb-6">
        <div>
          <h2 className="text-xl font-semibold mb-2">Your Profile</h2>
          <p className="text-sm text-gray-600 mb-3">
            Please update your profile settings here
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <FiAlertCircle />
          <button
            type="button"
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Go Pro
          </button>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8 border-b border-gray-200">
        <div className="space-y-4">
          <div className="border-b border-gray-200 mb-6">
            <label className="block text-sm font-medium mb-1">Name</label>
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={domain}
                onChange={(e) => setDomain(e.target.value)}
                className="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400"
              />
              <input
                type="text"
                value={domainId}
                onChange={(e) => setDomainId(e.target.value)}
                className="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400"
              />
            </div>
          </div>

          {/* Phone Number */}
          <div>
            <label className="block text-sm font-medium mb-1">
              Phone Number
            </label>
            <input
              type="text"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400"
            />
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              Profile Picture
            </label>
            <div className="flex items-center space-x-3">
              <div className="w-14 h-14 bg-gray-200 rounded-full" />
              <button className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100">
                Edit
              </button>
              <button className="px-3 py-1 text-sm border border-red-300 text-red-500 rounded-md hover:bg-red-50">
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="mb-8">
        <label className="block text-sm font-medium mb-2">
          Dashboard Customization
        </label>
        <p className="text-sm text-gray-600 mb-3">
          This allows users select what they want to see on their dashboard
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-2 p-4 bg-gray-100 rounded-md">
            <input
              type="checkbox"
              id="businessOverview"
              checked={dashboardOptions.businessOverview}
              onChange={() => handleDashboardChange("businessOverview")}
              className="h-4 w-4"
            />
            <label htmlFor="businessOverview" className="text-sm">
              Business Overview
            </label>
          </div>
          <div className="flex items-center space-x-2 p-4 bg-gray-100 rounded-md">
            <input
              type="checkbox"
              id="complianceRegulatory"
              checked={dashboardOptions.complianceRegulatory}
              onChange={() => handleDashboardChange("complianceRegulatory")}
              className="h-4 w-4"
            />
            <label htmlFor="complianceRegulatory" className="text-sm">
              Compliance & Regulatory Dashboard
            </label>
          </div>
          {/* Resource Planning */}
          <div className="flex items-center space-x-2 p-4 bg-gray-100 rounded-md">
            <input
              type="checkbox"
              id="resourcePlanning"
              checked={dashboardOptions.resourcePlanning}
              onChange={() => handleDashboardChange("resourcePlanning")}
              className="h-4 w-4"
            />
            <label htmlFor="resourcePlanning" className="text-sm">
              Resource Planning
            </label>
          </div>
          <div className="flex items-center space-x-2 p-4 bg-gray-100 rounded-md">
            <input
              type="checkbox"
              id="financialGrowth"
              checked={dashboardOptions.financialGrowth}
              onChange={() => handleDashboardChange("financialGrowth")}
              className="h-4 w-4"
            />
            <label htmlFor="financialGrowth" className="text-sm">
              Financial Growth Stats
            </label>
          </div>
        </div>
      </div>

      <div className="mb-8">
        <label className="block text-sm font-medium mb-2">Notifications</label>
        <p className="text-sm text-gray-600 mb-3">
          You will be notified when a new email comes in, or new documents are
          uploaded
        </p>
        <div className="flex flex-col space-y-3">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="email"
              checked={notifications.email}
              onChange={() => handleNotificationChange("email")}
              className="h-4 w-4"
            />
            <label htmlFor="email" className="text-sm">
              Email Notification
            </label>
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="sound"
              checked={notifications.sound}
              onChange={() => handleNotificationChange("sound")}
              className="h-4 w-4"
            />
            <label htmlFor="sound" className="text-sm">
              Sound Notification
            </label>
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="complianceAlerts"
              checked={notifications.complianceAlerts}
              onChange={() => handleNotificationChange("complianceAlerts")}
              className="h-4 w-4"
            />
            <label htmlFor="complianceAlerts" className="text-sm">
              Compliance Alerts
            </label>
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
          onClick={() => alert("Cancel")}
        >
          Cancel
        </button>
        <button
          type="button"
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          onClick={() => alert("Saved")}
        >
          Save
        </button>
      </div>
    </div>
  );
};

export default AccountSettings;
