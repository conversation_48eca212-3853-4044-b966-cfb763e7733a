import React from 'react';
import styles from './styles.module.css';

// Define the props interface
interface ProgressStepsProps {
  totalSteps?: number;  // Optional totalSteps prop
  currentStep?: number;  // Optional currentStep prop
}

const ProgressSteps: React.FC<ProgressStepsProps> = (props) => {
  // Destructure props for cleaner code
  const { totalSteps = 0, currentStep = 0 } = props;

  const steps = [...Array(totalSteps)].map((_, i) => {
    let classes = "step";

    if (currentStep > i) {
      classes += " finished";
    }
    if (currentStep === i) {
      classes += " active";
    }

    return <span key={i} className={classes}>{' '}</span>;
  });

  return (
    <div className="progressIndicator">
      {steps}
    </div>
  );
};

export default ProgressSteps;
