// import React from "react";
// import Image from "next/image";
// import HomeIcon from "@/public/assets/svg/sideHomeIcon.svg";
// import ProfileIcon from "@/public/assets/svg/sideProfileIcon.svg";
// import BusIcon from "@/public/assets/svg/sideBusinessIcon.svg";
// import DocIcon from "@/public/assets/svg/sideDocIcon.svg";
// import TaskIcon from "@/public/assets/svg/sideTaskIcon.svg";
// import SettingIcon from "@/public/assets/svg/sideSetIcon.svg";
// import SupportIcon from "@/public/assets/svg/sideSupIcon.svg";
// import PrivacyIcon from "@/public/assets/svg/sidePrivIcon.svg";

// // Define the sidebar menu items dynamically
// const menuItems = [
//   { label: "Home", icon: HomeIcon, href: "admin" },
//   { label: "Profile", icon: ProfileIcon, href: "occupation-form" },
//   { label: "Businesses", icon: BusIcon, href: "occupation" },
//   { label: "Documents", icon: DocIcon, href: "welcome" },
//   { label: "Tasks", icon: TaskIcon, href: "documents" },
//   { label: "Setting", icon: SettingIcon, href: "directors" },
//   { label: "Support", icon: SupportIcon, href: "shareholders" },
//   { label: "Privacy", icon: PrivacyIcon, href: "secretary" },
// ];

// const DashboardSidebar: React.FC = () => {
//   return (
//     <aside className="w-64 bg-[#01283E] text-white min-h-screen p-4">
//       <nav>
//         <ul className="list-none marker:space-y-4">
//           {menuItems.map((item, index) => (
//             <li
//               key={index}
//               className={`flex items-center space-x-5 pt-5 font-inter font-bold text-[18px] leading-[29.05px] ${
//                 item.label === "Setting" ? "mt-8" : ""
//               }`}
//             >
//               <Image src={item.icon} alt={item.label} width={20} height={20} />
//               <a href={item.href} className="text-white no-underline">
//                 {item.label}
//               </a>
//             </li>
//           ))}
//         </ul>
//       </nav>
//     </aside>
//   );
// };

// export default DashboardSidebar;


import React from "react";
import Image from "next/image";
import HomeIcon from "@/public/assets/svg/sideHomeIcon.svg";
import BusIcon from "@/public/assets/svg/sideBusinessIcon.svg";
import DocIcon from "@/public/assets/svg/sideDocIcon.svg";
import TaskIcon from "@/public/assets/svg/sideTaskIcon.svg";
import SettingIcon from "@/public/assets/svg/sideSetIcon.svg";
import SupportIcon from "@/public/assets/svg/sideSupIcon.svg";
import ProfileImage from "@/public/assets/svg/Avatar.svg"; 

const menuItems = [
  { label: "Home", icon: HomeIcon, href: "admin" },
  { label: "Businesses", icon: BusIcon, href: "occupation" },
  { label: "Business Steps", icon: TaskIcon, href: "occupation-form" },
  { label: "Documents & Notes", icon: DocIcon, href: "welcome" },
  { label: "Management", icon: TaskIcon, href: "documents" },
  { label: "Settings", icon: SettingIcon, href: "settings", isSeparator: true },
  // { label: "Business Steps", icon: TaskIcon, href: "occupation-form" },
  { label: "Help & Support", icon: SupportIcon, href: "shareholders" },
];

const DashboardSidebar: React.FC = () => {
  return (
    <aside className="w-full bg-[#01283E] text-white min-h-screen p-[2px] mt-[5px] flex flex-col justify-between">
      <div>
        <nav>
          <ul className="space-y-4">
            {menuItems.map((item, index) => (
              <li key={index}>
                <a
                  href={item.href}
                  className={`flex items-center p-3 rounded-lg bg-white text-black hover:bg-[#014259] ${
                    item.isSeparator ? "mt-6" : ""
                  }`}
                >
                  <Image src={item.icon} alt={item.label} width={24} height={24} className="bg-[#D9D9D9]" />
                  <span className="text-[12px] font-medium">{item.label}</span>
                </a>
              </li>
            ))}
          </ul>
        </nav>
      </div>

      <div className="mt-8 border-t border-gray-600 pt-4 flex items-center space-x-4">
        <Image
          src={ProfileImage}
          alt="User Profile"
          width={30}
          height={30}
          className="rounded-full"
        />
        <div>
          <p className="text-[10px] font-semibold">Azunyan U. Wu</p>
          <p className="text-[10px] text-gray-300">Basic Member</p>
        </div>
        <button className="ml-auto text-gray-300 hover:text-white">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            className="w-6 h-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M17 16l4-4m0 0l-4-4m4 4H7"
            />
          </svg>
        </button>
      </div>
    </aside>
  );
};

export default DashboardSidebar;
