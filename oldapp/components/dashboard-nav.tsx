"use client"

import type React from "react"

import Link from "next/link"
import { Home, Building2, FileText, Settings, LifeBuoy, LogOut } from "lucide-react"

interface DashboardNavProps {
  firstName: string
  imageUrl: string
}

export function DashboardNav({ firstName, imageUrl }: DashboardNavProps) {
  return (
    <div className="w-64 bg-[#01283e] text-white">
      <div className="p-4">
        <Link href="/" className="block py-4 text-2xl font-bold">
          VentureDirection
        </Link>
      </div>

      <nav className="mt-4 space-y-2 px-4">
        <NavItem href="/welcome" icon={<Home className="h-5 w-5" />} label="Home" />
        <NavItem href="/businesses" icon={<Building2 className="h-5 w-5" />} label="Businesses" />
        <NavItem href="/documents" icon={<FileText className="h-5 w-5" />} label="Documents & Notes" />
        <NavItem href="/management" icon={<FileText className="h-5 w-5" />} label="Management" />
      </nav>

      <div className="mt-auto space-y-2 px-4 pt-8">
        <NavItem href="/settings" icon={<Settings className="h-5 w-5" />} label="Settings" />
        <NavItem href="/help" icon={<LifeBuoy className="h-5 w-5" />} label="Help & Support" />
      </div>

      <div className="mt-auto border-t border-white/10 p-4">
        <div className="flex items-center gap-3">
          <div className="h-8 w-8 overflow-hidden rounded-full bg-gray-300">
            {imageUrl ? (
              <img src={imageUrl || "/placeholder.svg"} alt={firstName} className="h-full w-full object-cover" />
            ) : (
              <div className="flex h-full w-full items-center justify-center bg-gray-300 text-sm font-medium text-gray-600">
                {firstName.charAt(0)}
              </div>
            )}
          </div>
          <span>{firstName}</span>
          <Link href="/sign-out" className="ml-auto">
            <LogOut className="h-5 w-5" />
          </Link>
        </div>
      </div>
    </div>
  )
}

function NavItem({ href, icon, label }: { href: string; icon: React.ReactNode; label: string }) {
  return (
    <Link href={href} className="flex items-center gap-3 rounded-md px-4 py-3 transition-colors hover:bg-white/10">
      {icon}
      <span>{label}</span>
    </Link>
  )
}
