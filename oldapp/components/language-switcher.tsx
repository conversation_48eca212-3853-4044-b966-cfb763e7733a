"use client"

import { usePathname, useRouter } from "next/navigation"
import { useLocale } from "next-intl"
import { useState } from "react"
import { Check, ChevronDown, Globe } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

// Define language options with their labels
const languages = [
  { code: "en", label: "English" },
  { code: "fr", label: "Français" },
  { code: "es", label: "Español" },
  { code: "de", label: "Deuts<PERSON>" },
  { code: "ie", label: "English (Ireland)" },
  { code: "ng", label: "English (Nigeria)" },
  { code: "ee", label: "Eesti" },
]

export function LanguageSwitcher() {
  const locale = useLocale()
  const router = useRouter()
  const pathname = usePathname()
  const [open, setOpen] = useState(false)

  // Get the current language label
  const currentLanguage = languages.find((lang) => lang.code === locale)?.label || "English"

  // Function to switch language
  const switchLanguage = (newLocale: string) => {
    // Get the path without the locale prefix
    const pathWithoutLocale = pathname.replace(`/${locale}`, "")

    // Construct the new path with the new locale
    const newPath = `/${newLocale}${pathWithoutLocale}`

    // Navigate to the new path
    router.push(newPath)
    setOpen(false)
  }

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Globe className="h-4 w-4" />
          <span className="hidden sm:inline">{currentLanguage}</span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[200px]">
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => switchLanguage(language.code)}
            className="flex items-center justify-between cursor-pointer"
          >
            <span>{language.label}</span>
            {locale === language.code && <Check className="h-4 w-4 text-green-500" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
