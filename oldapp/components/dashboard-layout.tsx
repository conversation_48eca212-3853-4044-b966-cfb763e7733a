import type React from "react"
import { DashboardNav } from "./dashboard-nav"
import { DashboardHeader } from "./dashboard-header"
import { currentUser } from "@clerk/nextjs/server"

interface DashboardLayoutProps {
  children: React.ReactNode
}

export async function DashboardLayout({ children }: DashboardLayoutProps) {
  const user = await currentUser()
  const firstName = user?.firstName || user?.username || ""
  const email = user?.emailAddresses[0]?.emailAddress || ""
  const imageUrl = user?.imageUrl || ""

  return (
    <div className="flex min-h-screen">
      {/* Sidebar */}
      <DashboardNav firstName={firstName} imageUrl={imageUrl} />

      {/* Main Content */}
      <div className="flex-1">
        <DashboardHeader firstName={firstName} imageUrl={imageUrl} />
        <main className="p-6">{children}</main>
      </div>
    </div>
  )
}
