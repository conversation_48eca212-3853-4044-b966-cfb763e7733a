import React from 'react';
import ContentContainer from '../ContentContainer';
import styles from './styles.module.css';

// Define the shape of the errors object
interface Errors {
  [key: string]: string; // Assuming errors is an object with string keys and string values
}

// Function to parse errors
function parseErrors(errors: Errors | null | undefined) {
  if (!errors) return null; // Return null if there are no errors

  const errorList = Object.values(errors).map((error: string, key: number) => (
    <li key={key}>{error}</li>
  ));

  return (
    <div className="errorBox">
      <ul>
        {errorList}
      </ul>
    </div>
  );
}

// Define the props for Sidebar
interface SidebarProps {
  sidebox?: string; // Optional sidebox prop
  errors?: Errors | null | undefined; // Optional errors prop
}

// Sidebar component
const Sidebar: React.FC<SidebarProps> = (props) => (
  <div className="right-bar">
    {props.sidebox && 
      <ContentContainer className="tm-main tm-page-content">
        <p dangerouslySetInnerHTML={{ __html: props.sidebox }} />
      </ContentContainer>
    }

    {parseErrors(props.errors)}
    
    <div className="tm-site-header hidden">
      <i className="fas fa-search fa-2x tm-site-logo"></i>
      <h1 className="tm-site-name"></h1>
    </div>
  </div>
);

export default Sidebar;
