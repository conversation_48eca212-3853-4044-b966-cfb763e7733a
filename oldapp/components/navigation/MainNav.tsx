
import React from "react";
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Globe, Menu } from "lucide-react";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";

const MainNav = () => {
  return (
    <header className="fixed w-full bg-white/90 backdrop-blur-sm border-b border-gray-200 z-50">
      <nav className="container flex items-center justify-between py-4">
        <Link to="/" className="flex items-center gap-2">
          <Globe className="w-8 h-8 text-brand-600" />
          <span className="text-xl font-bold text-gray-900">GlobalInc</span>
        </Link>
        
        <div className="hidden md:flex items-center gap-6">
          <Link to="/features" className="text-gray-600 hover:text-brand-600 transition-colors">
            Features
          </Link>
          <Link to="/pricing" className="text-gray-600 hover:text-brand-600 transition-colors">
            Pricing
          </Link>
          <Link to="/countries" className="text-gray-600 hover:text-brand-600 transition-colors">
            Countries
          </Link>
          <Link to="/about" className="text-gray-600 hover:text-brand-600 transition-colors">
            About Us
          </Link>
        </div>

        <div className="hidden md:flex items-center gap-4">
          <Link to="/login">
            <Button variant="ghost">Log In</Button>
          </Link>
          <Link to="/register">
            <Button className="bg-brand-600 hover:bg-brand-700">Get Started</Button>
          </Link>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild className="md:hidden">
            <Button variant="outline" size="icon">
              <Menu className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem asChild>
              <Link to="/features" className="w-full cursor-pointer">Features</Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link to="/pricing" className="w-full cursor-pointer">Pricing</Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link to="/countries" className="w-full cursor-pointer">Countries</Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link to="/about" className="w-full cursor-pointer">About Us</Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link to="/login" className="w-full cursor-pointer">Log In</Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link to="/register" className="w-full cursor-pointer">Get Started</Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </nav>
    </header>
  );
};

export default MainNav;
