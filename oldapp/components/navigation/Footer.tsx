
import React from 'react';
import { Link } from 'react-router-dom';
import { Globe, Mail, MapPin, Phone } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center gap-2 mb-4">
              <Globe className="w-8 h-8 text-brand-400" />
              <span className="text-xl font-bold">GlobalInc</span>
            </div>
            <p className="text-gray-400 mb-6">
              Helping entrepreneurs start and manage businesses around the world.
            </p>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-brand-400" />
                <span className="text-gray-300">123 Business Avenue, London</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="h-5 w-5 text-brand-400" />
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-5 w-5 text-brand-400" />
                <a href="tel:+442012345678" className="text-gray-300 hover:text-white">
                  +44 20 1234 5678
                </a>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Services</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/services/incorporation" className="text-gray-400 hover:text-white">
                  Business Incorporation
                </Link>
              </li>
              <li>
                <Link to="/services/compliance" className="text-gray-400 hover:text-white">
                  Compliance Management
                </Link>
              </li>
              <li>
                <Link to="/services/social-media" className="text-gray-400 hover:text-white">
                  Social Media Management
                </Link>
              </li>
              <li>
                <Link to="/services/consulting" className="text-gray-400 hover:text-white">
                  Business Consulting
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Countries</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/incorporate/uk" className="text-gray-400 hover:text-white">
                  United Kingdom
                </Link>
              </li>
              <li>
                <Link to="/incorporate/nigeria" className="text-gray-400 hover:text-white">
                  Nigeria
                </Link>
              </li>
              <li>
                <Link to="/incorporate/france" className="text-gray-400 hover:text-white">
                  France
                </Link>
              </li>
              <li>
                <Link to="/incorporate/ireland" className="text-gray-400 hover:text-white">
                  Ireland
                </Link>
              </li>
              <li>
                <Link to="/incorporate/estonia" className="text-gray-400 hover:text-white">
                  Estonia
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Company</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/about" className="text-gray-400 hover:text-white">About Us</Link>
              </li>
              <li>
                <Link to="/blog" className="text-gray-400 hover:text-white">Blog</Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-400 hover:text-white">Contact</Link>
              </li>
              <li>
                <Link to="/careers" className="text-gray-400 hover:text-white">Careers</Link>
              </li>
              <li>
                <Link to="/terms" className="text-gray-400 hover:text-white">Terms of Service</Link>
              </li>
              <li>
                <Link to="/privacy" className="text-gray-400 hover:text-white">Privacy Policy</Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400">
          <p>© {new Date().getFullYear()} GlobalInc. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
