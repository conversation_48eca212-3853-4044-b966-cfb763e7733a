import React from "react";
import Image from "next/image";
import { <PERSON><PERSON><PERSON>, FiUser } from "react-icons/fi";
import BellIcon from "@/public/assets/svg/bellIcon.svg";
import UserIcon from "@/public/assets/svg/userIcon.svg";

const DashboardHeader: React.FC = () => {
  return (
    <header className="flex items-center justify-between p-4">
      {/* SEARCH BAR */}
      <div className="hidden md:flex items-center gap-2 text-xs rounded-full px-2"></div>
      <div className="flex items-center space-x-4">
        <Image
          src={BellIcon}
          width={20}
          height={20}
          alt="bell_icon"
          className="text-xl text-gray-600 cursor-pointer"
        />
        <div className="flex items-center space-x-2 cursor-pointer">
          <Image
            src={UserIcon}
            alt="user_icon"
            width={20}
            height={20}
            className="text-xl text-gray-600"
          />
          <span className="font-open font-bold text-[20px] leading-[34.05px]">Samuel</span>
        </div>
      </div>
    </header>
  );
};

export default DashboardHeader;
