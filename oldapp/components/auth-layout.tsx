import type React from "react"
import Link from "next/link"

interface AuthLayoutProps {
  children: React.ReactNode
}

export function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className="flex min-h-screen flex-col md:flex-row">
      {/* Left Side - Content */}
      <div className="flex flex-1 flex-col p-8 md:p-12 lg:p-16">
        <div className="mb-12">
          <Link href="/" className="text-2xl font-bold">
            VentureDirection
          </Link>
        </div>

        <div className="mx-auto w-full max-w-md">{children}</div>
      </div>

      {/* Right Side - Benefits */}
      <div className="flex flex-1 flex-col bg-[#01283e] p-8 text-white md:p-12 lg:p-16">
        <div className="flex h-full flex-col justify-center">
          <p className="mb-12 text-xl">
            Text about VentureDirection stats and logo because real data and personalisation drives action
          </p>

          <h2 className="mb-10 text-4xl font-bold">Did You Know?</h2>

          <ul className="space-y-8 text-lg">
            <li className="flex items-start">
              <span className="mr-2 text-xl">•</span>
              <span>
                Over 70% of businesses cite compliance as a top challenge—let VentureDirection keep you on track.
              </span>
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-xl">•</span>
              <span>
                65% of entrepreneurs say they're more confident about growth with the right compliance support.
              </span>
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-xl">•</span>
              <span>
                Businesses that use tools like VentureDirection are 40% more likely to meet their regulatory deadlines.
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  )
}
