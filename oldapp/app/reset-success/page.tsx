import Link from "next/link"
import { AuthLayout } from "@/components/auth-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Check } from "lucide-react"

export default function ResetSuccessPage() {
  return (
    <AuthLayout>
      <div className="flex flex-col items-center justify-center py-12">
        <div className="mb-8 flex h-24 w-24 items-center justify-center rounded-full bg-green-50">
          <Check className="h-12 w-12 text-green-500" />
        </div>

        <h2 className="mb-4 text-center text-2xl font-bold">Password reset successfully</h2>

        <div className="mt-8 w-full">
          <Link href="/sign-in">
            <Button className="w-full bg-[#01283e] py-6 text-white hover:bg-[#01283e]/90">Login</Button>
          </Link>
        </div>
      </div>
    </AuthLayout>
  )
}
