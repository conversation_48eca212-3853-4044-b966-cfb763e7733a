"use client"

import type React from "react"

import { useState } from "react"
import { useSignUp } from "@clerk/nextjs"
import { useRouter } from "next/navigation"

export default function VerifyEmailPage() {
  const { isLoaded, signUp, setActive } = useSignUp()
  const router = useRouter()
  const [code, setCode] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!isLoaded) return

    try {
      setIsLoading(true)
      setError("")

      const result = await signUp.attemptEmailAddressVerification({
        code,
      })

      if (result.status === "complete") {
        await setActive({ session: result.createdSessionId })
        router.push("/welcome")
      } else {
        console.error("Verification result not complete", result)
        setError("Something went wrong. Please try again.")
      }
    } catch (err: any) {
      setError(err.errors?.[0]?.message || "Something went wrong. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleResend = async () => {
    if (!isLoaded) return

    try {
      setIsLoading(true)
      setError("")

      await signUp.prepareEmailAddressVerification({
        strategy: "email_code",
      })

      // Show success message or notification
    } catch (err: any) {
      setError(err.errors?.[0]?.message || "Something went wrong. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-white p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold">VentureDirection</h1>
      </div>

      <div className="mx-auto w-full max-w-2xl space-y-8">
        <h2 className="text-center text-3xl font-bold">Thanks for joining VentureDirection!</h2>
        <p className="mt-4 text-center text-lg">
          Please check your email for a confirmation link to activate your account. Once confirmed, you'll be ready to
          start managing your business with confidence.
        </p>

        <div className="mt-8 text-center">
          <p className="mb-4 text-xl font-medium">Didn't receive the email?</p>
          <button onClick={handleResend} className="text-blue-600 hover:text-blue-500">
            Resend confirmation email
          </button>
        </div>
      </div>
    </div>
  )
}
