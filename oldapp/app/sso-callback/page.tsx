"use client"

import { useEffect } from "react"
import { useSignIn, useSignUp } from "@clerk/nextjs"
import { AuthLayout } from "@/components/auth-layout"

export default function SSOCallbackPage() {
  const { isLoaded: isSignInLoaded, signIn } = useSignIn()
  const { isLoaded: isSignUpLoaded, signUp } = useSignUp()

  useEffect(() => {
    if (isSignInLoaded && signIn) {
      signIn.authenticateWithRedirect({
        strategy: "oauth_callback",
        redirectUrl: "/sso-callback",
        redirectUrlComplete: "/",
      })
    }

    if (isSignUpLoaded && signUp) {
      signUp.authenticateWithRedirect({
        strategy: "oauth_callback",
        redirectUrl: "/sso-callback",
        redirectUrlComplete: "/",
      })
    }
  }, [isSignInLoaded, signIn, isSignUpLoaded, signUp])

  return (
    <AuthLayout>
      <div className="flex flex-col items-center justify-center py-12">
        <h2 className="mb-4 text-center text-2xl font-bold">Processing your sign in...</h2>
        <p className="text-center text-gray-500">Please wait while we complete your authentication.</p>
      </div>
    </AuthLayout>
  )
}
