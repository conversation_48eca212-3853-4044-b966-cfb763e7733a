import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { Inter } from "next/font/google";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "VentureDirection - Business Compliance Made Easy",
  description:
    "Start, register, and manage your business with ease using VentureDirection",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={inter.className}>{children}</body>
      </html>
    </ClerkProvider>
  );
}
