"use client"

import { useState } from "react"
import Link from "next/link"
import { useSignIn } from "@clerk/nextjs"
import { useRouter, useSearchParams } from "next/navigation"
import { AuthLayout } from "@/components/auth-layout"
import { Button } from "@/components/ui/button"

export default function CheckEmailPage() {
  const { isLoaded, signIn } = useSignIn()
  const router = useRouter()
  const searchParams = useSearchParams()
  const type = searchParams.get("type") || "reset"
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const email = signIn?.identifier || "your email"
  const maskedEmail = email.replace(/(.{2})(.*)(@.*)/, "$1***$3")

  const handleResend = async () => {
    if (!isLoaded || !signIn) return

    try {
      setIsLoading(true)
      setError("")

      if (type === "reset") {
        await signIn.create({
          strategy: "reset_password_email_code",
          identifier: signIn.identifier || "",
        })
      }

      // Show success message or notification
    } catch (err: any) {
      setError(err.errors?.[0]?.message || "Something went wrong. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthLayout>
      <div className="mb-8">
        <h2 className="text-3xl font-bold">Check your Email</h2>
        <p className="mt-4 text-base">We have sent an email with password reset information to {maskedEmail}.</p>
      </div>

      <div className="mt-8 space-y-4">
        <p className="text-base">Didn't receive the email? Check spam or promotion folder or</p>

        <Button
          onClick={handleResend}
          className="w-full bg-[#01283e] py-6 text-white hover:bg-[#01283e]/90"
          disabled={isLoading}
        >
          {isLoading ? "Resending..." : "Resend Email"}
        </Button>

        <div className="mt-6 text-center">
          <Link href="/sign-in" className="w-full inline-block py-3 px-4 border border-gray-300 rounded-md text-center">
            Back to Login
          </Link>
        </div>
      </div>
    </AuthLayout>
  )
}
