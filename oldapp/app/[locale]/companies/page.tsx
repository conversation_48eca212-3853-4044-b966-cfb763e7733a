"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useTranslations } from "next-intl"
import { Bell, Download, FileText, Filter, MoreHorizontal, Plus, Search, Settings, Trash2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { Company, Registry } from "@/oldapp/lib/types/database"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import DashboardLayout from "../dashboard-layout"

function Companies() {
  const t = useTranslations()
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [companies, setCompanies] = useState<Company[]>([])
  const [registries, setRegistries] = useState<Registry[]>([])
  const [selectedCountry, setSelectedCountry] = useState<string>("all")
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")

  useEffect(() => {
    fetchRegistries()
    fetchCompanies()
  }, [])

  useEffect(() => {
    fetchCompanies(selectedCountry !== "all" ? selectedCountry : undefined)
  }, [selectedCountry])

  const fetchRegistries = async () => {
    try {
      const response = await fetch("/api/registry")
      if (!response.ok) {
        throw new Error("Failed to fetch registries")
      }
      const data = await response.json()
      setRegistries(data)
    } catch (err) {
      console.error("Error fetching registries:", err)
    }
  }

  const fetchCompanies = async (countryCode?: string) => {
    setLoading(true)
    setError("")

    try {
      const url = countryCode ? `/api/companies?countryCode=${countryCode}` : "/api/companies"

      const response = await fetch(url)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to fetch companies")
      }

      const data = await response.json()
      setCompanies(data)
    } catch (err) {
      setError(err.message || "An error occurred while fetching companies")
      console.error("Error fetching companies:", err)
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status) => {
    switch (status?.toLowerCase()) {
      case "active":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            {t("companies.status.active")}
          </Badge>
        )
      case "dissolved":
        return <Badge variant="destructive">{t("companies.status.dissolved")}</Badge>
      case "liquidation":
        return <Badge variant="destructive">{t("companies.status.liquidation")}</Badge>
      default:
        return <Badge variant="secondary">{status || t("companies.status.unknown")}</Badge>
    }
  }

  const getCountryFlag = (countryCode) => {
    switch (countryCode) {
      case "uk":
        return "🇬🇧"
      case "ie":
        return "🇮🇪"
      case "ng":
        return "🇳🇬"
      case "ee":
        return "🇪🇪"
      case "fr":
        return "🇫🇷"
      default:
        return "🌍"
    }
  }

  const getRegistryName = (countryCode) => {
    const registry = registries.find((r) => r.country_code === countryCode)
    return registry ? registry.name : countryCode.toUpperCase()
  }

  const filteredCompanies = companies.filter(
    (company) =>
      company.name.toLowerCase().includes(searchQuery.toLowerCase()) || company.company_number.includes(searchQuery),
  )

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
          <div className="mr-4 hidden md:flex">
            <Link href="/" className="mr-6 flex items-center space-x-2">
              <FileText className="h-6 w-6" />
              <span className="hidden font-bold sm:inline-block">{t("common.appName")}</span>
            </Link>
            <nav className="flex items-center space-x-6 text-sm font-medium">
              <Link href="/dashboard" className="transition-colors hover:text-foreground/80">
                {t("common.dashboard")}
              </Link>
              <Link href="/companies" className="transition-colors hover:text-foreground/80 text-foreground">
                {t("common.companies")}
              </Link>
              <Link href="/filings" className="transition-colors hover:text-foreground/80">
                {t("common.filings")}
              </Link>
              <Link href="/reminders" className="transition-colors hover:text-foreground/80">
                {t("common.reminders")}
              </Link>
            </nav>
          </div>
          <div className="flex flex-1 items-center justify-end space-x-4">
            <div className="w-full flex-1 md:w-auto md:flex-none">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder={t("common.search") + "..."}
                  className="w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[300px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <nav className="flex items-center space-x-2">
              <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                <Bell className="h-4 w-4" />
                <span className="sr-only">{t("common.notifications")}</span>
              </Button>
              <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                <Settings className="h-4 w-4" />
                <span className="sr-only">{t("common.settings")}</span>
              </Button>
              <Avatar>
                <AvatarImage src="/placeholder.svg" alt="User" />
                <AvatarFallback>JS</AvatarFallback>
              </Avatar>
            </nav>
          </div>
        </div>
      </header>
      <main className="flex-1 p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">{t("common.companies")}</h1>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              {t("common.export")}
            </Button>
            <Button onClick={() => router.push("/companies/add")}>
              <Plus className="mr-2 h-4 w-4" />
              {t("companies.addCompany")}
            </Button>
          </div>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>{t("common.error")}</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Select value={selectedCountry} onValueChange={setSelectedCountry}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t("companies.filterByCountry")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("companies.allCountries")}</SelectItem>
                {registries.map((registry) => (
                  <SelectItem key={registry.country_code} value={registry.country_code}>
                    {getCountryFlag(registry.country_code)} {t(`registries.${registry.country_code}`)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              {t("common.filter")}
            </Button>
            <p className="text-sm text-muted-foreground">
              {loading ? (
                <Skeleton className="h-4 w-40" />
              ) : (
                t("companies.showing", {
                  filtered: filteredCompanies.length,
                  total: companies.length,
                })
              )}
            </p>
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t("companies.companyName")}</TableHead>
                <TableHead>{t("companies.companyNumber")}</TableHead>
                <TableHead>{t("companies.registry")}</TableHead>
                <TableHead>{t("companies.incorporationDate")}</TableHead>
                <TableHead>{t("companies.nextFilingDue")}</TableHead>
                <TableHead>{t("companies.status")}</TableHead>
                <TableHead className="text-right">{t("common.actions")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Skeleton className="h-5 w-40" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-5 w-20" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-5 w-20" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-5 w-24" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-5 w-24" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-5 w-16" />
                    </TableCell>
                    <TableCell className="text-right">
                      <Skeleton className="h-8 w-8 rounded-full ml-auto" />
                    </TableCell>
                  </TableRow>
                ))
              ) : filteredCompanies.length > 0 ? (
                filteredCompanies.map((company) => (
                  <TableRow key={company.id}>
                    <TableCell className="font-medium">{company.name}</TableCell>
                    <TableCell>{company.company_number}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <span>{getCountryFlag(company.country_code)}</span>
                        <span>{getRegistryName(company.country_code)}</span>
                      </div>
                    </TableCell>
                    <TableCell>{company.incorporation_date}</TableCell>
                    <TableCell>{company.next_filing_date || t("companies.notSet")}</TableCell>
                    <TableCell>{getStatusBadge(company.status)}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">{t("common.openMenu")}</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>{t("common.actions")}</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => router.push(`/companies/${company.id}`)}>
                            {t("common.viewDetails")}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => router.push(`/filing/new?companyId=${company.id}`)}>
                            {t("filings.prepareFiling")}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => router.push(`/reminders/new?companyId=${company.id}`)}>
                            {t("reminders.setReminders")}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-destructive">
                            <Trash2 className="mr-2 h-4 w-4" />
                            {t("common.remove")}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    {searchQuery ? (
                      <>{t("companies.noCompaniesFound")}</>
                    ) : (
                      <>
                        <p>{t("companies.noCompaniesAdded")}</p>
                        <Button variant="outline" className="mt-4" onClick={() => router.push("/companies/add")}>
                          <Plus className="mr-2 h-4 w-4" />
                          {t("companies.addFirstCompany")}
                        </Button>
                      </>
                    )}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </main>
    </div>
  )
}

export default function CompaniesPage() {
  return (
    <DashboardLayout>
      <Companies />
    </DashboardLayout>
  )
}
