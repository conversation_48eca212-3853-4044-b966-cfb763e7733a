"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { useTranslations } from "next-intl"
import { ArrowLeft, Bell, FileText, Loader2, Plus, Search, Settings } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { Company, Registry } from "@/oldapp/lib/types/database"

export default function AddCompany() {
  const t = useTranslations()
  const router = useRouter()
  const [companyNumber, setCompanyNumber] = useState("")
  const [selectedCountry, setSelectedCountry] = useState("uk")
  const [registries, setRegistries] = useState<Registry[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [companyData, setCompanyData] = useState(null)
  const [error, setError] = useState("")
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
    fetchRegistries()
  }, [])

  const fetchRegistries = async () => {
    try {
      const response = await fetch("/api/registry")
      if (!response.ok) {
        throw new Error("Failed to fetch registries")
      }
      const data = await response.json()
      setRegistries(data)
    } catch (err) {
      console.error("Error fetching registries:", err)
      setError(err.message || "Failed to fetch registries")
    }
  }

  const searchCompany = async (e) => {
    e.preventDefault()

    if (!companyNumber.trim()) {
      setError(t("companies.errors.companyNumberRequired"))
      return
    }

    setIsSearching(true)
    setError("")
    setCompanyData(null)

    try {
      const response = await fetch(
        `/api/company-registry?companyNumber=${companyNumber.trim()}&countryCode=${selectedCountry}`,
      )

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || t("companies.errors.fetchFailed"))
      }

      const data = await response.json()
      setCompanyData(data)
    } catch (err) {
      setError(err.message || t("companies.errors.searchError"))
    } finally {
      setIsSearching(false)
    }
  }

  const addCompany = async () => {
    if (!companyData) return

    setIsSaving(true)
    setError("")

    try {
      // Format the company data for our database
      const companyToSave: Partial<Company> = {
        company_number: companyData.company_number,
        name: companyData.company_name,
        status: companyData.company_status,
        incorporation_date: companyData.date_of_creation,
        company_type: companyData.type,
        registered_address: companyData.registered_office_address,
        sic_codes: companyData.sic_codes?.map((code) => code.description) || [],
        next_filing_date: null, // We'll need to calculate this based on the filing history
        country_code: selectedCountry,
        registry_specific_data: companyData.registry_specific_data,
      }

      // Save to our database
      const response = await fetch("/api/companies", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(companyToSave),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || t("companies.errors.saveFailed"))
      }

      // Redirect to the companies page
      router.push("/companies")
    } catch (err) {
      setError(err.message || t("companies.errors.saveError"))
    } finally {
      setIsSaving(false)
    }
  }

  const getCountryFlag = (countryCode) => {
    switch (countryCode) {
      case "uk":
        return "🇬🇧"
      case "ie":
        return "🇮🇪"
      case "ng":
        return "🇳🇬"
      case "ee":
        return "🇪🇪"
      case "fr":
        return "🇫🇷"
      default:
        return "🌍"
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
          <div className="mr-4 hidden md:flex">
            <Link href="/" className="mr-6 flex items-center space-x-2">
              <FileText className="h-6 w-6" />
              <span className="hidden font-bold sm:inline-block">{t("common.appName")}</span>
            </Link>
            <nav className="flex items-center space-x-6 text-sm font-medium">
              <Link href="/dashboard" className="transition-colors hover:text-foreground/80">
                {t("common.dashboard")}
              </Link>
              <Link href="/companies" className="transition-colors hover:text-foreground/80 text-foreground">
                {t("common.companies")}
              </Link>
              <Link href="/filings" className="transition-colors hover:text-foreground/80">
                {t("common.filings")}
              </Link>
              <Link href="/reminders" className="transition-colors hover:text-foreground/80">
                {t("common.reminders")}
              </Link>
            </nav>
          </div>
          <div className="flex flex-1 items-center justify-end space-x-4">
            <nav className="flex items-center space-x-2">
              <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                <Bell className="h-4 w-4" />
                <span className="sr-only">{t("common.notifications")}</span>
              </Button>
              <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                <Settings className="h-4 w-4" />
                <span className="sr-only">{t("common.settings")}</span>
              </Button>
              <Avatar>
                <AvatarImage src="/placeholder.svg" alt="User" />
                <AvatarFallback>JS</AvatarFallback>
              </Avatar>
            </nav>
          </div>
        </div>
      </header>
      <main className="flex-1 p-6">
        <div className="mb-6">
          <Button variant="outline" size="sm" onClick={() => router.push("/companies")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("common.back")} {t("common.companies")}
          </Button>
        </div>

        <div className="mx-auto max-w-2xl">
          <h1 className="text-3xl font-bold mb-6">{t("companies.addCompany")}</h1>

          <Card>
            <CardHeader>
              <CardTitle>{t("companies.searchCompany")}</CardTitle>
              <CardDescription>{t("companies.searchDescription")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <Select value={selectedCountry} onValueChange={setSelectedCountry}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={t("companies.selectRegistry")} />
                  </SelectTrigger>
                  <SelectContent>
                    {registries.map((registry) => (
                      <SelectItem key={registry.country_code} value={registry.country_code}>
                        {getCountryFlag(registry.country_code)} {t(`registries.${registry.country_code}`)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <form onSubmit={searchCompany} className="flex gap-2">
                <Input
                  placeholder={t("companies.enterCompanyNumber")}
                  value={companyNumber}
                  onChange={(e) => setCompanyNumber(e.target.value)}
                  className="flex-1"
                />
                <Button type="submit" disabled={isSearching}>
                  {isSearching ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t("common.loading")}
                    </>
                  ) : (
                    <>
                      <Search className="mr-2 h-4 w-4" />
                      {t("common.search")}
                    </>
                  )}
                </Button>
              </form>

              {error && (
                <Alert variant="destructive" className="mt-4">
                  <AlertTitle>{t("common.error")}</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {companyData && (
                <div className="mt-6 space-y-4">
                  <h3 className="text-lg font-semibold">{t("companies.companyFound")}</h3>
                  <div className="rounded-md border p-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="font-medium">{t("companies.companyName")}:</span>
                        <span>{companyData.company_name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">{t("companies.companyNumber")}:</span>
                        <span>{companyData.company_number}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">{t("companies.status")}:</span>
                        <span>{companyData.company_status}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">{t("companies.incorporationDate")}:</span>
                        <span>{companyData.date_of_creation}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">{t("companies.type")}:</span>
                        <span>{companyData.type}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">{t("companies.registeredAddress")}:</span>
                        <span>
                          {companyData.registered_office_address?.address_line_1},
                          {companyData.registered_office_address?.locality},
                          {companyData.registered_office_address?.postal_code}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
            {companyData && (
              <CardFooter>
                <Button onClick={addCompany} className="w-full" disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t("common.saving")}
                    </>
                  ) : (
                    <>
                      <Plus className="mr-2 h-4 w-4" />
                      {t("companies.addThisCompany")}
                    </>
                  )}
                </Button>
              </CardFooter>
            )}
          </Card>
        </div>
      </main>
    </div>
  )
}
