"use client";

import { redirect } from "next/navigation";
import { useUser } from "@clerk/nextjs";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CalendarClock, CheckCircle, FileText, Bell } from "lucide-react";
import Link from "next/link";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient();

export default async function HomePage() {
  const { isLoaded, isSignedIn } = useUser();

  if (isLoaded && !isSignedIn) {
    redirect("/sign-in");
  }

  // redirect("/welcome")
  const router = useRouter();

  useEffect(() => {
    if (isLoaded) {
      if (isSignedIn) {
        router.push("/welcome");
      } else {
        router.push("/sign-in");
      }
    }
  }, [isLoaded, isSignedIn, router]);

  // Show loading state while checking auth status
  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">
          Welcome to Venture Direction
        </h1>
        <QueryClientProvider client={queryClient}>
          <TooltipProvider>
            <Toaster />
            <Sonner />
          </TooltipProvider>
          <div className="flex min-h-screen flex-col">
            <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
              <div className="container flex h-16 items-center">
                <div className="mr-4 hidden md:flex">
                  <Link href="/" className="mr-6 flex items-center space-x-2">
                    <FileText className="h-6 w-6" />
                    <span className="hidden font-bold sm:inline-block">
                      Companies House Filing Assistant
                    </span>
                  </Link>
                  <nav className="flex items-center space-x-6 text-sm font-medium">
                    <Link
                      href="/dashboard"
                      className="transition-colors hover:text-foreground/80"
                    >
                      Dashboard
                    </Link>
                    <Link
                      href="/companies"
                      className="transition-colors hover:text-foreground/80"
                    >
                      Companies
                    </Link>
                    <Link
                      href="/filings"
                      className="transition-colors hover:text-foreground/80"
                    >
                      Filings
                    </Link>
                    <Link
                      href="/reminders"
                      className="transition-colors hover:text-foreground/80"
                    >
                      Reminders
                    </Link>
                  </nav>
                </div>
                <div className="flex flex-1 items-center justify-end space-x-4">
                  <nav className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 w-8 rounded-full"
                    >
                      <Bell className="h-4 w-4" />
                      <span className="sr-only">Notifications</span>
                    </Button>
                    <Button variant="outline" size="sm">
                      Sign In
                    </Button>
                    <Button size="sm">Sign Up</Button>
                  </nav>
                </div>
              </div>
            </header>
            <main className="flex-1">
              <section className="w-full py-12 md:py-24 lg:py-32 bg-slate-50 dark:bg-slate-900">
                <div className="container px-4 md:px-6">
                  <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 xl:grid-cols-2">
                    <div className="flex flex-col justify-center space-y-4">
                      <div className="space-y-2">
                        <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                          Never Miss a Companies House Filing Deadline
                        </h1>
                        <p className="max-w-[600px] text-muted-foreground md:text-xl">
                          Our platform simplifies the process of preparing and
                          submitting confirmation statements to Companies House,
                          with automated reminders and direct API integration.
                        </p>
                      </div>
                      <div className="flex flex-col gap-2 min-[400px]:flex-row">
                        <Button size="lg">Get Started</Button>
                        <Button size="lg" variant="outline">
                          Learn More
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-center justify-center">
                      <img
                        src="/placeholder.svg?height=400&width=500"
                        alt="Dashboard Preview"
                        className="rounded-lg object-cover shadow-lg"
                        width={500}
                        height={400}
                      />
                    </div>
                  </div>
                </div>
              </section>
              <section className="w-full py-12 md:py-24 lg:py-32">
                <div className="container px-4 md:px-6">
                  <div className="flex flex-col items-center justify-center space-y-4 text-center">
                    <div className="space-y-2">
                      <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                        Key Features
                      </h2>
                      <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                        Everything you need to manage your Companies House
                        filings efficiently
                      </p>
                    </div>
                  </div>
                  <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-3">
                    <Card>
                      <CardHeader className="flex flex-row items-center gap-4">
                        <CalendarClock className="h-8 w-8 text-primary" />
                        <div className="grid gap-1">
                          <CardTitle>Smart Reminders</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p>
                          Automated notifications for upcoming filing deadlines
                          via email, SMS, and in-app alerts.
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="flex flex-row items-center gap-4">
                        <FileText className="h-8 w-8 text-primary" />
                        <div className="grid gap-1">
                          <CardTitle>Direct Submission</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p>
                          Submit confirmation statements directly to Companies
                          House through our secure API integration.
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="flex flex-row items-center gap-4">
                        <CheckCircle className="h-8 w-8 text-primary" />
                        <div className="grid gap-1">
                          <CardTitle>Compliance Tracking</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p>
                          Monitor filing status and compliance history for all
                          your companies in one dashboard.
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </section>
            </main>
            <footer className="w-full border-t py-6">
              <div className="container flex flex-col items-center justify-between gap-4 md:flex-row">
                <p className="text-center text-sm text-muted-foreground md:text-left">
                  © 2025 Companies House Filing Assistant. All rights reserved.
                </p>
                <nav className="flex gap-4 text-sm text-muted-foreground">
                  <Link href="/terms" className="underline underline-offset-4">
                    Terms
                  </Link>
                  <Link
                    href="/privacy"
                    className="underline underline-offset-4"
                  >
                    Privacy
                  </Link>
                  <Link
                    href="/contact"
                    className="underline underline-offset-4"
                  >
                    Contact
                  </Link>
                </nav>
              </div>
            </footer>
          </div>
        </QueryClientProvider>
      </div>
    </div>
  );
}
