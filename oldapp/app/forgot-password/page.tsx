"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { useSignIn } from "@clerk/nextjs"
import { useRouter } from "next/navigation"
import { AuthLayout } from "@/components/auth-layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export default function ForgotPasswordPage() {
  const { isLoaded, signIn } = useSignIn()
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!isLoaded) return

    try {
      setIsLoading(true)
      setError("")

      await signIn.create({
        strategy: "reset_password_email_code",
        identifier: email,
      })

      router.push("/check-email?type=reset")
    } catch (err: any) {
      setError(err.errors?.[0]?.message || "Something went wrong. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthLayout>
      <div className="mb-8">
        <h2 className="text-3xl font-bold">Forgot Password</h2>
        <p className="mt-2 text-base">
          Enter the email you used to create your account so we can send you instructions on how to reset your password.
        </p>
      </div>

      <form className="space-y-6" onSubmit={handleSubmit}>
        {error && <div className="text-red-500 text-sm">{error}</div>}

        <div className="space-y-2">
          <label htmlFor="email" className="block text-base font-medium">
            Email
          </label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="w-full rounded-md border border-gray-300 px-4 py-2"
          />
        </div>

        <Button
          type="submit"
          className="w-full bg-[#01283e] py-6 text-white hover:bg-[#01283e]/90"
          disabled={isLoading}
        >
          {isLoading ? "Sending..." : "Send"}
        </Button>

        <div className="mt-6 text-center">
          <Link href="/sign-in" className="w-full inline-block py-3 px-4 border border-gray-300 rounded-md text-center">
            Back to Login
          </Link>
        </div>
      </form>
    </AuthLayout>
  )
}
