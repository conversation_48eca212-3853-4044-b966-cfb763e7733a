import { NextResponse } from "next/server"
import { getSupabaseServerClient } from "@/oldapp/lib/supabase/server"
import type { Reminder } from "@/oldapp/lib/types/database"

// Get all reminders for the current user
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get("companyId")
    const filingId = searchParams.get("filingId")

    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    const mockUserId = "00000000-0000-0000-0000-000000000000"

    let query = supabase
      .from("reminders")
      .select(`
        *,
        company:companies(id, name, company_number),
        filing:filings(id, filing_type, due_date)
      `)
      .eq("user_id", mockUserId)

    // Filter by company ID if provided
    if (companyId) {
      query = query.eq("company_id", companyId)
    }

    // Filter by filing ID if provided
    if (filingId) {
      query = query.eq("filing_id", filingId)
    }

    const { data, error } = await query.order("reminder_date", { ascending: true })

    if (error) {
      console.error("Error fetching reminders:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Error in reminders GET route:", error)
    return NextResponse.json({ error: "Failed to fetch reminders" }, { status: 500 })
  }
}

// Create a new reminder
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    const mockUserId = "00000000-0000-0000-0000-000000000000"

    // Add the user ID to the reminder data
    const reminderData: Partial<Reminder> = {
      ...body,
      user_id: mockUserId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    const { data, error } = await supabase.from("reminders").insert(reminderData).select().single()

    if (error) {
      console.error("Error creating reminder:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data, { status: 201 })
  } catch (error) {
    console.error("Error in reminders POST route:", error)
    return NextResponse.json({ error: "Failed to create reminder" }, { status: 500 })
  }
}

// Update an existing reminder
export async function PUT(request: Request) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json({ error: "Reminder ID is required" }, { status: 400 })
    }

    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    const mockUserId = "00000000-0000-0000-0000-000000000000"

    // Update the reminder
    const { data, error } = await supabase
      .from("reminders")
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .eq("user_id", mockUserId)
      .select()
      .single()

    if (error) {
      console.error("Error updating reminder:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Error in reminders PUT route:", error)
    return NextResponse.json({ error: "Failed to update reminder" }, { status: 500 })
  }
}

// Delete a reminder
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get("id")

    if (!id) {
      return NextResponse.json({ error: "Reminder ID is required" }, { status: 400 })
    }

    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    const mockUserId = "00000000-0000-0000-0000-000000000000"

    // Delete the reminder
    const { error } = await supabase.from("reminders").delete().eq("id", id).eq("user_id", mockUserId)

    if (error) {
      console.error("Error deleting reminder:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error in reminders DELETE route:", error)
    return NextResponse.json({ error: "Failed to delete reminder" }, { status: 500 })
  }
}
