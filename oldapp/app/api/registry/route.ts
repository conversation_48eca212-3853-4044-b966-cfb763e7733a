import { NextResponse } from "next/server"
import registryService from "@/oldapp/lib/services/registry-service"

// Get all registries
export async function GET() {
  try {
    const registries = await registryService.getAllRegistries()
    return NextResponse.json(registries)
  } catch (error) {
    console.error("Error fetching registries:", error)
    return NextResponse.json({ error: "Failed to fetch registries" }, { status: 500 })
  }
}
