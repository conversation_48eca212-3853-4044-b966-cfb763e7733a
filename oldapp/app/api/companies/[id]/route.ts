import { NextResponse } from "next/server"
import { getSupabaseServerClient } from "@/oldapp/lib/supabase/server"

// Get a specific company
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = params.id

    if (!id) {
      return NextResponse.json({ error: "Company ID is required" }, { status: 400 })
    }

    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    // For now, we'll use a mock user ID
    const userId = "00000000-0000-0000-0000-000000000000"

    const { data, error } = await supabase.from("companies").select("*").eq("id", id).eq("user_id", userId).single()

    if (error) {
      if (error.code === "PGRST116") {
        return NextResponse.json({ error: "Company not found" }, { status: 404 })
      }
      throw new Error(error.message)
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Error fetching company:", error)
    return NextResponse.json({ error: "Failed to fetch company" }, { status: 500 })
  }
}

// Update a company
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = params.id

    if (!id) {
      return NextResponse.json({ error: "Company ID is required" }, { status: 400 })
    }

    const body = await request.json()

    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    // For now, we'll use a mock user ID
    const userId = "00000000-0000-0000-0000-000000000000"

    // Check if company exists and belongs to user
    const { data: existingCompany, error: checkError } = await supabase
      .from("companies")
      .select("id")
      .eq("id", id)
      .eq("user_id", userId)
      .single()

    if (checkError) {
      if (checkError.code === "PGRST116") {
        return NextResponse.json({ error: "Company not found" }, { status: 404 })
      }
      throw new Error(checkError.message)
    }

    // Update the company
    const { data, error } = await supabase
      .from("companies")
      .update({
        ...body,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .eq("user_id", userId)
      .select()
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Error updating company:", error)
    return NextResponse.json({ error: "Failed to update company" }, { status: 500 })
  }
}

// Delete a company
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = params.id

    if (!id) {
      return NextResponse.json({ error: "Company ID is required" }, { status: 400 })
    }

    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    // For now, we'll use a mock user ID
    const userId = "00000000-0000-0000-0000-000000000000"

    // Check if company exists and belongs to user
    const { data: existingCompany, error: checkError } = await supabase
      .from("companies")
      .select("id")
      .eq("id", id)
      .eq("user_id", userId)
      .single()

    if (checkError) {
      if (checkError.code === "PGRST116") {
        return NextResponse.json({ error: "Company not found" }, { status: 404 })
      }
      throw new Error(checkError.message)
    }

    // Delete the company
    const { error } = await supabase.from("companies").delete().eq("id", id).eq("user_id", userId)

    if (error) {
      throw new Error(error.message)
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting company:", error)
    return NextResponse.json({ error: "Failed to delete company" }, { status: 500 })
  }
}
