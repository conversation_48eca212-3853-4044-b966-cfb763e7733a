import { NextResponse } from "next/server"
import { getSupabaseServerClient } from "@/oldapp/lib/supabase/server"
import type { Company } from "@/oldapp/lib/types/database"

// Get all companies for the current user
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const countryCode = searchParams.get("countryCode")

    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    // For now, we'll use a mock user ID
    const mockUserId = "00000000-0000-0000-0000-000000000000"

    let query = supabase.from("companies").select("*").eq("user_id", mockUserId)

    // Filter by country code if provided
    if (countryCode) {
      query = query.eq("country_code", countryCode)
    }

    const { data, error } = await query.order("name", { ascending: true })

    if (error) {
      console.error("Error fetching companies:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Error in companies GET route:", error)
    return NextResponse.json({ error: "Failed to fetch companies" }, { status: 500 })
  }
}

// Create a new company
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    const mockUserId = "00000000-0000-0000-0000-000000000000"

    // Add the user ID to the company data
    const companyData: Partial<Company> = {
      ...body,
      user_id: mockUserId,
      country_code: body.country_code || "uk", // Default to UK if not specified
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    const { data, error } = await supabase.from("companies").insert(companyData).select().single()

    if (error) {
      console.error("Error creating company:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data, { status: 201 })
  } catch (error) {
    console.error("Error in companies POST route:", error)
    return NextResponse.json({ error: "Failed to create company" }, { status: 500 })
  }
}

// Update a company
export async function PUT(request: Request) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json({ error: "Company ID is required" }, { status: 400 })
    }

    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    const mockUserId = "00000000-0000-0000-0000-000000000000"

    // Update the company
    const { data, error } = await supabase
      .from("companies")
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .eq("user_id", mockUserId)
      .select()
      .single()

    if (error) {
      console.error("Error updating company:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Error in companies PUT route:", error)
    return NextResponse.json({ error: "Failed to update company" }, { status: 500 })
  }
}
