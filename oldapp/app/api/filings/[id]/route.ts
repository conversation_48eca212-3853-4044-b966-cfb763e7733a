import { NextResponse } from "next/server"
import { getSupabaseServerClient } from "@/oldapp/lib/supabase/server"

// Get a specific filing
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = params.id

    if (!id) {
      return NextResponse.json({ error: "Filing ID is required" }, { status: 400 })
    }

    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    // For now, we'll use a mock user ID
    const userId = "00000000-0000-0000-0000-000000000000"

    const { data, error } = await supabase
      .from("filings")
      .select(`
        *,
        company:companies(id, name, company_number)
      `)
      .eq("id", id)
      .eq("user_id", userId)
      .single()

    if (error) {
      if (error.code === "PGRST116") {
        return NextResponse.json({ error: "Filing not found" }, { status: 404 })
      }
      throw new Error(error.message)
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Error fetching filing:", error)
    return NextResponse.json({ error: "Failed to fetch filing" }, { status: 500 })
  }
}

// Update a filing
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = params.id

    if (!id) {
      return NextResponse.json({ error: "Filing ID is required" }, { status: 400 })
    }

    const body = await request.json()

    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    // For now, we'll use a mock user ID
    const userId = "00000000-0000-0000-0000-000000000000"

    // Check if filing exists and belongs to user
    const { data: existingFiling, error: checkError } = await supabase
      .from("filings")
      .select("id")
      .eq("id", id)
      .eq("user_id", userId)
      .single()

    if (checkError) {
      if (checkError.code === "PGRST116") {
        return NextResponse.json({ error: "Filing not found" }, { status: 404 })
      }
      throw new Error(checkError.message)
    }

    // Update the filing
    const { data, error } = await supabase
      .from("filings")
      .update({
        ...body,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .eq("user_id", userId)
      .select()
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Error updating filing:", error)
    return NextResponse.json({ error: "Failed to update filing" }, { status: 500 })
  }
}
