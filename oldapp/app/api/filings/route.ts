import { NextResponse } from "next/server"
import { getSupabaseServerClient } from "@/oldapp/lib/supabase/server"
import type { Filing } from "@/oldapp/lib/types/database"

// Get all filings for the current user
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get("companyId")
    const countryCode = searchParams.get("countryCode")

    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    const mockUserId = "00000000-0000-0000-0000-000000000000"

    let query = supabase
      .from("filings")
      .select(`
        *,
        company:companies(id, name, company_number, country_code)
      `)
      .eq("user_id", mockUserId)

    // Filter by company ID if provided
    if (companyId) {
      query = query.eq("company_id", companyId)
    }

    // Filter by country code if provided
    if (countryCode) {
      query = query.eq("country_code", countryCode)
    }

    const { data, error } = await query.order("due_date", { ascending: true })

    if (error) {
      console.error("Error fetching filings:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Error in filings GET route:", error)
    return NextResponse.json({ error: "Failed to fetch filings" }, { status: 500 })
  }
}

// Create a new filing
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    const mockUserId = "00000000-0000-0000-0000-000000000000"

    // Get the company to determine the country code
    const { data: company, error: companyError } = await supabase
      .from("companies")
      .select("country_code")
      .eq("id", body.company_id)
      .single()

    if (companyError) {
      console.error("Error fetching company:", companyError)
      return NextResponse.json({ error: "Company not found" }, { status: 404 })
    }

    // Add the user ID and country code to the filing data
    const filingData: Partial<Filing> = {
      ...body,
      user_id: mockUserId,
      country_code: company.country_code,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    const { data, error } = await supabase.from("filings").insert(filingData).select().single()

    if (error) {
      console.error("Error creating filing:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data, { status: 201 })
  } catch (error) {
    console.error("Error in filings POST route:", error)
    return NextResponse.json({ error: "Failed to create filing" }, { status: 500 })
  }
}

// Update an existing filing
export async function PUT(request: Request) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json({ error: "Filing ID is required" }, { status: 400 })
    }

    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    const mockUserId = "00000000-0000-0000-0000-000000000000"

    // Update the filing
    const { data, error } = await supabase
      .from("filings")
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .eq("user_id", mockUserId)
      .select()
      .single()

    if (error) {
      console.error("Error updating filing:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Error in filings PUT route:", error)
    return NextResponse.json({ error: "Failed to update filing" }, { status: 500 })
  }
}

// Delete a filing
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get("id")

    if (!id) {
      return NextResponse.json({ error: "Filing ID is required" }, { status: 400 })
    }

    const supabase = getSupabaseServerClient()

    // In a real app, you would get the user ID from the session
    const mockUserId = "00000000-0000-0000-0000-000000000000"

    // Delete the filing
    const { error } = await supabase.from("filings").delete().eq("id", id).eq("user_id", mockUserId)

    if (error) {
      console.error("Error deleting filing:", error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error in filings DELETE route:", error)
    return NextResponse.json({ error: "Failed to delete filing" }, { status: 500 })
  }
}
