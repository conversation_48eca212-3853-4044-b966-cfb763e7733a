
export async function HANDLER (request: Request, response: Response) {
    const type = request.param.type;
    const action request.param.action;
    const help = request.param.help;
    const method = request.method;
    const ALLServices = {
        slack: {GET: ()=> {}}
        },
        teams: ()=> {}
    }
    
  return AllServices[type][method];
};
Export default async POST = HANDLER;
Export default async GET = HANDLER;


//jira feedback: Tell us what you think about the app
//jira help: Show this help information
//url/:type/:help
//url/:type/:feedback
//google.com/users/123





