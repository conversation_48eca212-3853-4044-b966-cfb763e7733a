import { NextResponse } from "next/server"

// Get the API key from environment variables
const COMPANIES_HOUSE_API_KEY = process.env.COMPANIES_HOUSE_API_KEY

// Base URL for Companies House API
const API_BASE_URL = "https://api.company-information.service.gov.uk"

/**
 * Get company profile from Companies House
 */
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const companyNumber = searchParams.get("companyNumber")

  if (!companyNumber) {
    return NextResponse.json({ error: "Company number is required" }, { status: 400 })
  }

  if (!COMPANIES_HOUSE_API_KEY) {
    return NextResponse.json({ error: "Companies House API key not configured" }, { status: 500 })
  }

  try {
    // Authorization header requires API key as Basic Auth
    const authHeader = `Basic ${Buffer.from(COMPANIES_HOUSE_API_KEY + ":").toString("base64")}`

    const response = await fetch(`${API_BASE_URL}/company/${companyNumber}`, {
      headers: {
        Authorization: authHeader,
        Accept: "application/json",
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Companies House API error:", errorText)

      return NextResponse.json(
        { error: `Companies House API error: ${response.status} - ${response.statusText}` },
        { status: response.status },
      )
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Error fetching company data:", error)
    return NextResponse.json({ error: "Failed to fetch company data" }, { status: 500 })
  }
}

/**
 * Get filing history for a company
 */
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { companyNumber } = body

    if (!companyNumber) {
      return NextResponse.json({ error: "Company number is required" }, { status: 400 })
    }

    if (!COMPANIES_HOUSE_API_KEY) {
      return NextResponse.json({ error: "Companies House API key not configured" }, { status: 500 })
    }

    // Authorization header requires API key as Basic Auth
    const authHeader = `Basic ${Buffer.from(COMPANIES_HOUSE_API_KEY + ":").toString("base64")}`

    // Get filing history
    const response = await fetch(`${API_BASE_URL}/company/${companyNumber}/filing-history`, {
      headers: {
        Authorization: authHeader,
        Accept: "application/json",
      },
    })

    if (!response.ok) {
      return NextResponse.json(
        { error: `Companies House API error: ${response.statusText}` },
        { status: response.status },
      )
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Error fetching filing history:", error)
    return NextResponse.json({ error: "Failed to fetch filing history" }, { status: 500 })
  }
}
