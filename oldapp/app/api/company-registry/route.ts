import { NextResponse } from "next/server"
import registryService from "@/oldapp/lib/services/registry-service"

/**
 * Get company profile from a registry
 */
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const companyNumber = searchParams.get("companyNumber")
  const countryCode = searchParams.get("countryCode") || "uk"

  if (!companyNumber) {
    return NextResponse.json({ error: "Company number is required" }, { status: 400 })
  }

  try {
    const apiKey = await registryService.getApiKey(countryCode)
    const apiBaseUrl = await registryService.getApiBaseUrl(countryCode)

    if (!apiKey) {
      return NextResponse.json({ error: `API key not configured for ${countryCode}` }, { status: 500 })
    }

    if (!apiBaseUrl) {
      return NextResponse.json({ error: `API base URL not found for ${countryCode}` }, { status: 500 })
    }

    // Different endpoints and authentication methods for different registries
    let response
    let data

    switch (countryCode) {
      case "uk":
        // Companies House (UK)
        const authHeader = `Basic ${Buffer.from(apiKey + ":").toString("base64")}`
        response = await fetch(`${apiBaseUrl}/company/${companyNumber}`, {
          headers: {
            Authorization: authHeader,
            Accept: "application/json",
          },
        })
        break

      case "ie":
        // Companies Registration Office (Ireland)
        response = await fetch(`${apiBaseUrl}/company/${companyNumber}`, {
          headers: {
            "X-API-KEY": apiKey,
            Accept: "application/json",
          },
        })
        break

      case "ng":
        // Corporate Affairs Commission (Nigeria)
        response = await fetch(`${apiBaseUrl}/companies/${companyNumber}`, {
          headers: {
            Authorization: `Bearer ${apiKey}`,
            Accept: "application/json",
          },
        })
        break

      case "ee":
        // Estonian Business Register
        response = await fetch(`${apiBaseUrl}/companies/${companyNumber}`, {
          headers: {
            "X-API-KEY": apiKey,
            Accept: "application/json",
          },
        })
        break

      case "fr":
        // National Register of Companies (France)
        response = await fetch(`${apiBaseUrl}/entreprises/${companyNumber}`, {
          headers: {
            Authorization: `Bearer ${apiKey}`,
            Accept: "application/json",
          },
        })
        break

      default:
        return NextResponse.json({ error: `Unsupported country code: ${countryCode}` }, { status: 400 })
    }

    if (!response.ok) {
      const errorText = await response.text()
      console.error(`Registry API error (${countryCode}):`, errorText)

      return NextResponse.json(
        { error: `Registry API error: ${response.status} - ${response.statusText}` },
        { status: response.status },
      )
    }

    data = await response.json()

    // Normalize the data structure for different registries
    const normalizedData = normalizeCompanyData(data, countryCode)
    return NextResponse.json(normalizedData)
  } catch (error) {
    console.error(`Error fetching company data from ${countryCode} registry:`, error)
    return NextResponse.json({ error: "Failed to fetch company data" }, { status: 500 })
  }
}

/**
 * Get filing history for a company
 */
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { companyNumber, countryCode = "uk" } = body

    if (!companyNumber) {
      return NextResponse.json({ error: "Company number is required" }, { status: 400 })
    }

    const apiKey = await registryService.getApiKey(countryCode)
    const apiBaseUrl = await registryService.getApiBaseUrl(countryCode)

    if (!apiKey) {
      return NextResponse.json({ error: `API key not configured for ${countryCode}` }, { status: 500 })
    }

    if (!apiBaseUrl) {
      return NextResponse.json({ error: `API base URL not found for ${countryCode}` }, { status: 500 })
    }

    // Different endpoints and authentication methods for different registries
    let response
    let data

    switch (countryCode) {
      case "uk":
        // Companies House (UK)
        const authHeader = `Basic ${Buffer.from(apiKey + ":").toString("base64")}`
        response = await fetch(`${apiBaseUrl}/company/${companyNumber}/filing-history`, {
          headers: {
            Authorization: authHeader,
            Accept: "application/json",
          },
        })
        break

      case "ie":
        // Companies Registration Office (Ireland)
        response = await fetch(`${apiBaseUrl}/company/${companyNumber}/filings`, {
          headers: {
            "X-API-KEY": apiKey,
            Accept: "application/json",
          },
        })
        break

      case "ng":
        // Corporate Affairs Commission (Nigeria)
        response = await fetch(`${apiBaseUrl}/companies/${companyNumber}/filings`, {
          headers: {
            Authorization: `Bearer ${apiKey}`,
            Accept: "application/json",
          },
        })
        break

      case "ee":
        // Estonian Business Register
        response = await fetch(`${apiBaseUrl}/companies/${companyNumber}/filings`, {
          headers: {
            "X-API-KEY": apiKey,
            Accept: "application/json",
          },
        })
        break

      case "fr":
        // National Register of Companies (France)
        response = await fetch(`${apiBaseUrl}/entreprises/${companyNumber}/actes`, {
          headers: {
            Authorization: `Bearer ${apiKey}`,
            Accept: "application/json",
          },
        })
        break

      default:
        return NextResponse.json({ error: `Unsupported country code: ${countryCode}` }, { status: 400 })
    }

    if (!response.ok) {
      return NextResponse.json({ error: `Registry API error: ${response.statusText}` }, { status: response.status })
    }

    data = await response.json()

    // Normalize the data structure for different registries
    const normalizedData = normalizeFilingHistoryData(data, countryCode)
    return NextResponse.json(normalizedData)
  } catch (error) {
    console.error(`Error fetching filing history from ${body?.countryCode} registry:`, error)
    return NextResponse.json({ error: "Failed to fetch filing history" }, { status: 500 })
  }
}

// Helper function to normalize company data from different registries
function normalizeCompanyData(data: any, countryCode: string): any {
  switch (countryCode) {
    case "uk":
      // Companies House (UK) data is already in the expected format
      return {
        company_name: data.company_name,
        company_number: data.company_number,
        company_status: data.company_status,
        date_of_creation: data.date_of_creation,
        type: data.type,
        registered_office_address: data.registered_office_address,
        sic_codes: data.sic_codes,
        registry_specific_data: data,
        country_code: countryCode,
      }

    case "ie":
      // Companies Registration Office (Ireland)
      return {
        company_name: data.name,
        company_number: data.number,
        company_status: data.status,
        date_of_creation: data.incorporationDate,
        type: data.type,
        registered_office_address: {
          address_line_1: data.address?.line1,
          address_line_2: data.address?.line2,
          locality: data.address?.city,
          postal_code: data.address?.postcode,
          country: "Ireland",
        },
        sic_codes: data.activities?.map((activity) => activity.description) || [],
        registry_specific_data: data,
        country_code: countryCode,
      }

    case "ng":
      // Corporate Affairs Commission (Nigeria)
      return {
        company_name: data.name,
        company_number: data.rcNumber,
        company_status: data.status,
        date_of_creation: data.dateOfRegistration,
        type: data.companyType,
        registered_office_address: {
          address_line_1: data.address,
          locality: data.city,
          postal_code: data.postalCode,
          country: "Nigeria",
        },
        sic_codes: [data.businessActivity],
        registry_specific_data: data,
        country_code: countryCode,
      }

    case "ee":
      // Estonian Business Register
      return {
        company_name: data.name,
        company_number: data.registryCode,
        company_status: data.status,
        date_of_creation: data.registrationDate,
        type: data.legalForm,
        registered_office_address: {
          address_line_1: data.address?.streetAddress,
          locality: data.address?.city,
          postal_code: data.address?.postalCode,
          country: "Estonia",
        },
        sic_codes: data.activities || [],
        registry_specific_data: data,
        country_code: countryCode,
      }

    case "fr":
      // National Register of Companies (France)
      return {
        company_name: data.denomination,
        company_number: data.siren,
        company_status: data.etatAdministratif,
        date_of_creation: data.dateCreation,
        type: data.formeJuridique,
        registered_office_address: {
          address_line_1: data.siege?.adresse,
          locality: data.siege?.ville,
          postal_code: data.siege?.codePostal,
          country: "France",
        },
        sic_codes: [data.activitePrincipale],
        registry_specific_data: data,
        country_code: countryCode,
      }

    default:
      return data
  }
}

// Helper function to normalize filing history data from different registries
function normalizeFilingHistoryData(data: any, countryCode: string): any {
  switch (countryCode) {
    case "uk":
      // Companies House (UK) data is already in the expected format
      return data

    case "ie":
      // Companies Registration Office (Ireland)
      return {
        items: data.filings.map((filing) => ({
          date: filing.date,
          type: filing.type,
          description: filing.description,
          links: filing.links,
        })),
      }

    case "ng":
      // Corporate Affairs Commission (Nigeria)
      return {
        items: data.filings.map((filing) => ({
          date: filing.filingDate,
          type: filing.filingType,
          description: filing.description,
          links: { document: filing.documentUrl },
        })),
      }

    case "ee":
      // Estonian Business Register
      return {
        items: data.filings.map((filing) => ({
          date: filing.submissionDate,
          type: filing.type,
          description: filing.description,
          links: { document: filing.documentUrl },
        })),
      }

    case "fr":
      // National Register of Companies (France)
      return {
        items: data.actes.map((acte) => ({
          date: acte.date,
          type: acte.type,
          description: acte.description,
          links: { document: acte.url },
        })),
      }

    default:
      return data
  }
}
