import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CheckCircle } from "lucide-react"

export default function BusinessRegistrationSuccessPage() {
  return (
    <div className="flex flex-col items-center justify-center py-16">
      <div className="mb-8 text-green-500">
        <CheckCircle className="h-24 w-24" />
      </div>

      <h1 className="mb-4 text-3xl font-bold">Business Successfully Registered!</h1>

      <p className="mb-12 max-w-lg text-center text-lg text-gray-600">
        Your business has been successfully registered with VentureDirection. You can now manage your business
        compliance and documentation.
      </p>

      <div className="flex gap-4">
        <Link href="/businesses">
          <Button variant="outline" className="px-6 py-2">
            View All Businesses
          </Button>
        </Link>

        <Link href="/welcome">
          <Button className="bg-[#01283e] px-6 py-2 text-white hover:bg-[#01283e]/90">Go to Dashboard</Button>
        </Link>
      </div>
    </div>
  )
}
