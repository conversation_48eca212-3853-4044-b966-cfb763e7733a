"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Globe } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export default function BusinessSearchPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCountry, setSelectedCountry] = useState("Nigeria")

  const countries = [
    { name: "Nigeria", flag: "🇳🇬" },
    { name: "Ghana", flag: "🇬🇭" },
    { name: "UK", flag: "🇬🇧" },
    { name: "Ireland", flag: "🇮🇪" },
    { name: "France", flag: "🇫🇷" },
    { name: "Estonia", flag: "🇪🇪" },
  ]

  return (
    <div className="mx-auto max-w-4xl">
      <h1 className="mb-12 text-center text-3xl font-bold">Basic Business Information</h1>

      <div className="mb-16">
        <h2 className="mb-8 text-center text-3xl">What is the name of your business?</h2>

        <div className="relative flex items-center">
          <Input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Enter business name"
            className="h-16 rounded-md bg-gray-200 px-4 py-2 text-lg"
          />

          <Button className="absolute right-0 h-16 rounded-l-none rounded-r-md bg-green-500 px-8 text-lg font-medium text-white hover:bg-green-600">
            Search
          </Button>

          <div className="absolute right-[-70px]">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-12 w-12 rounded-full p-0">
                  <Globe className="h-8 w-8" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56 bg-gray-200 p-2">
                <div className="mb-2 px-2 py-1.5 text-sm font-semibold">Select Country</div>
                <div className="grid grid-cols-3 gap-1">
                  {countries.map((country) => (
                    <DropdownMenuItem
                      key={country.name}
                      className="flex flex-col items-center justify-center p-2 text-center"
                      onClick={() => setSelectedCountry(country.name)}
                    >
                      <div className="text-2xl">{country.flag}</div>
                      <div className="mt-1 text-xs">{country.name}</div>
                    </DropdownMenuItem>
                  ))}
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <div className="text-center">
        <p className="text-lg text-gray-500">
          Search for your business to see if it's already registered or to add it to our system.
        </p>
      </div>
    </div>
  )
}
