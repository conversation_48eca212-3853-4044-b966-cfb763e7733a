"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function RegisterBusinessPage() {
  const router = useRouter()
  const [businessName, setBusinessName] = useState("")
  const [businessType, setBusinessType] = useState("")
  const [address, setAddress] = useState("")
  const [city, setCity] = useState("")
  const [country, setCountry] = useState("Nigeria")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, this would submit to an API
    router.push("/businesses/success")
  }

  return (
    <div className="mx-auto max-w-3xl">
      <Button onClick={() => router.back()} variant="ghost" className="mb-6 flex items-center gap-2">
        <span>←</span> Back
      </Button>

      <h1 className="mb-8 text-3xl font-bold">Register a New Business</h1>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="businessName">Business Name</Label>
          <Input
            id="businessName"
            value={businessName}
            onChange={(e) => setBusinessName(e.target.value)}
            required
            className="h-12"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="businessType">Business Type</Label>
          <Select value={businessType} onValueChange={setBusinessType}>
            <SelectTrigger className="h-12">
              <SelectValue placeholder="Select business type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="food">Food and Beverage</SelectItem>
              <SelectItem value="retail">Retail</SelectItem>
              <SelectItem value="tech">Technology</SelectItem>
              <SelectItem value="ngo">NGO</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="address">Business Address</Label>
          <Input id="address" value={address} onChange={(e) => setAddress(e.target.value)} required className="h-12" />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="city">City</Label>
            <Input id="city" value={city} onChange={(e) => setCity(e.target.value)} required className="h-12" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="country">Country</Label>
            <Select value={country} onValueChange={setCountry}>
              <SelectTrigger className="h-12">
                <SelectValue placeholder="Select country" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Nigeria">Nigeria</SelectItem>
                <SelectItem value="Ghana">Ghana</SelectItem>
                <SelectItem value="UK">United Kingdom</SelectItem>
                <SelectItem value="Ireland">Ireland</SelectItem>
                <SelectItem value="France">France</SelectItem>
                <SelectItem value="Estonia">Estonia</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Button
          type="submit"
          className="mt-8 w-full bg-green-500 py-6 text-lg font-medium text-white hover:bg-green-600"
        >
          Register Business
        </Button>
      </form>
    </div>
  )
}
