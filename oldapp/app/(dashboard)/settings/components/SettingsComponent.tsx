"use client";
import React, { useState } from "react";
import RecruitmentTeamSettings from "@/components/RecruitmentTeamSettings";
import PaymentSettings from "@/components/PaymentSettings";
import BusinessesSettings from "@/components/BusinessesSettings";
import AccountSettings from "@/components/AccountSettings";

const TABS = [
  { label: "General", value: "general" },
  { label: "Businesses", value: "businesses" },
  { label: "Payment", value: "payment" },
  { label: "Recruitment & Team", value: "recruitment" },
  { label: "Account", value: "account" },
];

const SettingsComponent: React.FC = () => {
  const [activeTab, setActiveTab] = useState("account");

  const renderContent = () => {
    switch (activeTab) {
      case "general":
        return <div>General settings content here</div>;
      case "businesses":
        return <BusinessesSettings />;
      case "payment":
        return <PaymentSettings />;
      case "recruitment":
        return <RecruitmentTeamSettings />;
      case "account":
        return <AccountSettings />;
      default:
        return null;
    }
  };

  return (
    <>
      <div className=" flex items-center justify-between pr-6 pl-4">
        <h1 className="text-lg font-semibold">Settings</h1>
        <div className="flex items-center space-x-4">
          <input
            type="text"
            placeholder="Search settings..."
            className="px-3 py-2 border rounded-md text-sm outline-none focus:ring-2 focus:ring-blue-400"
          />
        </div>
    </div>
    <div className=" pr-6 pl-4">
        <div className="flex space-x-6 border-b border-gray-200 mb-6">
          {TABS.map((tab) => (
            <button
              key={tab.value}
              onClick={() => setActiveTab(tab.value)}
              className={`py-2 px-1 text-sm font-medium border-b-2 ${
                activeTab === tab.value
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700"
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {renderContent()}
      </div>
    </>
  );
};

export default SettingsComponent;
