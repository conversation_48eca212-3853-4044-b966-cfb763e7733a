import React from "react";

const employees = [
  { name: "<PERSON><PERSON><PERSON><PERSON>", role: "Store manager" },
  { name: "<PERSON><PERSON>", role: "Assistant store manager" },
  { name: "<PERSON><PERSON><PERSON>", role: "Loss prevention associate" },
  { name: "<PERSON><PERSON><PERSON>", role: "Stock clerk" },
  { name: "<PERSON>", role: "Cashier" },
  { name: "<PERSON><PERSON>", role: "Cashier" },
  { name: "<PERSON><PERSON><PERSON>", role: "Cashier" },
  { name: "<PERSON><PERSON><PERSON>", role: "Cashier" },
  { name: "<PERSON><PERSON><PERSON>", role: "Security" },
  { name: "<PERSON><PERSON><PERSON>", role: "Security" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", role: "Jan<PERSON>" },
  { name: "<PERSON><PERSON><PERSON>", role: "Janitor" },
  { name: "<PERSON><PERSON><PERSON>", role: "Jan<PERSON>" },
  { name: "Emeka Balogun", role: "Janitor" },
];

const CurrentEmployee: React.FC = () => {
  return (
    <div className="min-h-screen">
      {/* Header Section */}
      <header className="px-8">
        <h1 className="text-2xl font-bold">Hello Samuel</h1>
        <p className="text-gray-600 text-sm mt-2">
          <strong>Market Place</strong> est. 2009 3, Hakeem Dickson, Lekki,
          Lagos
        </p>
      </header>

      {/* Business Management Section */}
      <main className="bg-[#F4F0EC] mx-5 mt-6 p-6 rounded-lg">
        <h2 className="text-center text-lg font-semibold mb-6">
          Market Place Business Management
        </h2>

        <div className="grid grid-cols-2 gap-x-6 gap-y-4">
          {/* Left Column - Employee List */}
          <div>
            <h3 className="text-md font-bold mb-4">Current employees</h3>
            <ul className="space-y-2">
              {employees.map((employee, index) => (
                <li key={index} className="text-sm">
                  {index + 1}. {employee.name}
                </li>
              ))}
            </ul>
          </div>

          {/* Right Column - Roles */}
          <div>
            <h3 className="text-md font-bold mb-4">&nbsp;</h3>
            <ul className="space-y-2">
              {employees.map((employee, index) => (
                <li key={index} className="text-sm">
                  {employee.role}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </main>
    </div>
  );
};

export default CurrentEmployee;
