"use client";

import React, { useState } from "react";
import Link from "next/link";
import { FiInfo } from "react-icons/fi";
// import { BsThreeDots } from "react-icons/bs";
// import { HiBriefcase } from "react-icons/hi";

const BusinessManagement: React.FC = () => {
  const [step, setStep] = useState(1); // Manage steps
  const [formData, setFormData] = useState({});

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const nextStep = () => {
    setStep((prev) => prev + 1);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Final Submitted Data:", formData);
  };

  return (
    <div className="min-h-screen">
      {/* Header Section */}
      <header className="px-8">
        <h1 className="text-2xl font-bold">Hello Samuel</h1>
        <p className="text-gray-600 text-sm mt-2">
          <strong>Market Place</strong> est. 2009 3, Hakeem Dickson, Lekki,
          Lagos
        </p>
      </header>

      {/* Card Section */}
      <div className="mt-6 bg-[#F4F0EC] rounded-lg p-6 mx-5">
        <h2 className="text-lg font-semibold font-poppins text-center mb-4">
          Market Place Business Management
        </h2>

        {/* <div className="grid grid-cols-2 gap-4"> */}
        {/* Current Employees */}
        <Link href="/current-employees" className="block">
          <div className="flex justify-between leading-5 cursor-pointer p-2 rounded text-black">
            <span>Current employees</span>
            <div className="flex items-center gap-2">
              <span>15</span>
              <FiInfo className="text-gray-400" />
            </div>
          </div>
        </Link>

        {/* March Profit */}
        <Link href="/march-profit" className="block">
          <div className="flex justify-between leading-5 cursor-pointer p-2 rounded text-black">
            <span>March 2024 profit</span>
            <div className="flex items-center gap-2">
              <span>₦ 1,245,665.08</span>
              <FiInfo className="text-gray-400" />
            </div>
          </div>
        </Link>

        {/* Recruitment */}
        <div className="col-span-2 flex justify-between leading-5 cursor-pointer p-2 rounded">
          <span>Recruitment</span>
          <ul className="list-disc list-inside">
            <li>Social media manager</li>
            <li>Security guard</li>
          </ul>
        </div>
        {/* </div> */}

        <div className="mt-6">
          <p className="mb-2 text-center font-poppins">
            Sourcing → Screening → Interviewing → Offers Extended → Hired{" "}
            <FiInfo className="inline text-gray-400" />
          </p>

          {/* Sourcing Options */}
          <p className="text-sm text-gray-500 mb-4">
            Sourcing options - LinkedIn, Indeed, Jiji, Jobberman
          </p>

          {/* Screened and Shortlisted Candidates */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="font-medium">Screened candidates</p>
              <p>17</p>
            </div>
            <div>
              <p className="font-medium">Shortlisted candidates</p>
              <p>5</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessManagement;
