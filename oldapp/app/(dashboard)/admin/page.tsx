"use client";

import React from "react";
import Image from "next/image";
import PlusIcon from "@/public/assets/svg/plusIcon.svg";

const DashboardPage: React.FC = () => {
  const handleAddBusiness = () => {
    alert("Redirecting to add business page...");
    // Implement routing logic here
  };
  return (
    <div>
      <div className="flex flex-col items-center justify-center">
        {/* Welcome Message */}
        <h1 className="text-black mb-4 pt-20 font-poppins font-bold text-[70px] leading-[110px]">Welcome, Samuel</h1>
        <p className=" text-black mb-8 pt-20 font-poppins font-normal text-[30px] leading-[60px]">Let’s get started</p>

        {/* Add Business Button */}
        <button
          onClick={handleAddBusiness}
          className="flex items-center mt-20 px-6 py-3 bg-[#01283E] text-white text-[30px] font-semibold rounded-md shadow-md hover:bg-[#014c65] transition-all"
        >
          <Image src={PlusIcon} alt="plus_icon" className="w-10 h-10 mr-2" />
          Add a business
        </button>
      </div>
    </div>
  );
};

export default DashboardPage;
