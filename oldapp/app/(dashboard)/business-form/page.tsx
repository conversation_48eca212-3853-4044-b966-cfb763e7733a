"use client";

import { useState } from "react";
import Link from "next/link";

const BusinessForm = () => {
  const [isNewBusiness, setIsNewBusiness] = useState(false);
  const [sections, setSections] = useState([
    {
      name: "Company Registration",
      progress: 100,
      status: "Submitted",
      link: "/company",
    },
    {
      name: "Director(s) Information",
      progress: 50,
      status: "Not submitted",
      link: "/directors",
    },
    {
      name: "Shareholders Information",
      progress: 0,
      status: "Not submitted",
      link: "/shareholders",
    },
    {
      name: "Secretary Information",
      progress: 0,
      status: "Not submitted",
      link: "/secretary",
    },
  ]);

  // Calculate the total progress
  const totalProgress =
    sections.reduce((acc, section) => acc + section.progress, 0) /
    sections.length;

  return (
    <div className="px-8 min-h-screen">
      {/* Title */}
      {!isNewBusiness && (
        <>
          <div className="flex flex-col items-center justify-center">
            <h1 className="text-center mb-20 font-poppins font-medium text-[30px] leading-[60px]">
              Is your business registered?
            </h1>

            {/* Buttons */}
            <div className="flex space-x-20">
              <button
                onClick={() => setIsNewBusiness(true)}
                className="w-[300px] h-[200px] font-poppins font-semibold text-[20px] leading-[150px] bg-[#D9D9D9] rounded-[10px] text-black"
              >
                New Business
              </button>
              <button className="w-[300px] h-[200px] font-poppins font-semibold text-[20px] leading-[150px] bg-[#D9D9D9] rounded-[10px] text-black">
                Existing Business
              </button>
            </div>
          </div>
        </>
      )}

      {/* Interface after clicking YES */}
      {isNewBusiness && (
        <div className="p-8 max-w-[800px]">
          <p className="text-black font-poppins font-bold text-[25px] leading-[60px]">
            Hello, Samuel
          </p>
          <div className="">
            {/* Progress Bar */}
            <p className="font-poppins font-medium text-[15px] uppercase">
              Progress Bar
            </p>
            <div className="w-full h-2 bg-gray-200 rounded mb-6">
              <div
                className="h-2 bg-blue-500 rounded"
                style={{ width: `${totalProgress}%` }}
              ></div>
            </div>

            {/* Section Table */}
            <div className=" rounded-lg">
              {/* Table Header */}
              <div className="grid grid-cols-3 gap-4 font-bold font-poppins leading-[45px] text-[20px] text-black uppercase p-4">
                <div>Section</div>
                <div>Completion</div>
                <div>Status</div>
              </div>

              {/* Table Rows */}
              {sections.map((section, index) => (
                <Link key={index} href={section.link}>
                  <div
                    className={`cursor-pointer grid grid-cols-3 gap-4 items-center text-sm p-4 ${
                      index % 2 === 0 ? "bg-gray-50" : "bg-gray-100"
                    } hover:bg-gray-200 transition`}
                  >
                    {/* Section Name */}
                    <div className="font-semibold font-poppins leading-[25px] text-[14px] text-black uppercase">{section.name}</div>

                    {/* Progress Bar */}
                    <div>
                      <div className="w-full h-2 bg-gray-200 rounded">
                        <div
                          className={`h-2 ${
                            section.status === "Submitted"
                              ? "bg-green-500"
                              : "bg-blue-500"
                          } rounded`}
                          style={{ width: `${section.progress}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Status */}
                    <div
                      className={`flex items-center gap-2 ${
                        section.status === "Submitted"
                          ? "text-green-600"
                          : "text-red-500"
                      }`}
                    >
                      <span
                        className={`w-3 h-3 rounded-full ${
                          section.status === "Submitted"
                            ? "bg-green-600"
                            : "bg-red-500"
                        }`}
                      ></span>
                      {section.status}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BusinessForm;
