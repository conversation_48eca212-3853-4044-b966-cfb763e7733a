"use client";

import { useState } from "react";

const CompanyRegistration = () => {
  const [step, setStep] = useState(1); // Manage steps
  const [formData, setFormData] = useState({
    email: "",
    phone: "",
    state: "",
    lga: "",
    city: "",
    houseNumber: "",
    street: "",
    postCode: "",
    companyName: "",
    industry: "",
    registrationNumber: "",
    last_name: "",
    first_name: "",
    other_name: "",
    gender: "",
    nationality: "",
    former_name: "",
    former_nationality: "",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const nextStep = () => {
    setStep((prev) => prev + 1);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Final Submitted Data:", formData);
  };

  return (
    <div className="px-6 md:px-12 max-w-6xl mx-auto">
      <h1 className="text-black font-poppins font-bold text-[25px] leading-[60px]">
        Hello Samuel
      </h1>
      <h2 className="mt-4 font-poppins font-semibold text-[20px] leading-[20px]">
        COMPANY REGISTRATION FORM
      </h2>

      {/* Step 1: Entity Details */}
      {step === 1 && (
        <form className="mt-6 space-y-6">
          <div>
            <h3 className="font-medium font-poppins text-[16px] leading-[15px]">
              ENTITY DETAILS
            </h3>
          </div>

          {/* Email Address */}
          <div className="flex items-center">
            <label
              htmlFor="email"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[80%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          {/* Phone Number */}
          <div className="flex items-center">
            <label
              htmlFor="phone"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[80%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          {/* Registered Address */}
          <div>
            <h3 className="font-medium font-poppins text-[16px] leading-[15px]">
              Registered Address
            </h3>
          </div>

          {/* State */}
          <div className="flex items-center">
            <label
              htmlFor="state"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              State
            </label>
            <select
              id="state"
              name="state"
              value={formData.state}
              onChange={handleChange}
              className="w-[80%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            >
              <option value="">Type or select the option</option>
              <option value="State 1">State 1</option>
              <option value="State 2">State 2</option>
              <option value="State 3">State 3</option>
            </select>
          </div>

          {/* LGA */}
          <div className="flex items-center">
            <label
              htmlFor="lga"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              LGA
            </label>
            <select
              id="lga"
              name="lga"
              value={formData.lga}
              onChange={handleChange}
              className="w-[80%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            >
              <option value="">Type or select the option</option>
              <option value="LGA 1">LGA 1</option>
              <option value="LGA 2">LGA 2</option>
              <option value="LGA 3">LGA 3</option>
            </select>
          </div>

          {/* City */}
          <div className="flex items-center">
            <label
              htmlFor="city"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              City/Town/Village
            </label>
            <input
              type="text"
              id="city"
              name="city"
              value={formData.city}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[70%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          {/* House Number */}
          <div className="flex items-center">
            <label
              htmlFor="houseNumber"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              House number/Building Name
            </label>
            <input
              type="text"
              id="houseNumber"
              name="houseNumber"
              value={formData.houseNumber}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[60%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          {/* Street */}
          <div className="flex items-center">
            <label
              htmlFor="street"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Street
            </label>
            <input
              type="text"
              id="street"
              name="street"
              value={formData.street}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[60%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          {/* Post Code */}
          <div className="flex items-center">
            <label
              htmlFor="postCode"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Post Code
            </label>
            <input
              type="text"
              id="postCode"
              name="postCode"
              value={formData.postCode}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[60%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          <button
            type="button"
            onClick={nextStep}
            className="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600"
          >
            Next
          </button>
        </form>
      )}

      {/* Step 2: Object of Memrandum */}
      {step === 2 && (
        <form className="mt-6 space-y-6">
          {/* Field: Object of Memorandum */}
          <section className="mb-8">
            <label
              htmlFor="memorandum"
              className="block text-sm font-medium text-gray-900 mb-2"
            >
              OBJECT OF MEMORANDUM
            </label>
            <p className="text-sm text-gray-500 mb-4">
              i.e the purpose of your company and the scope of its operations
            </p>
            <textarea
              id="memorandum"
              name="memorandum"
              placeholder="Type your answer here"
              className="w-full border border-gray-300 rounded-lg px-4 py-3 text-sm focus:outline-none focus:ring focus:ring-blue-300"
            ></textarea>
          </section>

          <button
            type="button"
            onClick={nextStep}
            className="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600"
          >
            Next
          </button>
        </form>
      )}

      {/* Step 3: Company Information */}
      {step === 3 && (
        <form onSubmit={handleSubmit} className="mt-6 space-y-6">
          {/* Field: Object of Memorandum */}
          <section className="mb-8">
            <label
              htmlFor="memorandum"
              className="block text-sm font-medium text-gray-900 mb-2"
            >
              ARTICLE OF ASSOCIATION
            </label>
            <p className="text-sm text-gray-500 mb-4">
              i.e the purpose of your company and the scope of its operations
            </p>
            <textarea
              id="memorandum"
              name="memorandum"
              placeholder="Type your answer here"
              className="w-full border border-gray-300 rounded-lg px-4 py-3 text-sm focus:outline-none focus:ring focus:ring-blue-300"
            ></textarea>
          </section>

          <button
            type="button"
            onClick={nextStep}
            className="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600"
          >
            Next
          </button>
          {/* <button
            type="submit"
            className="bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600"
          >
            Submit
          </button> */}
        </form>
      )}

      {step === 4 && (
        <form className="mt-6 space-y-6">
          <section className="mb-8">
            <label
              htmlFor="memorandum"
              className="font-medium font-poppins text-[16px] leading-[15px] mb-2"
            >
              WITNESS INFORMATION
            </label>
          </section>
          {/* Email Address */}
          <div className="flex items-center">
            <label
              htmlFor="last_name"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Surname
            </label>
            <input
              type="text"
              id="last_name"
              name="last_name"
              value={formData.last_name}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[60%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          <div className="flex items-center">
            <label
              htmlFor="first_name"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              First Name
            </label>
            <input
              type="text"
              id="first_name"
              name="first_name"
              value={formData.first_name}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[60%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          <div className="flex items-center">
            <label
              htmlFor="other_name"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Other Name
            </label>
            <input
              type="text"
              id="other_name"
              name="other_name"
              value={formData.other_name}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[60%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          <button
            type="button"
            onClick={nextStep}
            className="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600"
          >
            Next
          </button>
        </form>
      )}

      {step === 5 && (
        <form className="mt-6 space-y-6">
          <div>
            <h3 className="font-medium font-poppins text-[16px] leading-[15px]">
              WITNESS INFORMATION
            </h3>
          </div>

          {/* Email Address */}
          <div className="flex items-center">
            <label
              htmlFor="email"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[80%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          {/* Phone Number */}
          <div className="flex items-center">
            <label
              htmlFor="phone"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[80%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          {/* LGA */}
          <div className="flex items-center">
            <label
              htmlFor="gender"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Gender
            </label>
            <select
              id="gender"
              name="gender"
              value={formData.gender}
              onChange={handleChange}
              className="w-[80%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            >
              <option value="">Type or select the option</option>
              <option value="male">Male</option>
              <option value="female">Female</option>
            </select>
          </div>

          {/* City */}
          <div className="flex items-center">
            <label
              htmlFor="dob"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Date of Birth (DD/MM/YYYY)
            </label>
            <input
              type="date"
              id="dob"
              name="dob"
              // value={formData.dob}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[65%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          {/* House Number */}
          <div className="flex items-center">
            <label
              htmlFor="nationality"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Nationality
            </label>
            <input
              type="text"
              id="nationality"
              name="nationality"
              value={formData.nationality}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[80%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          {/* Street */}
          <div className="flex items-center">
            <label
              htmlFor="former_name"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Former Name (if any)
            </label>
            <input
              type="text"
              id="former_name"
              name="former_name"
              value={formData.former_name}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[70%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          {/* Post Code */}
          <div className="flex items-center">
            <label
              htmlFor="former_nationality"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Former Nationality (if any)
            </label>
            <input
              type="text"
              id="former_nationality"
              name="former_nationality"
              value={formData.former_nationality}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[70%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          <button
            type="button"
            onClick={nextStep}
            className="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600"
          >
            Next
          </button>
        </form>
      )}

      {step === 6 && (
        <form className="mt-6 space-y-6">
          <section className="mb-8">
            <label
              htmlFor="memorandum"
              className="block text-sm font-bold text-gray-900 mb-2"
            >
              WITNESS INFORMATION
            </label>
          </section>
          {/* State */}
          <div className="flex items-center">
            <label
              htmlFor="state"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              State
            </label>
            <select
              id="state"
              name="state"
              value={formData.state}
              onChange={handleChange}
              className="w-[80%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            >
              <option value="">Type or select the option</option>
              <option value="State 1">State 1</option>
              <option value="State 2">State 2</option>
              <option value="State 3">State 3</option>
            </select>
          </div>

          {/* LGA */}
          <div className="flex items-center">
            <label
              htmlFor="lga"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              LGA
            </label>
            <select
              id="lga"
              name="lga"
              value={formData.lga}
              onChange={handleChange}
              className="w-[80%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            >
              <option value="">Type or select the option</option>
              <option value="LGA 1">LGA 1</option>
              <option value="LGA 2">LGA 2</option>
              <option value="LGA 3">LGA 3</option>
            </select>
          </div>

          {/* City */}
          <div className="flex items-center">
            <label
              htmlFor="city"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              City/Town/Village
            </label>
            <input
              type="text"
              id="city"
              name="city"
              value={formData.city}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[70%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          {/* House Number */}
          <div className="flex items-center">
            <label
              htmlFor="houseNumber"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              House number/Building Name
            </label>
            <input
              type="text"
              id="houseNumber"
              name="houseNumber"
              value={formData.houseNumber}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[60%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          {/* Street */}
          <div className="flex items-center">
            <label
              htmlFor="street"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Street
            </label>
            <input
              type="text"
              id="street"
              name="street"
              value={formData.street}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[60%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          {/* Post Code */}
          <div className="flex items-center">
            <label
              htmlFor="postCode"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Post Code
            </label>
            <input
              type="text"
              id="postCode"
              name="postCode"
              value={formData.postCode}
              onChange={handleChange}
              placeholder="Type your answer here"
              className="w-[60%] bg-transparent border-b border-black focus:outline-none mt-1"
              required
            />
          </div>

          <div>
            <label
              htmlFor="city"
              className="mr-4 font-normal text-[15px] font-poppins leading-[20px]"
            >
              Signature
            </label>
            <textarea
              id="memorandum"
              name="memorandum"
              placeholder="Type your answer here"
              className="w-full border border-gray-300 rounded-lg px-4 py-3 text-sm focus:outline-none focus:ring focus:ring-blue-300"
            ></textarea>
          </div>

          <button
            type="button"
            onClick={nextStep}
            className="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600"
          >
            Next
          </button>
        </form>
      )}
    </div>
  );
};

export default CompanyRegistration;
