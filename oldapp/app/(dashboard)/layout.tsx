import DashboardHeader from "@/components/DashboardHeader";
import DashboardSidebar from "@/components/DashboardSidebar";
import React, { ReactNode } from "react";
import Link from "next/link";
import { Poppins, Inclusive_Sans, Open_Sans, Inter } from "next/font/google";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-open",
  display: "swap",
  weight: ["400", "500", "600", "700"],
});

const open = Open_Sans({
  subsets: ["latin"],
  variable: "--font-open",
  display: "swap",
  weight: ["400", "500", "600", "700"],
});

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap",
  weight: ["400", "500", "600", "700"],
});

const inclusive = Inclusive_Sans({
  subsets: ["latin"],
  variable: "--font-inclusive",
  display: "swap",
  weight: ["400"],
});


const DashboardLayout = ({ children }: { children: ReactNode }) => {
  return (
    <div className={ `min-h-screen overflow-y-auto flex ${inter.variable} ${poppins.variable} ${inclusive.variable} ${open.variable}`}>
      {/* LEFT */}
      <div className="w-[20%] bg-[#01283E] md:w-[8%] lg:w-[16%] xl:w-[20%] p-4">
        <Link
          href="/"
          className="flex items-center justify-center lg:justify-start gap-2"
        >
          <span className="hidden lg:block font-bold font-poppins text-[white] text-[20px] leading-[34.05px]">VentureDirection</span>
        </Link>
        <DashboardSidebar />
      </div>
      {/* RIGHT */}
      <div className="w-[80%] md:w-[92%] lg:w-[84%] xl:w-[80%] bg-[#F7F8FA] overflow-scroll flex flex-col">
        <DashboardHeader />
        {children}
      </div>
    </div>
  );
};

export default DashboardLayout;
