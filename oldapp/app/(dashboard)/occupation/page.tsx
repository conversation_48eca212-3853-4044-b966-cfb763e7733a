"use client";

import { useState } from "react";

const Home = () => {
  const [isYesClicked, setIsYesClicked] = useState(false);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      {/* Title */}
      {!isYesClicked && (
        <>
          <div className="">
            <h1 className="text-center mb-20 font-poppins font-medium text-[30px] leading-[60px]">
              Is your business registered?
            </h1>

            {/* Buttons */}
            <div className="flex space-x-20">
              <button
                onClick={() => setIsYesClicked(true)}
                className="w-[300px] h-[200px] font-poppins font-semibold text-[70px] leading-[150px] bg-[#D9D9D9] rounded-[10px] text-black"
              >
                YES
              </button>
              <button className="w-[300px] h-[200px] font-poppins font-semibold text-[70px] leading-[150px] bg-[#D9D9D9] rounded-[10px] text-black">
                NO
              </button>
            </div>
          </div>
        </>
      )}

      {/* Interface after clicking YES */}
      {isYesClicked && (
        <div className="flex flex-col items-center space-y-4">
          <div className="">
            <h1 className="text-center mb-10 font-poppins font-medium text-[25px] leading-[60px]">
              Basic Business Information
            </h1>
            <p className="text-center mb-20 font-poppins font-normal text-[18px] ">
              What is the name of your business?
            </p>
            <div className="flex items-center w-full max-w-[510px] h-[60px] mx-auto rounded-[15px] border border-black bg-[#F4F0EC]">
              <input
                type="text"
                className="w-64 p-2 border-none outline-none bg-transparent"
              />
              <button className="mr-[2.5px] px-3 py-4 bg-[#009A49] font-poppins font-medium text-[14px] text-white rounded-[15px] border border-[#1a1a1a]">
                Search CAC registry
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Home;
