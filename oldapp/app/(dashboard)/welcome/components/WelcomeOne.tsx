import React from 'react';
import Link from 'next/link';
import Image, { StaticImageData } from 'next/image';

import ShoppingCartIcon from '@/public/assets/svg/shoppingIcon.svg';
import CarIcon from '@/public/assets/svg/carIcon.svg';
import TVIcon from '@/public/assets/svg/computerIcon.svg';

type Business = {
  name: string;
  icon: StaticImageData; 
  flags: string[];
  link: string;
};

const businesses: Business[] = [
  {
    name: 'Market Place',
    icon: ShoppingCartIcon,
    flags: ['🇬🇭', '🇳🇬'],
    link: '/marketplace', 
  },
  {
    name: "Sammy's Rental Service",
    icon: CarIcon,
    flags: ['🇳🇬'],
    link: '/rental-service',
  },
  {
    name: "Sammy's Electronics",
    icon: TVIcon,
    flags: ['🇳🇬'],
    link: '/electronics',
  },
];

const WelcomeOne: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center">
      <h1 className="mb-4 font-poppins font-bold text-[40px] leading-[60px]">Welcome, <PERSON></h1>
      <p className="font-poppins font-normal text-[25px] leading-[60px] mb-12">Which business would you like to manage today?</p>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {businesses.map((business, index) => (
          <Link key={index} href={business.link}>
            <div className="flex flex-col items-center justify-center bg-[#F4F0EC] rounded-lg p-6 hover:scale-105 transition-transform cursor-pointer">
              <div className="flex w-full justify-end space-x-1 mb-2">
                {business.flags.map((flag, idx) => (
                  <span key={idx} className="text-2xl">
                    {flag}
                  </span>
                ))}
              </div>
              <div className="text-gray-700 mb-4">
                <Image src={business.icon} alt={business.name} width={100} height={100} />
              </div>
              <h2 className="text-[black] font-poppins font-bold text-[17px] leading-[60px]">{business.name}</h2>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default WelcomeOne;
