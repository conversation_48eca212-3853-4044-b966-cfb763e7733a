"use client";
import { useState } from "react";
import { FaPlus, FaCamera } from "react-icons/fa";
import { FiUpload, FiSmartphone } from "react-icons/fi";
import { BsPencilFill } from "react-icons/bs";

const Secretary = () => {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    surname: "",
    first_name: "",
    email: "",
    phone: "",
    nationality: "",
    formerNationality: "",
    formerName: "",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const nextStep = () => {
    setStep((prev) => prev + 1);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Final Submitted Data:", formData);
  };

  return (
    <div className="px-6 md:px-12 max-w-6xl mx-auto">
      <h1 className="text-black font-poppins font-bold text-[25px] leading-[60px]">
        Hello Samuel
      </h1>

      {/* Navigation Tabs */}
      <div className="flex space-x-4 mt-6">
        {[
          "Company Registration",
          "Director’s Info",
          "Shareholder’s Info",
          "Secretary’s Info",
        ].map((tab, index) => (
          <div key={index}>
            <button
              className={`px-4 py-2 font-poppins font-semibold text-[14px] leading-[20px] uppercase rounded-md ${
                index < 2
                  ? "bg-[#d9d9d9] text-black"
                  : "bg-gray-300 text-gray-600"
              }`}
            >
              {tab}
            </button>
            {index < 2 && <div className="h-1 bg-green-500 w-full mt-1"></div>}
          </div>
        ))}
      </div>

      <h2 className="mt-10 font-poppins font-semibold text-center text-[20px] uppercase leading-[20px]">
        Secretary Information
      </h2>

      {/* Step 1 */}
      {step === 1 && (
        <div className="mt-6 space-y-6">
          <div className="grid grid-cols-2 gap-4">
            {/* Surname */}
            <div>
              <label
                htmlFor="surname"
                className="block font-poppins font-normal text-[15px] leading-[20px] text-[#000000]"
              >
                Surname
              </label>
              <input
                id="surname"
                name="surname"
                type="text"
                className="p-2 border h-[40px] border-[#d9d9d9] rounded w-full"
                onChange={handleChange}
              />
            </div>

            {/* First Name */}
            <div>
              <label
                htmlFor="firstName"
                className="block font-poppins font-normal text-[15px] leading-[20px] text-[#000000]"
              >
                First Name, Other Name
              </label>
              <input
                id="firstName"
                name="firstName"
                type="text"
                className="p-2 border h-[40px] border-[#d9d9d9] rounded w-full"
                onChange={handleChange}
              />
            </div>

            {/* Email */}
            <div>
              <label
                htmlFor="email"
                className="block font-poppins font-normal text-[15px] leading-[20px] text-[#000000]"
              >
                Email Address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                className="p-2 border h-[40px] border-[#d9d9d9] rounded w-full"
                onChange={handleChange}
              />
            </div>

            {/* Phone Number */}
            <div>
              <label
                htmlFor="phone"
                className="block font-poppins font-normal text-[15px] leading-[20px] text-[#000000]"
              >
                Phone Number
              </label>
              <input
                id="phone"
                name="phone"
                type="tel"
                className="p-2 border h-[40px] border-[#d9d9d9] rounded w-full"
                onChange={handleChange}
              />
            </div>

            {/* Date of Birth */}
            <div>
              <label
                htmlFor="dob"
                className="block font-poppins font-normal text-[15px] leading-[20px] text-[#000000]"
              >
                Date of Birth (DD/MM/YYYY)
              </label>
              <input
                id="dob"
                name="dob"
                type="text"
                className="p-2 border h-[40px] border-[#d9d9d9] rounded w-full"
                onChange={handleChange}
              />
            </div>

            {/* Select an Option */}
            <div>
              <label
                htmlFor="gener"
                className="block font-poppins font-normal text-[15px] leading-[20px] text-[#000000]"
              >
                Gender
              </label>
              <select
                id="option"
                name="option"
                className="p-2 border h-[40px] border-[#d9d9d9] rounded w-full"
                onChange={handleChange}
              >
                <option
                  value=""
                  className="font-poppins font-normal text-[15px] leading-[20px] text-[#d9d9d9]"
                >
                  Select an option
                </option>
                <option value="option1">Option 1</option>
              </select>
            </div>

            {/* Nationality */}
            <div>
              <label
                htmlFor="nationality"
                className="block font-poppins font-normal text-[15px] leading-[20px] text-[#000000]"
              >
                Nationality
              </label>
              <select
                id="nationality"
                name="nationality"
                className="p-2 border h-[40px] border-[#d9d9d9] rounded w-full"
                onChange={handleChange}
              >
                <option value="">Select nationality</option>
                <option value="nigerian">Nigerian</option>
              </select>
            </div>

            {/* Former Name */}
            <div>
              <label
                htmlFor="formerName"
                className="block font-poppins font-normal text-[15px] leading-[20px] text-[#000000]"
              >
                Former Name (if any)
              </label>
              <input
                id="formerName"
                name="formerName"
                type="text"
                className="p-2 border h-[40px] border-[#d9d9d9] rounded w-full"
                onChange={handleChange}
              />
            </div>

            {/* Former Nationality */}
            <div>
              <label
                htmlFor="formerNationality"
                className="block font-poppins font-normal text-[15px] leading-[20px] text-[#000000]"
              >
                Former Nationality (if any)
              </label>
              <select
                id="formerNationality"
                name="formerNationality"
                className="p-2 border h-[40px] border-[#d9d9d9] rounded w-full"
                onChange={handleChange}
              >
                <option
                  value=""
                  className="font-poppins font-normal text-[15px] leading-[20px] text-[#d9d9d9]"
                >
                  Select former nationality
                </option>
                <option value="british">British</option>
              </select>
            </div>
          </div>

          {/* Next Button */}
          <button
            type="button"
            onClick={nextStep}
            className="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600"
          >
            Next
          </button>
        </div>
      )}

      {/* Step 2 */}
      {step === 2 && (
        <div>
          <h2 className="my-5 font-poppins font-normal text-[18px] uppercase leading-[20px]">
            CONTACT DETAILS
          </h2>
          <div className="grid grid-cols-2 gap-4">
            {/* Email */}
            <div>
              <label
                htmlFor="email"
                className="block my-2 font-poppins font-normal text-[15px] leading-[20px] text-[#000000]"
              >
                Email Address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                className="p-2 border bg-[#E7E5EB] h-[40px] border-[#d9d9d9] rounded w-full"
                onChange={handleChange}
              />
            </div>

            {/* Phone Number */}
            <div>
              <label
                htmlFor="phone"
                className="block my-2 font-poppins font-normal text-[15px] leading-[20px] text-[#000000]"
              >
                Phone Number
              </label>
              <input
                id="phone"
                name="phone"
                type="tel"
                className="p-2 border bg-[#E7E5EB] h-[40px] border-[#d9d9d9] rounded w-full"
                onChange={handleChange}
              />
            </div>
          </div>
          <div>
            <label
              htmlFor="phone"
              className="block my-2 font-poppins font-normal text-[15px] leading-[20px] text-[#000000]"
            >
              Street Address
            </label>
            <input
              id="phone"
              name="phone"
              type="tel"
              className="p-2 border bg-[#E7E5EB] h-[40px] border-[#d9d9d9] rounded w-full"
              onChange={handleChange}
            />
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block my-2 font-poppins font-normal text-[15px] leading-[20px] text-[#000000]">
                City/Town/Village
              </label>
              <input
                type="text"
                className="p-2 border bg-[#E7E5EB] h-[40px] border-[#d9d9d9] rounded w-full"
                onChange={handleChange}
              />
            </div>
            <div>
              <label className="block my-2 font-poppins font-normal text-[15px] leading-[20px] text-[#000000]">
                State/Province
              </label>
              <input
                type="text"
                className="p-2 border bg-[#E7E5EB] h-[40px] border-[#d9d9d9] rounded w-full"
                onChange={handleChange}
              />
            </div>
            <div>
              <label className="block my-2 font-poppins font-normal text-[15px] leading-[20px] text-[#000000]">
                Post Code
              </label>
              <input
                type="text"
                className="p-2 border bg-[#E7E5EB] h-[40px] border-[#d9d9d9] rounded w-full"
                onChange={handleChange}
              />
            </div>
          </div>
          <div>
            <label className="block my-2 font-poppins font-normal text-[15px] leading-[20px] text-[#000000]">
              Signature
            </label>
            {/* <div className="w-full h-20 text-center border rounded bg-gray-200 flex items-center justify-center text-gray-500">
              Sign here
            </div> */}
            <textarea
              name=""
              id=""
              cols={30}
              className="p-2 border text-center text-[14px] text-[#c1c0c4] bg-[#E7E5EB]  border-[#d9d9d9] rounded w-full"
            >
              Sign here
            </textarea>
          </div>

          {/* Next Button */}
          <button
            type="button"
            onClick={nextStep}
            className="bg-blue-500 mt-3 text-white py-2 px-4 rounded hover:bg-blue-600"
          >
            Next
          </button>
        </div>
      )}

      {step === 3 && (
        <div>
          <div className="mt-4 grid grid-cols-2 gap-4">
            <div>
              <label className="block my-2 font-poppins font-normal text-[15px] leading-[20px] text-[#000000]">
                Full Name
              </label>
              <input type="text" className="w-full border p-2 rounded mt-1" />
              <div className="mt-2 w-full border p-2 rounded bg-gray-100 italic text-gray-600">
                e.g. Tobi Musa Okonkwo
              </div>
            </div>
            <div>
              <label className="block my-2 font-poppins font-normal text-[15px] leading-[20px] text-[#000000]">
                Initials
              </label>
              <input type="text" className="w-full border p-2 rounded mt-1" />
              <div className="mt-2 w-full border p-2 rounded bg-gray-100 italic text-gray-600">
                e.g. T.M.O
              </div>
            </div>
          </div>

          {/* Divider */}
          <div className="my-6 flex items-center">
            <div className="flex-1 border-t border-gray-300"></div>
            <span className="mx-4 text-gray-500">or</span>
            <div className="flex-1 border-t border-gray-300"></div>
          </div>

          {/* Signature Options */}
          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg bg-gray-100 flex items-center gap-3">
              <BsPencilFill size={24} />
              <div>
                <p className="font-semibold">Draw signature</p>
                <p className="text-sm text-gray-600">
                  Draw your signature using your device.
                </p>
              </div>
            </div>
            <div className="p-4 border rounded-lg bg-gray-100 flex items-center gap-3">
              <FiUpload size={24} />
              <div>
                <p className="font-semibold">Upload signature</p>
                <p className="text-sm text-gray-600">
                  Upload an image of your signature.
                </p>
              </div>
            </div>
            <div className="p-4 border rounded-lg bg-gray-100 flex items-center gap-3">
              <FaCamera size={24} />
              <div>
                <p className="font-semibold">Capture signature</p>
                <p className="text-sm text-gray-600">
                  Capture your signature with a web camera.
                </p>
              </div>
            </div>
            <div className="p-4 border rounded-lg bg-gray-100 flex items-center gap-3">
              <FiSmartphone size={24} />
              <div>
                <p className="font-semibold">Sign via mobile device</p>
                <p className="text-sm text-gray-600">
                  Sign in on a mobile device to continue signing.
                </p>
              </div>
            </div>
          </div>
          {/* Done Button */}
          <div className="my-6 text-right">
            <button className="px-4 py-2 bg-[#d9d9d9] font-poppins font-normal text-[15px] leading-[20px] text-[#000000] rounded-md">
              Done
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Secretary;
