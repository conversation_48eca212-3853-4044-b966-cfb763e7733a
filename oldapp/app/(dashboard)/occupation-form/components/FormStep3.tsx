import Input from 'postcss/lib/input';
import React, { useState } from 'react';

const FormStep3: React.FC = () => {
  const [hasEmployees, setHasEmployees] = useState<boolean | null>(null);
  const [complianceTasks, setComplianceTasks] = useState<string>('');

  const handleEmployeeChange = (value: boolean) => {
    setHasEmployees(value);
  };

  return (
    <div className="max-w-xl">
      {/* Header */}
      <h2 className="text-black text-center font-poppins font-medium text-[30px] leading-[60px] mb-4">Operational Details</h2>

      {/* Question 7 */}
      <div className="mb-6">
        <label className="block font-poppins font-normal text-[20px] text-black mb-2">
          7. Do you currently have employees or plan to hire?
        </label>
        <div className="flex space-x-6">
          <label className="px-5 flex items-center space-x-4">
            <input
              type="checkbox"
              name="employees"
              checked={hasEmployees === true}
              onChange={() => handleEmployeeChange(true)}
              className="w-5 h-5 rounded-[5px] border border-black bg-[#F4F0EC]"
            />
            <span className='font-poppins font-normal text-[15px] text-black'>Yes</span>
          </label>
          <label className="flex items-center space-x-4">
            <input
              type="checkbox"
              name="employees"
              checked={hasEmployees === false}
              onChange={() => handleEmployeeChange(false)}
              className="w-5 h-5 rounded-[5px] border border-black bg-[#F4F0EC]"
            />
            <span className='font-poppins font-normal text-[15px] text-black'>No</span>
          </label>
        </div>
      </div>

      {/* Question 8 */}
      <div className="mb-6">
        <label className="block font-poppins font-normal text-[20px] text-black mb-2">
          8. Are there specific compliance tasks you need help managing? <br />
          <span className="text-sm text-gray-500">e.g., Tax filing, license renewal, or annual reporting</span>
        </label>
        <input
          className="w-full px-4 py-2 focus:outline-none rounded-[15px] border border-black bg-[#F4F0EC]"
            placeholder=""
          value={complianceTasks}
          onChange={(e) => setComplianceTasks(e.target.value)}
        />
      </div>
    </div>
  );
};

export default FormStep3;

