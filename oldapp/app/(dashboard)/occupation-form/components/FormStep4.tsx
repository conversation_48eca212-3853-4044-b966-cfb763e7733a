import React, { useState } from "react";

const FormStep4: React.FC = () => {
  const [hasEmployees, setHasEmployees] = useState<boolean | null>(null);

  const handleEmployeeChange = (value: boolean) => {
    setHasEmployees(value);
  };
  return (
    <div className="max-w-xl">
      {/* Header */}
      <h2 className="text-black text-center font-poppins font-medium text-[25px] leading-[60px] mb-4">
        Existing Business Information (if required)
      </h2>

      {/* Question 9 */}
      <div className="mb-6">
        <label className="block font-poppins font-normal text-[20px] text-black mb-2">
          9. Is your business already registered?
        </label>
        <div className="flex space-x-6">
          <label className="px-5 flex items-center space-x-4">
            <input
              type="checkbox"
              name="employees"
              checked={hasEmployees === true}
              onChange={() => handleEmployeeChange(true)}
              className="w-5 h-5 rounded-[5px] border border-black bg-[#F4F0EC]"
            />
            <span className="font-poppins font-normal text-[15px] text-black">
              Yes
            </span>
          </label>
          <label className="flex items-center space-x-4">
            <input
              type="checkbox"
              name="employees"
              checked={hasEmployees === false}
              onChange={() => handleEmployeeChange(false)}
              className="w-5 h-5 rounded-[5px] border border-black bg-[#F4F0EC]"
            />
            <span className="font-poppins font-normal text-[15px] text-black">
              No
            </span>
          </label>
        </div>
      </div>

      {/* Question 10 */}
      <div className="mb-6">
        <label className="block font-poppins font-normal text-[20px] text-black mb-2">
          10. Do you have existing compliance documents?
        </label>
        <div className="flex space-x-6">
          <label className="px-5 flex items-center space-x-4">
            <input
              type="checkbox"
              name="employees"
              checked={hasEmployees === true}
              onChange={() => handleEmployeeChange(true)}
              className="w-5 h-5 rounded-[5px] border border-black bg-[#F4F0EC]"
            />
            <span className="font-poppins font-normal text-[15px] text-black">
              Yes
            </span>
          </label>
          <label className="flex items-center space-x-4">
            <input
              type="checkbox"
              name="employees"
              checked={hasEmployees === false}
              onChange={() => handleEmployeeChange(false)}
              className="w-5 h-5 rounded-[5px] border border-black bg-[#F4F0EC]"
            />
            <span className="font-poppins font-normal text-[15px] text-black">
              No
            </span>
          </label>
        </div>
      </div>
    </div>
  );
};

export default FormStep4;
