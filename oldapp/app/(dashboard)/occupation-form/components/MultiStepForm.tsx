"use client";

import React, { useState } from "react";
import StepProgress from "./StepProgress";
import FormStep1 from "./FormStep1";
import FormStep2 from "./FormStep2";
import FormStep3 from "./FormStep3";
import FormStep4 from "./FormStep4";
import FormStep5 from "./FormStep5";

const MultiStepForm = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 5;

  const nextStep = () => {
    if (currentStep < totalSteps) setCurrentStep(currentStep + 1);
  };

  const previousStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <FormStep1 />;
      case 2:
        return <FormStep2 />;
      case 3:
        return <FormStep3 />;
      case 4:
        return <FormStep4 />;
      case 5:
        return <FormStep5 />;
      default:
        return <FormStep1 />;
    }
  };

  return (
    <div className="flex flex-col items-center justify-center">
      <div className="w-full max-w-[50rem]">
        {/* Progress Bar */}
        <StepProgress currentStep={currentStep} totalSteps={totalSteps} />

        {/* Form Content */}
        {renderStep()}

        {/* Navigation Buttons */}
        <div className="flex justify-evenly mt-8">
          <button
            onClick={previousStep}
            disabled={currentStep === 1}
            className={`px-3 py-2 text-black font-poppins font-normal text-[15px] bg-[#D9D9D9] rounded ${
              currentStep === 1 ? " cursor-not-allowed" : ""
            }`}
          >
            Previous
          </button>
          <button
            onClick={nextStep}
            disabled={currentStep === totalSteps}
            className={`px-3 py-2 text-black font-poppins font-normal text-[15px] bg-[#D9D9D9] rounded ${
              currentStep === totalSteps ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default MultiStepForm;
