import React from "react";

const FormStep1: React.FC = () => {
  return (
    <div className="w-full max-w-xl">
      <h1 className="text-black text-center font-poppins font-medium text-[30px] leading-[60px] mb-2">
        Basic Business Information
      </h1>
      <form className="space-y-6">
        {/* Question 1 */}
        <div>
          <label className="block font-poppins font-normal text-[20px] text-black mb-2">
            1. What is the name of your business?
          </label>
          <input
            type="text"
            className="w-full px-4 py-2 focus:outline-none rounded-[15px] border border-black bg-[#F4F0EC]"
            placeholder=""
          />
        </div>

        {/* Question 2 */}
        <div>
          <label className="block font-poppins font-normal text-[20px] text-black mb-2">
            2. What type of business do you run?
          </label>
          <select className="w-full px-4 py-2 focus:outline-none rounded-[15px] border border-black bg-[#F4F0EC]">
            <option value="">Select an option</option>
            <option value="sole-proprietorship">Sole Proprietorship</option>
            <option value="partnership">Partnership</option>
            <option value="corporation">Corporation</option>
            <option value="llc">LLC</option>
          </select>
        </div>

        {/* Question 3 */}
        <div>
          <label className="block font-poppins font-normal text-[20px] text-black mb-2">
            3. What industry does your business operate in?
          </label>
          <input
            type="text"
            className="w-full px-4 py-2 focus:outline-none rounded-[15px] border border-black bg-[#F4F0EC]"
            placeholder="e.g., Retail, Tech, Fashion, Consulting, etc"
          />
        </div>

        {/* Question 4 */}
        <div>
          <label className="block font-poppins font-normal text-[20px] text-black mb-2">
            4. Where is your business based?
          </label>
          <input
            type="text"
            className="w-full px-4 py-2 focus:outline-none rounded-[15px] border border-black bg-[#F4F0EC]"
            placeholder=""
          />
        </div>
      </form>
    </div>
  );
};

export default FormStep1;
