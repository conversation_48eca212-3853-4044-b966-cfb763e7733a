import React, { useState } from "react";

const FormStep5: React.FC = () => {
  const [businessStage, setBusinessStage] = useState<string>("");
  const [selectedGoals, setSelectedGoals] = useState<string[]>([]);

  const goals = [
    "Business Registration",
    "Compliance Tracking",
    "Growth Management",
    "Recruitment",
    "Document Storage",
  ];

  const toggleGoal = (goal: string) => {
    setSelectedGoals((prevGoals) =>
      prevGoals.includes(goal)
        ? prevGoals.filter((g) => g !== goal)
        : [...prevGoals, goal]
    );
  };

  return (
    <div className="w-full max-w-xl ">
      <h2 className="text-black text-center font-poppins font-medium text-[30px] leading-[60px] mb-4">
        Customization & Preferences
      </h2>
      {/* Business Goals Checkboxes */}
      <div className="mb-6">
        <label className="block font-poppins font-normal text-[20px] text-black mb-4">
          11. What would you like to see on your dashboard? (Select
          one or more)
        </label>
        <div className="grid grid-cols-2 gap-4">
          {goals.map((goal) => (
            <label
              key={goal}
              className="flex items-center space-x-4 text-[14px] font-poppins font-normal text-black leading-[20px]"
            >
              <input
                type="checkbox"
                value={goal}
                checked={selectedGoals.includes(goal)}
                onChange={() => toggleGoal(goal)}
                className="w-5 h-5 rounded-[5px] border border-black bg-[#F4F0EC]"
              />
              <span>{goal}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Business Stage Dropdown */}
      <div className="mb-6">
        <label className="block font-poppins font-normal text-[20px] text-black mb-2">
          12. How often would you like compliance reminders?
        </label>
        <select
          className="w-full px-4 py-2 focus:outline-none rounded-[15px] border border-black bg-[#F4F0EC]"
          value={businessStage}
          onChange={(e) => setBusinessStage(e.target.value)}
        >
          <option value="">Select an option</option>
          <option value="Startup">Daily</option>
          <option value="Growth">Weekly</option>
          <option value="Established">Monthly</option>
        </select>
      </div>
    </div>
  );
};

export default FormStep5;
