interface StepProgressProps {
    currentStep: number;
    totalSteps: number;
  }
  
  const StepProgress: React.FC<StepProgressProps> = ({ currentStep, totalSteps }) => {
    const progress = (currentStep / totalSteps) * 100;
  
    return (
      <div className="relative">
        <p className="font-poppins font-medium text-[15px] uppercase">Progress Bar</p>
        <div className="w-full h-2 bg-gray-300 rounded-full mb-3"> 
          <div
            className="h-2  rounded-full"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>
    );
  };
  
  export default StepProgress;
  