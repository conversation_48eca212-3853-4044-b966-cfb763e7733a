import React from "react";
import Link from "next/link";
import { FaCheckCircle } from "react-icons/fa";
import { BsThreeDots } from "react-icons/bs";
import { HiBriefcase } from "react-icons/hi";

const MarketPlaceOne: React.FC = () => {
  return (
    <div className="min-h-screen">
      {/* Header Section */}
      <header className="px-8 py-6">
        <h1 className="text-2xl font-bold">Hello Samuel</h1>
        <p className="text-gray-600 text-sm mt-2">
          <strong>Market Place</strong> est. 2009 3, Hakeem Dickson, Lekki,
          Lagos
        </p>
      </header>

      {/* Content Section */}
      {/* <main className=""> */}
      {/* <div className="grid w-[30%] gap-6"> */}

      <div className="flex justify-between px-8 py-4">
        {/* Register Business Card */}
        <div className="w-[30%]">
          <div className=" p-6 rounded-[15px] bg-[#F4F0EC] flex flex-col items-center text-center">
            <div className=" p-4 rounded-full mb-4">
              <HiBriefcase className="text-primary text-4xl" />
            </div>
            <h2 className="text-lg font-semibold mb-2">Register Business</h2>
            <Link href="business-form" className="text-gray-600 text-sm">
              Click here to register new and existing businesses
            </Link>
          </div>
        </div>

        {/* Business Management */}
        <div className="w-[60%]">
          <Link href="/business-management">
            <div className="bg-[#F4F0EC] p-6 rounded-[15px] ">
              <h2 className="text-lg font-semibold text-black mb-4">
                Market Place Business Management
              </h2>
              <div className="text-sm space-y-2 text-black">
                <p className="flex justify-between">
                  <span>Current employees</span>
                  <span>15</span>
                </p>
                <p className="flex justify-between">
                  <span>March 2024 profit</span>
                  <span>₦1,245,665.08</span>
                </p>
                <p className="flex justify-between">
                  <span>Recruitment</span>
                  <span>Social media manager, Security guard</span>
                </p>
              </div>
              <div className="text-sm text-gray-600 mt-6">
                Sourcing → Screening →{" "}
                <span className="text-primary font-semibold underline">
                  Interviewing
                </span>{" "}
                → Offers Extended → Hired
              </div>
            </div>
          </Link>
        </div>
      </div>

      <div className="flex justify-between px-8 py-4">
        {/* Documents */}
        <div className="w-[30%]">
          <Link href="documents">
            <div className="bg-[#F4F0EC] p-6 rounded-[15px]">
              <h2 className="text-lg font-semibold mb-4">Documents & Notes</h2>
              <ul className="text-sm space-y-3">
                {[
                  "Annual confirmation status",
                  "Certification of Incorporation",
                  "VAT registration",
                  "NAFDAC license",
                  "Statutory declaration",
                ].map((doc, index) => (
                  <li key={index} className="flex justify-between">
                    <span>{doc}</span>
                    <FaCheckCircle className="text-green-500" />
                  </li>
                ))}
              </ul>
            </div>
          </Link>
        </div>

        {/* Business Planner */}
        <div className="w-[60%]">
          <div className="bg-[#F4F0EC] p-6 rounded-[15px] ">
            <h2 className="text-lg font-semibold mb-4">Business Planner</h2>
            <div className="grid grid-cols-3 gap-4">
              {/* Planner Notes */}
              <div className="p-2 bg-gray-200 rounded-md text-sm">
                <strong>Expansion Idea</strong>
                <p>
                  Speak to Bimbo about leasing a space in Ikoyi. Talk to her
                  about foot traffic in Awolowo...
                </p>
              </div>
              <div className="p-2 bg-gray-200 rounded-md text-sm">
                <strong>Growing Competition</strong>
                <ul className="list-disc pl-4">
                  <li>Ebeano</li>
                  <li>Daytona</li>
                  <li>BG Mart</li>
                </ul>
              </div>
              <div className="p-2 bg-gray-200 rounded-md text-sm text-center flex items-center justify-center">
                <span>February 2024 Profit</span>
              </div>
            </div>
            <div className="mt-6 text-center">
              <BsThreeDots className="text-gray-500 mx-auto text-xl" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarketPlaceOne;
