import React from "react";

const profits = [
  { time: "27th, 8:55pm", amount: "₦1,245,665.08" },
  { time: "27th, 8:30pm", amount: "₦10,200.00" },
  { time: "27th, 8:25pm", amount: "₦24,566.50" },
  { time: "27th, 8:09pm", amount: "₦72,100.00" },
  { time: "27th, 8:02pm", amount: "₦11,770.25" },
  { time: "27th, 8:02pm", amount: "₦1,250.00" },
  { time: "27th, 7:50pm", amount: "₦31,250.00" },
  { time: "27th, 7:37pm", amount: "₦6705.00" },
  { time: "27th, 7:30pm", amount: "₦221,500.75" },
];

const MarchProfit: React.FC = () => {
  return (
    <div className="min-h-screen">
      {/* Header Section */}
      <header className="px-8">
        <h1 className="text-2xl font-bold">Hello <PERSON></h1>
        <p className="text-gray-600 text-sm mt-2">
          <strong>Market Place</strong> est. 2009 3, Hakeem <PERSON>, Lekki,
          Lagos
        </p>
      </header>

      {/* Business Management Section */}
      <main className="bg-[#F4F0EC] mx-5 mt-6 p-6 rounded-lg">
        <h2 className="text-center text-lg font-semibold mb-6">
          Market Place Business Management
        </h2>

        <div className="grid grid-cols-2 gap-x-6 gap-y-4">
          {/* Left Column - Date/Time */}
          <div>
            <h3 className="text-md font-bold mb-4">March 2024 profit</h3>
            <ul className="space-y-2">
              {profits.map((profit, index) => (
                <li key={index} className="text-sm">
                  {profit.time}
                </li>
              ))}
            </ul>
          </div>

          {/* Right Column - Amount */}
          <div>
            <h3 className="text-md font-bold mb-4">&nbsp;</h3>
            <ul className="space-y-2">
              {profits.map((profit, index) => (
                <li key={index} className="text-sm text-right">
                  {profit.amount}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </main>
    </div>
  );
};

export default MarchProfit;
