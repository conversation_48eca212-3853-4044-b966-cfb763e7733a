"use client";

import React, { useState } from "react";
import Document1 from "@/public/assets/svg/document1.svg";
import Document2 from "@/public/assets/svg/document2.svg";
import Image from "next/image";

interface UploadedDocument {
  name: string;
  date: string;
  file: File;
}

const Documents: React.FC = () => {
  const [uploadedDocs, setUploadedDocs] = useState<UploadedDocument[]>([]);

  // Handle File Upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];

      const newDoc: UploadedDocument = {
        name: file.name,
        date: new Date().toLocaleDateString(),
        file,
      };

      setUploadedDocs([...uploadedDocs, newDoc]);
    }
  };

  return (
    <div className="min-h-screen">
      {/* Header Section */}
      <header className="px-8 py-6">
        <h1 className="text-2xl font-bold">Hello Samuel</h1>
        <p className="text-gray-600 text-sm mt-2">
          <strong>Market Place</strong> est. 2009 3, Hakeem Dickson, Lekki, Lagos
        </p>
      </header>

      {/* Documents Section */}
      <div className="px-8 pb-6">
        <h3 className="font-poppins font-bold text-[20px] text-center text-black mb-4">
          Documents
        </h3>
        <div className="bg-[#F4F0EC] p-6 rounded-lg">
          {/* Document Cards */}
          <div className="flex flex-wrap">
            {/* Existing Documents */}
            <div className="w-[20%] p-1">
              <div className="bg-white">
                <Image src={Document1} alt="Current Employment" />
              </div>
              <p className="font-poppins font-normal text-[12px] text-black">
                Current Employment
              </p>
              <p className="font-poppins font-normal text-[12px] text-black">
                27/01/2024
              </p>
            </div>

            <div className="w-[20%] p-1">
              <div className="bg-white">
                <Image src={Document2} alt="Property Management" className="mb-2" />
              </div>
              <p className="font-poppins font-normal text-[12px] text-black">
                Property Management
              </p>
              <p className="font-poppins font-normal text-[12px] text-black">
                02/02/2024
              </p>
            </div>

            {/* Uploaded Documents */}
            {uploadedDocs.map((doc, index) => (
              <div key={index} className="w-[20%] p-1">
                <div className="bg-white p-2">
                  <p className="font-poppins font-normal text-[12px] text-black truncate">
                    {doc.name}
                  </p>
                  <p className="font-poppins font-normal text-[12px] text-black">
                    {doc.date}
                  </p>
                </div>
              </div>
            ))}

            {/* Upload Button */}
            <div className="w-[20%] p-1">
              <label htmlFor="fileUpload" className="cursor-pointer">
                <div className="bg-white p-10 flex justify-center items-center">
                  <span className="font-poppins font-normal text-[12px] text-black">
                    + Add a document
                  </span>
                </div>
              </label>
              <input
                id="fileUpload"
                type="file"
                className="hidden"
                onChange={handleFileUpload}
                accept=".pdf,.doc,.docx,.jpg,.png"
              />
            </div>

            {/* Notepad Section */}
            <div className="w-[20%] p-2">
              <div className="bg-white pt-4 px-4 pb-10">
                <h4 className="font-poppins font-normal text-[16px] text-black">
                  Notepad
                </h4>
                <textarea
                  placeholder="What's your idea?"
                  className="w-full border-gray-300 rounded-lg mt-2 font-poppins font-normal text-[12px] text-black"
                />
              </div>
            </div>
          </div>

          {/* Notepad Section */}
          <div className="flex flex-wrap">
            <div className="w-[70%] bg-white p-1"></div>

            <div className="w-[10%]"></div>

            <div className="w-[20%] p-2">
              <div className="bg-white p-2">
                <p className="font-poppins font-normal text-[14px] text-black">
                  Lorem ipsum dolor sit amet, consect...
                </p>
                <p className="font-poppins font-normal text-[14px] text-black">
                  Lorem ipsum dolor sit amet, consect...
                </p>
                <p className="font-poppins font-normal text-[14px] text-black">
                  Lorem ipsum dolor sit amet, consect...
                </p>
                <p className="font-poppins font-normal text-[14px] text-black">
                  Lorem ipsum dolor sit amet, consect...
                </p>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default Documents;
