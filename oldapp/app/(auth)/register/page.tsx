"use client";

import Link from "next/link";
import { useState } from "react";
import { FcGoogle } from "react-icons/fc";
import { FaFacebook } from "react-icons/fa";

const Register = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [agreeToTerms, setAgreeToTerms] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log({ email, password, agreeToTerms });
    // Add form submission logic here
  };

  const handleGoogleSignIn = () => {
    console.log("Sign in with Google");
    // Add Google sign-in logic here
  };

  const handleFacebookSignIn = () => {
    console.log("Sign in with Facebook");
    // Add Facebook sign-in logic here
  };

  return (
    <div className="flex flex-col md:flex-row min-h-screen">
      {/* Left Section: Registration Form */}
      <div className="flex-1 bg-white  p-6">
        <p className="font-poppins font-bold text-[20px] leading-[37.5px] text-black">
          VentureDirection
        </p>
        <div className="items-center justify-center m-auto p-10">
          <form className="w-4/5 max-w-md" onSubmit={handleSubmit}>
            <h1 className="text-2xl font-medium mb-2">Get Started Now</h1>
            <p className="text-black text-[14px] font-medium mb-6">
              Create an account to take your business to the next level
            </p>
            {/* Email Input */}
            <div className="mb-4">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-black"
              >
                Email address
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                className="w-full border border-[D9D9D9] rounded-lg p-1 mt-1 focus:outline-none"
              />
            </div>
            {/* Password Input */}
            <div className="mb-4">
              <label
                htmlFor="password"
                className="block text-sm font-medium text-black"
              >
                Password
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                className="w-full border border-[D9D9D9] rounded-lg p-1 mt-1 focus:outline-none"
              />
            </div>
            {/* Terms and Policy */}
            <div className="mb-6 flex items-center">
              <input
                id="terms"
                type="checkbox"
                checked={agreeToTerms}
                onChange={(e) => setAgreeToTerms(e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="terms" className="text-sm">
                I agree to the{" "}
                <a href="" className="text-black underline">
                  terms & policy
                </a>
              </label>
            </div>
            {/* Sign Up Button */}
            <button
              type="submit"
              className="w-full font-poppins font-bold bg-[#01283E] text-white text-[13px] py-1 rounded-lg transition"
            >
              Sign Up
            </button>
            {/* Divider */}
            <div className="flex items-center justify-between my-8">
              <hr className="w-[45%]" />
              <p className="text-sm">or</p>
              <hr className="w-[45%]" />
            </div>
            {/* Social Logins */}
            <div className="flex justify-center gap-4 p-1">
              {/* Google Sign-In Button */}
              <div
                onClick={handleGoogleSignIn}
                className="flex items-center gap-2 border border-[D9D9D9] rounded-lg px-7 py-2  transition duration-200 cursor-pointer"
              >
                <FcGoogle size={20} />

                <span className="text-[10px] font-medium">
                  Sign in with Google
                </span>
              </div>

              {/* Facebook Sign-In Button */}
              <div
                onClick={handleFacebookSignIn}
                className="flex items-center gap-2 border border-[D9D9D9] rounded-lg px-7 py-2  transition duration-200 cursor-pointer"
              >
                <FaFacebook size={20} className="text-blue-600" />

                <span className="text-[10px] font-medium">
                  Sign in with Facebook
                </span>
              </div>
            </div>
            <p className="text-sm text-center mt-4 font-poppins">
              Have an account?{" "}
              <Link href="login" className="text-blue-500">
                Sign In
              </Link>
            </p>
          </form>
        </div>
      </div>

      {/* Right Section: Why Register */}
      <div className="flex-1 bg-[#01283E] text-white items-center justify-center p-8">
        <div>
          <p className="font-inclusive font-normal italic text-[20px] leading-[30px] mb-24">
            Become one of the hundreds of businesses registered with
            VentureDirection!
          </p>
          <h2 className="font-bold mb-10 text-center font-poppins text-[40px]">
            Why Register?
          </h2>
          <p className="mb-6 text-lg font-poppins">
            You can take control of your business's future. With <br />{" "}
            VentureDirection, you'll get:
          </p>
          <ul className="list-disc pl-6 space-y-4 text-lg font-poppins">
            <li>Simple Business Setup: Register and manage your
              business with ease.
            </li>
            <li>Stay Compliant: Get timely reminders and
              resources to meet every regulatory requirement.
            </li>
            <li>Growth Tools: Access features designed to support
              and scale your business across borders.
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Register;
