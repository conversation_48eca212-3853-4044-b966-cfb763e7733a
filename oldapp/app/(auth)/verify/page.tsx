"use client";

import React from "react";

const Confirmation: React.FC = () => {
  const resendEmail = () => {
    // Simulate sending a new confirmation email
    alert("Confirmation email resent!");
  };

  return (
    <div className="flex justify-center items-center min-h-screen bg-[#01283E] px-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-[1124px] min-h-[520px] w-full">
        <p className="text-gray-800 text-[25px] leading-relaxed mt-20">
          Thanks for joining <strong>VentureDirection!</strong> Please check
          your email for a confirmation link to activate your account. Once
          confirmed, you’ll be ready to start managing your business with
          confidence.
        </p>
        <p className="mt-6 text-black text-[25px]">
          Didn’t receive the email?{" "}
          <button
            onClick={resendEmail}
            className="text-black italic"
          >
            Resend confirmation email
          </button>
        </p>
      </div>
    </div>
  );
};

export default Confirmation;
