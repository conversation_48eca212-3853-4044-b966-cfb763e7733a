"use client";

import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { FcGoogle } from "react-icons/fc";
import { FaFacebook } from "react-icons/fa";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [agreeToTerms, setAgreeToTerms] = useState(false);

  const router = useRouter();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log({ email, password, agreeToTerms });
    // Add form submission logic here

    // Redirect to the dashboard page
    router.push("/admin");
  };

  const handleGoogleSignIn = () => {
    console.log("Sign in with Google");
    // Add Google sign-in logic here
  };

  const handleFacebookSignIn = () => {
    console.log("Sign in with Facebook");
    // Add Facebook sign-in logic here
  };

  return (
    <div className="flex flex-col md:flex-row min-h-screen">
      {/* Left Section: Login Form */}
      <div className="flex-1 bg-white  p-6">
        <p className="font-poppins font-bold text-[20px] leading-[37.5px] text-black">
          VentureDirection
        </p>
        <div className="items-center justify-center m-auto p-10">
          <form className="w-4/5 max-w-md" onSubmit={handleSubmit}>
            <h1 className="text-2xl font-medium mb-2">Welcome back!</h1>
            <p className="text-black text-[14px] font-medium mb-6">
              Enter your details to access your account
            </p>
            {/* Email Input */}
            <div className="mb-4">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-black"
              >
                Email address
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                className="w-full border border-[D9D9D9] rounded-lg p-1 mt-1 focus:outline-none"
              />
            </div>
            {/* Password Input */}
            <div className="mb-4">
              <label
                htmlFor="password"
                className=" text-sm font-medium text-black flex justify-between"
              >
                Password
                <a href="#" className="text-blue-600 text-sm hover:underline">
                  Forgot password?
                </a>
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                className="w-full border border-[D9D9D9] rounded-lg p-1 mt-1 focus:outline-none"
              />
            </div>
            {/* Terms and Policy */}
            <div className="mb-6 flex items-center">
              <input
                id="terms"
                type="checkbox"
                checked={agreeToTerms}
                onChange={(e) => setAgreeToTerms(e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="terms" className="text-sm">
                I agree to the{" "}
                <a href="" className="text-black underline">
                  terms & policy
                </a>
              </label>
            </div>
            {/* Sign Up Button */}
            <button
              type="submit"
              className="w-full font-poppins font-bold bg-[#01283E] text-white text-[13px] py-1 rounded-lg transition"
            >
              Login
            </button>
            {/* Divider */}
            <div className="flex items-center justify-between my-8">
              <hr className="w-[45%]" />
              <p className="text-sm">or</p>
              <hr className="w-[45%]" />
            </div>
            {/* Social Logins */}
            <div className="flex justify-center gap-4 p-1">
              {/* Google Sign-In Button */}
              <div
                onClick={handleGoogleSignIn}
                className="flex items-center gap-2 border border-[D9D9D9] rounded-lg px-7 py-2  transition duration-200 cursor-pointer"
              >
                <FcGoogle size={20} />

                <span className="text-[10px] font-medium">
                  Sign in with Google
                </span>
              </div>

              {/* Facebook Sign-In Button */}
              <div
                onClick={handleFacebookSignIn}
                className="flex items-center gap-2 border border-[D9D9D9] rounded-lg px-7 py-2  transition duration-200 cursor-pointer"
              >
                <FaFacebook size={20} className="text-blue-600" />

                <span className="text-[10px] font-medium">
                  Sign in with Facebook
                </span>
              </div>
            </div>
            <p className="text-sm text-center mt-4 font-poppins">
              Don't have an account?{" "}
              <Link href="register" className="text-blue-500">
                Sign Up
              </Link>
            </p>
          </form>
        </div>
      </div>

      {/* Right Section: Did you know? */}
      <div className="flex-1 bg-[#01283E] text-white items-center justify-center p-8">
        <div>
          <p className="font-inclusive font-normal text-[20px] leading-[30px] mb-24">
            Text about VentureDirection stats and logo because real data and
            personalisation drives action
          </p>
          <h2 className="font-bold mb-10 text-center font-poppins text-[40px]">
            Did You Know?
          </h2>
          <ul className="list-disc pl-6 space-y-4 text-lg font-poppins">
            <li>
              Simple Business Setup: Register and manage your business with
              ease.
            </li>
            <li>
              Stay Compliant: Get timely reminders and resources to meet every
              regulatory requirement.
            </li>
            <li>
              Growth Tools: Access features designed to support and scale your
              business across borders.
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Login;
