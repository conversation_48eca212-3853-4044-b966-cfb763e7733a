"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import {
  ArrowLeft,
  Bell,
  Building,
  Calendar,
  FileText,
  Loader2,
  MapPin,
  MoreHorizontal,
  Plus,
  Search,
  Settings,
  User,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Skeleton } from "@/components/ui/skeleton"
import type { Company } from "@/oldapp/lib/types/database"

export default function CompanyDetail({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [company, setCompany] = useState<Company | null>(null)
  const [filingHistory, setFilingHistory] = useState([])
  const [loadingFilings, setLoadingFilings] = useState(false)
  const [error, setError] = useState("")

  useEffect(() => {
    fetchCompany()
  }, [])

  const fetchCompany = async () => {
    setLoading(true)
    setError("")

    try {
      const response = await fetch(`/api/companies/${params.id}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to fetch company")
      }

      const data = await response.json()
      setCompany(data)
    } catch (err) {
      setError(err.message || "An error occurred while fetching company details")
      console.error("Error fetching company:", err)
    } finally {
      setLoading(false)
    }
  }

  const fetchFilingHistory = async () => {
    if (!company) return

    setLoadingFilings(true)
    setError("")

    try {
      const response = await fetch("/api/companies-house", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ companyNumber: company.company_number }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to fetch filing history")
      }

      const data = await response.json()
      setFilingHistory(data.items || [])
    } catch (err) {
      setError(err.message || "An error occurred while fetching filing history")
    } finally {
      setLoadingFilings(false)
    }
  }

  const getStatusBadge = (status) => {
    switch (status?.toLowerCase()) {
      case "active":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            Active
          </Badge>
        )
      case "dissolved":
        return <Badge variant="destructive">Dissolved</Badge>
      case "liquidation":
        return <Badge variant="destructive">Liquidation</Badge>
      default:
        return <Badge variant="secondary">{status || "Unknown"}</Badge>
    }
  }

  const formatAddress = (address) => {
    if (!address) return "Not available"

    const parts = [
      address.address_line_1,
      address.address_line_2,
      address.locality,
      address.postal_code,
      address.country,
    ].filter(Boolean)

    return parts.join(", ")
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
          <div className="mr-4 hidden md:flex">
            <Link href="/" className="mr-6 flex items-center space-x-2">
              <FileText className="h-6 w-6" />
              <span className="hidden font-bold sm:inline-block">Companies House Filing Assistant</span>
            </Link>
            <nav className="flex items-center space-x-6 text-sm font-medium">
              <Link href="/dashboard" className="transition-colors hover:text-foreground/80">
                Dashboard
              </Link>
              <Link href="/companies" className="transition-colors hover:text-foreground/80 text-foreground">
                Companies
              </Link>
              <Link href="/filings" className="transition-colors hover:text-foreground/80">
                Filings
              </Link>
              <Link href="/reminders" className="transition-colors hover:text-foreground/80">
                Reminders
              </Link>
            </nav>
          </div>
          <div className="flex flex-1 items-center justify-end space-x-4">
            <nav className="flex items-center space-x-2">
              <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                <Bell className="h-4 w-4" />
                <span className="sr-only">Notifications</span>
              </Button>
              <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                <Settings className="h-4 w-4" />
                <span className="sr-only">Settings</span>
              </Button>
              <Avatar>
                <AvatarImage src="/placeholder.svg" alt="User" />
                <AvatarFallback>JS</AvatarFallback>
              </Avatar>
            </nav>
          </div>
        </div>
      </header>
      <main className="flex-1 p-6">
        <div className="mb-6">
          <Button variant="outline" size="sm" onClick={() => router.push("/companies")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Companies
          </Button>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {loading ? (
          <div className="space-y-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <Skeleton className="h-8 w-64 mb-2" />
                <div className="flex items-center gap-2">
                  <Skeleton className="h-5 w-40" />
                </div>
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-10 w-32" />
                <Skeleton className="h-10 w-32" />
              </div>
            </div>

            <div className="grid gap-6 md:grid-cols-3">
              <Skeleton className="h-64 md:col-span-2" />
              <Skeleton className="h-64" />
            </div>
          </div>
        ) : company ? (
          <div className="space-y-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold">{company.name}</h1>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline">Company Number: {company.company_number}</Badge>
                  {getStatusBadge(company.status)}
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => router.push(`/filing/new?companyId=${company.id}`)}>
                  <FileText className="mr-2 h-4 w-4" />
                  Prepare Filing
                </Button>
                <Button onClick={() => router.push(`/reminders/new?companyId=${company.id}`)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Reminder
                </Button>
              </div>
            </div>

            <div className="grid gap-6 md:grid-cols-3">
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle>Company Information</CardTitle>
                  <CardDescription>Details from Companies House</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-start gap-2">
                        <Building className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="font-medium">Company Number</p>
                          <p className="text-muted-foreground">{company.company_number}</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="font-medium">Incorporation Date</p>
                          <p className="text-muted-foreground">{company.incorporation_date || "Not available"}</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="font-medium">Registered Address</p>
                          <p className="text-muted-foreground">{formatAddress(company.registered_address)}</p>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-start gap-2">
                        <User className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="font-medium">SIC Codes</p>
                          <div className="text-muted-foreground">
                            {company.sic_codes && company.sic_codes.length > 0 ? (
                              company.sic_codes.map((code, index) => <p key={index}>{code}</p>)
                            ) : (
                              <p>Not available</p>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="font-medium">Next Filing Due</p>
                          <p className="text-muted-foreground">{company.next_filing_date || "Not set"}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Filing Status</CardTitle>
                  <CardDescription>Current confirmation statement status</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <p className="font-medium">Next Confirmation Statement</p>
                    <div className="flex items-center justify-between">
                      <p className="text-muted-foreground">Due Date:</p>
                      <p>{company.next_filing_date || "Not set"}</p>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-muted-foreground">Status:</p>
                      <Badge variant="outline">Pending</Badge>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button className="w-full" onClick={() => router.push(`/filing/new?companyId=${company.id}`)}>
                    <FileText className="mr-2 h-4 w-4" />
                    Prepare Filing
                  </Button>
                </CardFooter>
              </Card>
            </div>

            <Tabs defaultValue="filings" className="mt-6">
              <TabsList>
                <TabsTrigger value="filings">Filing History</TabsTrigger>
                <TabsTrigger value="reminders">Reminders</TabsTrigger>
                <TabsTrigger value="officers">Officers</TabsTrigger>
              </TabsList>

              <TabsContent value="filings" className="mt-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                      <CardTitle>Filing History</CardTitle>
                      <CardDescription>Previous filings with Companies House</CardDescription>
                    </div>
                    <Button variant="outline" onClick={fetchFilingHistory} disabled={loadingFilings}>
                      {loadingFilings ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        <>
                          <Search className="mr-2 h-4 w-4" />
                          Fetch from Companies House
                        </>
                      )}
                    </Button>
                  </CardHeader>
                  <CardContent>
                    {filingHistory.length > 0 ? (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Date</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Description</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filingHistory.map((filing, index) => (
                            <TableRow key={index}>
                              <TableCell>{filing.date}</TableCell>
                              <TableCell>{filing.type}</TableCell>
                              <TableCell>{filing.description}</TableCell>
                              <TableCell className="text-right">
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        {loadingFilings ? (
                          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                        ) : (
                          <>
                            <FileText className="h-8 w-8 mx-auto mb-2" />
                            <p>No filing history available</p>
                            <p className="text-sm">Click "Fetch from Companies House" to retrieve filing history</p>
                          </>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="reminders" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Reminders</CardTitle>
                    <CardDescription>Scheduled reminders for this company</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                      <Bell className="h-8 w-8 mx-auto mb-2" />
                      <p>No reminders set</p>
                      <Button
                        variant="outline"
                        className="mt-4"
                        onClick={() => router.push(`/reminders/new?companyId=${company.id}`)}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add Reminder
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="officers" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Officers</CardTitle>
                    <CardDescription>Directors and secretaries</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                      <User className="h-8 w-8 mx-auto mb-2" />
                      <p>Officer information not loaded</p>
                      <Button variant="outline" className="mt-4">
                        <Search className="mr-2 h-4 w-4" />
                        Fetch Officers
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        ) : (
          <Alert>
            <AlertTitle>Company not found</AlertTitle>
            <AlertDescription>
              The company you are looking for could not be found. Please check the URL and try again.
            </AlertDescription>
          </Alert>
        )}
      </main>
    </div>
  )
}
