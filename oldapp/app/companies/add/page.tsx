"use client"

import { useState } from "react"
import { useR<PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, Bell, FileText, Loader2, Plus, Search, Settings } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import type { Company } from "@/oldapp/lib/types/database"

export default function AddCompany() {
  const router = useRouter()
  const [companyNumber, setCompanyNumber] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [companyData, setCompanyData] = useState(null)
  const [error, setError] = useState("")
  const [isSaving, setIsSaving] = useState(false)

  const searchCompany = async (e) => {
    e.preventDefault()

    if (!companyNumber.trim()) {
      setError("Please enter a company number")
      return
    }

    setIsSearching(true)
    setError("")
    setCompanyData(null)

    try {
      const response = await fetch(`/api/companies-house?companyNumber=${companyNumber.trim()}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to fetch company data")
      }

      const data = await response.json()
      setCompanyData(data)
    } catch (err) {
      setError(err.message || "An error occurred while searching for the company")
    } finally {
      setIsSearching(false)
    }
  }

  const addCompany = async () => {
    if (!companyData) return

    setIsSaving(true)
    setError("")

    try {
      // Format the company data for our database
      const companyToSave: Partial<Company> = {
        company_number: companyData.company_number,
        name: companyData.company_name,
        status: companyData.company_status,
        incorporation_date: companyData.date_of_creation,
        company_type: companyData.type,
        registered_address: companyData.registered_office_address,
        sic_codes: companyData.sic_codes?.map((code) => code.description) || [],
        next_filing_date: null, // We'll need to calculate this based on the filing history
      }

      // Save to our database
      const response = await fetch("/api/companies", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(companyToSave),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to save company")
      }

      // Redirect to the companies page
      router.push("/companies")
    } catch (err) {
      setError(err.message || "An error occurred while saving the company")
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
          <div className="mr-4 hidden md:flex">
            <Link href="/" className="mr-6 flex items-center space-x-2">
              <FileText className="h-6 w-6" />
              <span className="hidden font-bold sm:inline-block">Companies House Filing Assistant</span>
            </Link>
            <nav className="flex items-center space-x-6 text-sm font-medium">
              <Link href="/dashboard" className="transition-colors hover:text-foreground/80">
                Dashboard
              </Link>
              <Link href="/companies" className="transition-colors hover:text-foreground/80 text-foreground">
                Companies
              </Link>
              <Link href="/filings" className="transition-colors hover:text-foreground/80">
                Filings
              </Link>
              <Link href="/reminders" className="transition-colors hover:text-foreground/80">
                Reminders
              </Link>
            </nav>
          </div>
          <div className="flex flex-1 items-center justify-end space-x-4">
            <nav className="flex items-center space-x-2">
              <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                <Bell className="h-4 w-4" />
                <span className="sr-only">Notifications</span>
              </Button>
              <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                <Settings className="h-4 w-4" />
                <span className="sr-only">Settings</span>
              </Button>
              <Avatar>
                <AvatarImage src="/placeholder.svg" alt="User" />
                <AvatarFallback>JS</AvatarFallback>
              </Avatar>
            </nav>
          </div>
        </div>
      </header>
      <main className="flex-1 p-6">
        <div className="mb-6">
          <Button variant="outline" size="sm" onClick={() => router.push("/companies")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Companies
          </Button>
        </div>

        <div className="mx-auto max-w-2xl">
          <h1 className="text-3xl font-bold mb-6">Add Company</h1>

          <Card>
            <CardHeader>
              <CardTitle>Search Companies House</CardTitle>
              <CardDescription>
                Enter a company number to search for a company in the Companies House register
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={searchCompany} className="flex gap-2">
                <Input
                  placeholder="Enter company number (e.g. 12345678)"
                  value={companyNumber}
                  onChange={(e) => setCompanyNumber(e.target.value)}
                  className="flex-1"
                />
                <Button type="submit" disabled={isSearching}>
                  {isSearching ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Searching...
                    </>
                  ) : (
                    <>
                      <Search className="mr-2 h-4 w-4" />
                      Search
                    </>
                  )}
                </Button>
              </form>

              {error && (
                <Alert variant="destructive" className="mt-4">
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {companyData && (
                <div className="mt-6 space-y-4">
                  <h3 className="text-lg font-semibold">Company Found</h3>
                  <div className="rounded-md border p-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="font-medium">Company Name:</span>
                        <span>{companyData.company_name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Company Number:</span>
                        <span>{companyData.company_number}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Status:</span>
                        <span>{companyData.company_status}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Incorporation Date:</span>
                        <span>{companyData.date_of_creation}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Type:</span>
                        <span>{companyData.type}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Registered Address:</span>
                        <span>
                          {companyData.registered_office_address?.address_line_1},
                          {companyData.registered_office_address?.locality},
                          {companyData.registered_office_address?.postal_code}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
            {companyData && (
              <CardFooter>
                <Button onClick={addCompany} className="w-full" disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Plus className="mr-2 h-4 w-4" />
                      Add This Company
                    </>
                  )}
                </Button>
              </CardFooter>
            )}
          </Card>
        </div>
      </main>
    </div>
  )
}
