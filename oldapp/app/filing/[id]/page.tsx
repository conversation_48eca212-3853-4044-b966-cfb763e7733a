"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, Bell, Calendar, FileText, Info, Save, Search, Settings, Upload, Plus } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

export default function FilingDetail({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [filing, setFiling] = useState({
    id: Number.parseInt(params.id),
    companyName: "Acme Ltd",
    companyNumber: "12345678",
    filingType: "Confirmation Statement",
    dueDate: "2025-05-15",
    status: "in-progress",
    lastUpdated: "2025-04-28",
    data: {
      sameRegisteredOfficeAddress: true,
      sameSICCodes: true,
      sameShareholderInfo: false,
      samePSCInfo: true,
      notes: "Need to update shareholder information",
    },
  })

  const [formData, setFormData] = useState(filing.data)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleCheckboxChange = (field: string) => {
    setFormData({
      ...formData,
      [field]: !formData[field],
    })
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      [field]: value,
    })
  }

  const handleSave = async () => {
    // In a real app, this would save to the database
    setFiling({
      ...filing,
      data: formData,
      lastUpdated: new Date().toISOString().split("T")[0],
    })

    // Show success message
    alert("Filing saved successfully")
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)

    // In a real app, this would call the API to submit to Companies House
    setTimeout(() => {
      setFiling({
        ...filing,
        status: "completed",
        lastUpdated: new Date().toISOString().split("T")[0],
      })
      setIsSubmitting(false)

      // Show success message
      alert("Filing submitted successfully to Companies House")

      // Redirect to filings page
      router.push("/filings")
    }, 2000)
  }

  const getStatusBadge = (status) => {
    switch (status) {
      case "urgent":
        return <Badge variant="destructive">Urgent</Badge>
      case "in-progress":
        return <Badge variant="outline">In Progress</Badge>
      case "pending":
        return <Badge>Pending</Badge>
      case "completed":
        return (
          <Badge variant="success" className="bg-green-100 text-green-800 hover:bg-green-100">
            Completed
          </Badge>
        )
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
          <div className="mr-4 hidden md:flex">
            <Link href="/" className="mr-6 flex items-center space-x-2">
              <FileText className="h-6 w-6" />
              <span className="hidden font-bold sm:inline-block">Companies House Filing Assistant</span>
            </Link>
            <nav className="flex items-center space-x-6 text-sm font-medium">
              <Link href="/dashboard" className="transition-colors hover:text-foreground/80">
                Dashboard
              </Link>
              <Link href="/companies" className="transition-colors hover:text-foreground/80">
                Companies
              </Link>
              <Link href="/filings" className="transition-colors hover:text-foreground/80 text-foreground">
                Filings
              </Link>
              <Link href="/reminders" className="transition-colors hover:text-foreground/80">
                Reminders
              </Link>
            </nav>
          </div>
          <div className="flex flex-1 items-center justify-end space-x-4">
            <div className="w-full flex-1 md:w-auto md:flex-none">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search..."
                  className="w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[300px]"
                />
              </div>
            </div>
            <nav className="flex items-center space-x-2">
              <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                <Bell className="h-4 w-4" />
                <span className="sr-only">Notifications</span>
              </Button>
              <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                <Settings className="h-4 w-4" />
                <span className="sr-only">Settings</span>
              </Button>
              <Avatar>
                <AvatarImage src="/placeholder.svg" alt="User" />
                <AvatarFallback>JS</AvatarFallback>
              </Avatar>
            </nav>
          </div>
        </div>
      </header>
      <main className="flex-1 p-6">
        <div className="mb-6">
          <Button variant="outline" size="sm" onClick={() => router.push("/filings")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Filings
          </Button>
        </div>

        <div className="flex flex-col gap-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">{filing.filingType}</h1>
              <div className="flex items-center gap-2 mt-1">
                <p className="text-muted-foreground">
                  {filing.companyName} ({filing.companyNumber})
                </p>
                {getStatusBadge(filing.status)}
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleSave}>
                <Save className="mr-2 h-4 w-4" />
                Save Draft
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button disabled={filing.status === "completed" || isSubmitting}>
                    <Upload className="mr-2 h-4 w-4" />
                    {isSubmitting ? "Submitting..." : "Submit to Companies House"}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Submit Confirmation Statement</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to submit this confirmation statement to Companies House? This action cannot
                      be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleSubmit}>Submit</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <div className="md:col-span-2">
              <Tabs defaultValue="details">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="details">Company Details</TabsTrigger>
                  <TabsTrigger value="shareholders">Shareholders</TabsTrigger>
                  <TabsTrigger value="pscs">PSCs</TabsTrigger>
                </TabsList>

                <TabsContent value="details" className="mt-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Company Details</CardTitle>
                      <CardDescription>Confirm the company details for the confirmation statement</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="registered-office"
                          checked={formData.sameRegisteredOfficeAddress}
                          onCheckedChange={() => handleCheckboxChange("sameRegisteredOfficeAddress")}
                        />
                        <Label htmlFor="registered-office">The registered office address has not changed</Label>
                      </div>

                      {!formData.sameRegisteredOfficeAddress && (
                        <div className="grid gap-4 pl-6 pt-2">
                          <div className="grid gap-2">
                            <Label htmlFor="address-line-1">Address Line 1</Label>
                            <Input id="address-line-1" placeholder="123 Business Street" />
                          </div>
                          <div className="grid gap-2">
                            <Label htmlFor="address-line-2">Address Line 2</Label>
                            <Input id="address-line-2" placeholder="Suite 456" />
                          </div>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                              <Label htmlFor="city">City</Label>
                              <Input id="city" placeholder="London" />
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="postcode">Postcode</Label>
                              <Input id="postcode" placeholder="EC1A 1BB" />
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="sic-codes"
                          checked={formData.sameSICCodes}
                          onCheckedChange={() => handleCheckboxChange("sameSICCodes")}
                        />
                        <Label htmlFor="sic-codes">The SIC codes have not changed</Label>
                      </div>

                      {!formData.sameSICCodes && (
                        <div className="grid gap-4 pl-6 pt-2">
                          <div className="grid gap-2">
                            <Label htmlFor="sic-code-1">SIC Code 1</Label>
                            <Select defaultValue="62020">
                              <SelectTrigger id="sic-code-1">
                                <SelectValue placeholder="Select SIC code" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="62020">62020 - Computer consultancy activities</SelectItem>
                                <SelectItem value="62090">
                                  62090 - Other information technology service activities
                                </SelectItem>
                                <SelectItem value="70229">70229 - Management consultancy activities</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <Button variant="outline" size="sm" className="w-fit">
                            <Plus className="mr-2 h-4 w-4" />
                            Add Another SIC Code
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="shareholders" className="mt-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Shareholders</CardTitle>
                      <CardDescription>
                        Confirm the shareholder information for the confirmation statement
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="shareholders-info"
                          checked={formData.sameShareholderInfo}
                          onCheckedChange={() => handleCheckboxChange("sameShareholderInfo")}
                        />
                        <Label htmlFor="shareholders-info">The shareholder information has not changed</Label>
                      </div>

                      {!formData.sameShareholderInfo && (
                        <div className="space-y-4 pl-6 pt-2">
                          <Accordion type="single" collapsible className="w-full">
                            <AccordionItem value="shareholder-1">
                              <AccordionTrigger>John Smith</AccordionTrigger>
                              <AccordionContent>
                                <div className="grid gap-4">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div className="grid gap-2">
                                      <Label htmlFor="first-name">First Name</Label>
                                      <Input id="first-name" defaultValue="John" />
                                    </div>
                                    <div className="grid gap-2">
                                      <Label htmlFor="last-name">Last Name</Label>
                                      <Input id="last-name" defaultValue="Smith" />
                                    </div>
                                  </div>
                                  <div className="grid gap-2">
                                    <Label htmlFor="shares-held">Shares Held</Label>
                                    <Input id="shares-held" defaultValue="500" />
                                  </div>
                                  <div className="grid gap-2">
                                    <Label htmlFor="share-class">Share Class</Label>
                                    <Select defaultValue="ordinary">
                                      <SelectTrigger id="share-class">
                                        <SelectValue placeholder="Select share class" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="ordinary">Ordinary Shares</SelectItem>
                                        <SelectItem value="preference">Preference Shares</SelectItem>
                                        <SelectItem value="deferred">Deferred Shares</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                            <AccordionItem value="shareholder-2">
                              <AccordionTrigger>Sarah Johnson</AccordionTrigger>
                              <AccordionContent>
                                <div className="grid gap-4">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div className="grid gap-2">
                                      <Label htmlFor="first-name-2">First Name</Label>
                                      <Input id="first-name-2" defaultValue="Sarah" />
                                    </div>
                                    <div className="grid gap-2">
                                      <Label htmlFor="last-name-2">Last Name</Label>
                                      <Input id="last-name-2" defaultValue="Johnson" />
                                    </div>
                                  </div>
                                  <div className="grid gap-2">
                                    <Label htmlFor="shares-held-2">Shares Held</Label>
                                    <Input id="shares-held-2" defaultValue="500" />
                                  </div>
                                  <div className="grid gap-2">
                                    <Label htmlFor="share-class-2">Share Class</Label>
                                    <Select defaultValue="ordinary">
                                      <SelectTrigger id="share-class-2">
                                        <SelectValue placeholder="Select share class" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="ordinary">Ordinary Shares</SelectItem>
                                        <SelectItem value="preference">Preference Shares</SelectItem>
                                        <SelectItem value="deferred">Deferred Shares</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          </Accordion>

                          <Button variant="outline" size="sm" className="w-fit">
                            <Plus className="mr-2 h-4 w-4" />
                            Add Shareholder
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="pscs" className="mt-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Persons with Significant Control (PSCs)</CardTitle>
                      <CardDescription>Confirm the PSC information for the confirmation statement</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="psc-info"
                          checked={formData.samePSCInfo}
                          onCheckedChange={() => handleCheckboxChange("samePSCInfo")}
                        />
                        <Label htmlFor="psc-info">The PSC information has not changed</Label>
                      </div>

                      {!formData.samePSCInfo && (
                        <div className="space-y-4 pl-6 pt-2">
                          <Accordion type="single" collapsible className="w-full">
                            <AccordionItem value="psc-1">
                              <AccordionTrigger>John Smith</AccordionTrigger>
                              <AccordionContent>
                                <div className="grid gap-4">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div className="grid gap-2">
                                      <Label htmlFor="psc-first-name">First Name</Label>
                                      <Input id="psc-first-name" defaultValue="John" />
                                    </div>
                                    <div className="grid gap-2">
                                      <Label htmlFor="psc-last-name">Last Name</Label>
                                      <Input id="psc-last-name" defaultValue="Smith" />
                                    </div>
                                  </div>
                                  <div className="grid gap-2">
                                    <Label htmlFor="psc-nature">Nature of Control</Label>
                                    <Select defaultValue="shares">
                                      <SelectTrigger id="psc-nature">
                                        <SelectValue placeholder="Select nature of control" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="shares">Ownership of shares (more than 25%)</SelectItem>
                                        <SelectItem value="voting">Voting rights (more than 25%)</SelectItem>
                                        <SelectItem value="appointment">Right to appoint/remove directors</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          </Accordion>

                          <Button variant="outline" size="sm" className="w-fit">
                            <Plus className="mr-2 h-4 w-4" />
                            Add PSC
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>

            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Filing Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-1">
                    <p className="text-sm font-medium">Due Date</p>
                    <p className="text-sm text-muted-foreground flex items-center">
                      <Calendar className="mr-2 h-4 w-4" />
                      {filing.dueDate}
                    </p>
                  </div>
                  <div className="grid gap-1">
                    <p className="text-sm font-medium">Status</p>
                    <div>{getStatusBadge(filing.status)}</div>
                  </div>
                  <div className="grid gap-1">
                    <p className="text-sm font-medium">Last Updated</p>
                    <p className="text-sm text-muted-foreground">{filing.lastUpdated}</p>
                  </div>

                  <div className="border-t pt-4 mt-4">
                    <p className="text-sm font-medium mb-2">Notes</p>
                    <Textarea
                      placeholder="Add notes about this filing..."
                      value={formData.notes || ""}
                      onChange={(e) => handleInputChange("notes", e.target.value)}
                      rows={4}
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col items-start gap-2">
                  <div className="flex items-start gap-2 text-sm text-muted-foreground">
                    <Info className="h-4 w-4 mt-0.5" />
                    <p>
                      This confirmation statement will be submitted to Companies House. Make sure all information is
                      accurate before submitting.
                    </p>
                  </div>
                </CardFooter>
              </Card>

              <Card className="mt-4">
                <CardHeader>
                  <CardTitle>Set Reminders</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="reminder-2-weeks" defaultChecked />
                    <Label htmlFor="reminder-2-weeks">2 weeks before due date</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="reminder-1-week" defaultChecked />
                    <Label htmlFor="reminder-1-week">1 week before due date</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="reminder-day-before" defaultChecked />
                    <Label htmlFor="reminder-day-before">Day before due date</Label>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full">
                    <Bell className="mr-2 h-4 w-4" />
                    Update Reminders
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
