import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

export default function ConfirmEmailPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-white p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold">VentureDirection</h1>
      </div>

      <div className="mx-auto w-full max-w-2xl space-y-8">
        <h2 className="text-center text-3xl font-bold">Confirm your email</h2>

        <div className="space-y-6">
          <p className="text-lg">
            To help protect your account, confirm that this is your email address. This also ensures that critical
            updates and notifications are sent to the right person.
          </p>

          <p className="text-lg">Confirm your email by clicking on the link below.</p>

          <div className="flex justify-center">
            <Link href="/verify-email">
              <Button className="bg-blue-600 px-8 py-6 text-lg font-medium hover:bg-blue-700">Confirm Email</Button>
            </Link>
          </div>

          <div className="pt-8 text-lg">
            <p className="mb-4">Need help? Visit the Help Center or contact us.</p>
            <p>
              To make sure you get important updates from Venturedirection, subscribe to our newsletter or click on the
              icon and add us to your contacts.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
