"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useClerk } from "@clerk/nextjs"

export default function SignOutPage() {
  const { signOut } = useClerk()
  const router = useRouter()

  useEffect(() => {
    signOut().then(() => {
      router.push("/sign-in")
    })
  }, [signOut, router])

  return (
    <div className="flex min-h-screen items-center justify-center bg-[#01283e] text-white">
      <p className="text-xl">Signing you out...</p>
    </div>
  )
}
