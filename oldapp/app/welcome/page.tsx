import { redirect } from "next/navigation"
import { currentUser } from "@clerk/nextjs/server"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DashboardLayout } from "@/components/dashboard-layout"
import { PlusCircle } from "lucide-react"
import Link from "next/link"

export default async function WelcomePage() {
  const user = await currentUser()

  if (!user) {
    redirect("/sign-in")
  }

  const firstName = user.firstName || user.username || "there"

  return (
    <DashboardLayout>
      <div className="flex flex-col items-center justify-center py-16">
        <h1 className="mb-8 text-5xl font-bold">Welcome, {firstName}</h1>

        <p className="mb-16 text-2xl">Let's get started</p>

        <Link href="/businesses">
          <Button className="flex items-center gap-2 rounded-md bg-[#01283e] px-6 py-6 text-xl font-medium text-white hover:bg-[#01283e]/90">
            <PlusCircle className="h-6 w-6" />
            Add a business
          </Button>
        </Link>
      </div>
    </DashboardLayout>
  )
}
