"use client"

import { useState } from "react"
import Link from "next/link"
import {
  Bell,
  Calendar,
  Check,
  Download,
  Eye,
  FileText,
  MoreHorizontal,
  Pencil,
  Plus,
  Search,
  Settings,
  Upload,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function Filings() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  // Mock data for demonstration
  const filings = [
    {
      id: 1,
      companyName: "Acme Ltd",
      companyNumber: "12345678",
      filingType: "Confirmation Statement",
      dueDate: "2025-05-15",
      status: "pending",
      lastUpdated: "2025-04-25",
    },
    {
      id: 2,
      companyName: "Tech Solutions UK",
      companyNumber: "87654321",
      filingType: "Confirmation Statement",
      dueDate: "2025-05-22",
      status: "in-progress",
      lastUpdated: "2025-04-28",
    },
    {
      id: 3,
      companyName: "Global Ventures",
      companyNumber: "45678912",
      filingType: "Confirmation Statement",
      dueDate: "2025-05-05",
      status: "urgent",
      lastUpdated: "2025-04-20",
    },
    {
      id: 4,
      companyName: "Digital Services Ltd",
      companyNumber: "98765432",
      filingType: "Confirmation Statement",
      dueDate: "2025-06-10",
      status: "completed",
      lastUpdated: "2025-04-15",
    },
    {
      id: 5,
      companyName: "Creative Solutions",
      companyNumber: "56789123",
      filingType: "Confirmation Statement",
      dueDate: "2025-07-22",
      status: "pending",
      lastUpdated: "2025-04-10",
    },
  ]

  const getStatusBadge = (status) => {
    switch (status) {
      case "urgent":
        return <Badge variant="destructive">Urgent</Badge>
      case "in-progress":
        return <Badge variant="outline">In Progress</Badge>
      case "pending":
        return <Badge>Pending</Badge>
      case "completed":
        return (
          <Badge variant="success" className="bg-green-100 text-green-800 hover:bg-green-100">
            Completed
          </Badge>
        )
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const filteredFilings = filings.filter((filing) => {
    const matchesSearch =
      filing.companyName.toLowerCase().includes(searchQuery.toLowerCase()) || filing.companyNumber.includes(searchQuery)
    const matchesStatus = statusFilter === "all" || filing.status === statusFilter

    return matchesSearch && matchesStatus
  })

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
          <div className="mr-4 hidden md:flex">
            <Link href="/" className="mr-6 flex items-center space-x-2">
              <FileText className="h-6 w-6" />
              <span className="hidden font-bold sm:inline-block">Companies House Filing Assistant</span>
            </Link>
            <nav className="flex items-center space-x-6 text-sm font-medium">
              <Link href="/dashboard" className="transition-colors hover:text-foreground/80">
                Dashboard
              </Link>
              <Link href="/companies" className="transition-colors hover:text-foreground/80">
                Companies
              </Link>
              <Link href="/filings" className="transition-colors hover:text-foreground/80 text-foreground">
                Filings
              </Link>
              <Link href="/reminders" className="transition-colors hover:text-foreground/80">
                Reminders
              </Link>
            </nav>
          </div>
          <div className="flex flex-1 items-center justify-end space-x-4">
            <div className="w-full flex-1 md:w-auto md:flex-none">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search filings..."
                  className="w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[300px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <nav className="flex items-center space-x-2">
              <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                <Bell className="h-4 w-4" />
                <span className="sr-only">Notifications</span>
              </Button>
              <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                <Settings className="h-4 w-4" />
                <span className="sr-only">Settings</span>
              </Button>
              <Avatar>
                <AvatarImage src="/placeholder.svg" alt="User" />
                <AvatarFallback>JS</AvatarFallback>
              </Avatar>
            </nav>
          </div>
        </div>
      </header>
      <main className="flex-1 p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Filings</h1>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Filing
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              <Calendar className="mr-2 h-4 w-4" />
              Date Range
            </Button>
            <p className="text-sm text-muted-foreground">
              Showing {filteredFilings.length} of {filings.length} filings
            </p>
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Company</TableHead>
                <TableHead>Filing Type</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredFilings.map((filing) => (
                <TableRow key={filing.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{filing.companyName}</p>
                      <p className="text-sm text-muted-foreground">{filing.companyNumber}</p>
                    </div>
                  </TableCell>
                  <TableCell>{filing.filingType}</TableCell>
                  <TableCell>{filing.dueDate}</TableCell>
                  <TableCell>{getStatusBadge(filing.status)}</TableCell>
                  <TableCell>{filing.lastUpdated}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Pencil className="mr-2 h-4 w-4" />
                          Edit Filing
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Upload className="mr-2 h-4 w-4" />
                          Submit to Companies House
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Check className="mr-2 h-4 w-4" />
                          Mark as Completed
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </main>
    </div>
  )
}
