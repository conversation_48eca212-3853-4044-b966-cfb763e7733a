"use client"

import { useState } from "react"
import Link from "next/link"
import { Bell, Calendar, Clock, FileText, Filter, MoreHorizontal, Plus, Search, Settings, Trash2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"

export default function Reminders() {
  const [searchQuery, setSearchQuery] = useState("")

  // Mock data for demonstration
  const reminders = [
    {
      id: 1,
      companyName: "Acme Ltd",
      companyNumber: "12345678",
      filingType: "Confirmation Statement",
      dueDate: "2025-05-15",
      reminderDate: "2025-05-01",
      notificationType: ["email", "app"],
      status: "active",
    },
    {
      id: 2,
      companyName: "Tech Solutions UK",
      companyNumber: "87654321",
      filingType: "Confirmation Statement",
      dueDate: "2025-05-22",
      reminderDate: "2025-05-08",
      notificationType: ["email", "sms", "app"],
      status: "active",
    },
    {
      id: 3,
      companyName: "Global Ventures",
      companyNumber: "45678912",
      filingType: "Confirmation Statement",
      dueDate: "2025-05-05",
      reminderDate: "2025-04-21",
      notificationType: ["email"],
      status: "active",
    },
    {
      id: 4,
      companyName: "Digital Services Ltd",
      companyNumber: "98765432",
      filingType: "Confirmation Statement",
      dueDate: "2025-06-10",
      reminderDate: "2025-05-27",
      notificationType: ["email", "app"],
      status: "active",
    },
  ]

  const getNotificationBadges = (types) => {
    return types.map((type, index) => {
      const badgeVariant = "outline"
      const badgeLabel = type.charAt(0).toUpperCase() + type.slice(1)

      return (
        <Badge key={index} variant={badgeVariant} className="mr-1">
          {badgeLabel}
        </Badge>
      )
    })
  }

  const filteredReminders = reminders.filter(
    (reminder) =>
      reminder.companyName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      reminder.companyNumber.includes(searchQuery),
  )

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
          <div className="mr-4 hidden md:flex">
            <Link href="/" className="mr-6 flex items-center space-x-2">
              <FileText className="h-6 w-6" />
              <span className="hidden font-bold sm:inline-block">Companies House Filing Assistant</span>
            </Link>
            <nav className="flex items-center space-x-6 text-sm font-medium">
              <Link href="/dashboard" className="transition-colors hover:text-foreground/80">
                Dashboard
              </Link>
              <Link href="/companies" className="transition-colors hover:text-foreground/80">
                Companies
              </Link>
              <Link href="/filings" className="transition-colors hover:text-foreground/80">
                Filings
              </Link>
              <Link href="/reminders" className="transition-colors hover:text-foreground/80 text-foreground">
                Reminders
              </Link>
            </nav>
          </div>
          <div className="flex flex-1 items-center justify-end space-x-4">
            <div className="w-full flex-1 md:w-auto md:flex-none">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search reminders..."
                  className="w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[300px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <nav className="flex items-center space-x-2">
              <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                <Bell className="h-4 w-4" />
                <span className="sr-only">Notifications</span>
              </Button>
              <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                <Settings className="h-4 w-4" />
                <span className="sr-only">Settings</span>
              </Button>
              <Avatar>
                <AvatarImage src="/placeholder.svg" alt="User" />
                <AvatarFallback>JS</AvatarFallback>
              </Avatar>
            </nav>
          </div>
        </div>
      </header>
      <main className="flex-1 p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Reminders</h1>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            New Reminder
          </Button>
        </div>

        <div className="grid gap-6 md:grid-cols-3 mb-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>Configure how you want to receive reminders</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Bell className="h-4 w-4" />
                  <Label htmlFor="email-notifications">Email Notifications</Label>
                </div>
                <Switch id="email-notifications" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Bell className="h-4 w-4" />
                  <Label htmlFor="sms-notifications">SMS Notifications</Label>
                </div>
                <Switch id="sms-notifications" />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Bell className="h-4 w-4" />
                  <Label htmlFor="app-notifications">In-App Notifications</Label>
                </div>
                <Switch id="app-notifications" defaultChecked />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Default Reminder Schedule</CardTitle>
              <CardDescription>Set when you want to be reminded by default</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <Label htmlFor="two-weeks">Two weeks before</Label>
                </div>
                <Switch id="two-weeks" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <Label htmlFor="one-week">One week before</Label>
                </div>
                <Switch id="one-week" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <Label htmlFor="day-before">Day before</Label>
                </div>
                <Switch id="day-before" defaultChecked />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Calendar Integration</CardTitle>
              <CardDescription>Sync reminders with your calendar</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <Label htmlFor="google-calendar">Google Calendar</Label>
                </div>
                <Switch id="google-calendar" />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <Label htmlFor="outlook-calendar">Outlook Calendar</Label>
                </div>
                <Switch id="outlook-calendar" />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <Label htmlFor="ical-export">iCal Export</Label>
                </div>
                <Switch id="ical-export" defaultChecked />
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                Connect Calendar
              </Button>
            </CardFooter>
          </Card>
        </div>

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
            <p className="text-sm text-muted-foreground">
              Showing {filteredReminders.length} of {reminders.length} reminders
            </p>
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Company</TableHead>
                <TableHead>Filing Type</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Reminder Date</TableHead>
                <TableHead>Notification Type</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReminders.map((reminder) => (
                <TableRow key={reminder.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{reminder.companyName}</p>
                      <p className="text-sm text-muted-foreground">{reminder.companyNumber}</p>
                    </div>
                  </TableCell>
                  <TableCell>{reminder.filingType}</TableCell>
                  <TableCell>{reminder.dueDate}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                      {reminder.reminderDate}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap">{getNotificationBadges(reminder.notificationType)}</div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem>Edit Reminder</DropdownMenuItem>
                        <DropdownMenuItem>Change Date</DropdownMenuItem>
                        <DropdownMenuItem>Add Notification Method</DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-destructive">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Reminder
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </main>
    </div>
  )
}
