"use client";

import { useState, useEffect } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ProgressSteps from "@/components/ProgressSteps";
import ContentContainer from "@/components/ContentContainer";
import Tab from "@/components/Tab";
import Input from "@/components/Input";
import Select from "@/components/Select";
import Textbox from "@/components/Textbox";
import Sidebar from "@/components/Sidebar";
import {
  Business,
  Shareholder,
  Director,
  PSC,
  Person,
} from "@/oldapp/utils/interfaces";
import { contextualContent } from "@/oldapp/utils/contextualContent";
import {
  validateBusinessName,
  validateRegistrationType,
  validateEmail,
  validateAddress,
  validateDescription,
} from "@/oldapp/utils/entryValidationFunctions";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus, faSave } from "@fortawesome/free-solid-svg-icons";
import styles from "@/styles/Home.module.css";

// Track the validation status of each field in the form
// Out here so it's not reset on re-renders from typing
let validationStatus: any = {};

export default function Incorporate() {
  const formSteps = [
    "Business Details",
    "Shareholders Details",
    "Directors Details",
    "Persons with Significant Control",
    "Registrant Details",
  ];

  // Track which step the form is currently on
  const [step, setStep] = useState(0);
  // For the sidebox content to make visible
  // Dictionary: Stores error messages to display
  // To hold the entered business/people data
  const [sidebox, setSidebox] = useState<string | null | any>(null);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [business, setBusinessData] = useState<Business | any>({});
  const [shareholders, setShareholders] = useState<Shareholder[] | any>([]);
  const [directors, setDirectors] = useState<Director[]>([]);
  const [psc, setPsc] = useState<PSC[]>([]);

  // The price for this registration
  const [individual, setIndividual] = useState<Shareholder | Director | PSC | null | any>(null);
  const [registrant, setRegistrant] = useState<Person | undefined>();
  const [price, setPrice] = useState<number>(500);

  const contextBox = (contextName: string) => {
    let content = contextualContent(contextName);
    setSidebox(content);
  };

  const saveShareholder = () => {
    if (individual) {
      setShareholders([...shareholders, individual]);
      setIndividual(null);
    }
  };

  const handleChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const target = event.target as HTMLInputElement | HTMLSelectElement;
    const fieldName = target.name;
    const fieldValue = target.value;
  
    const field = { [fieldName]: fieldValue };
  
    if (step === 0) {
      setBusinessData({ ...business, ...field });
    } else if (step === 1) {
      setIndividual({ ...individual, ...field });
    } else if (step === 2) {
      setDirectors({ ...directors, ...field });
    } else if (step === 3) {
      setPsc({ ...psc, ...field });
    } else if (step === 4) {
      setRegistrant({ ...registrant, ...field });
    }
  };
  

  const handleTextAreaChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const target = event.target as HTMLTextAreaElement;
    const fieldName = target.name;
    const fieldValue = target.value;
  
    const field = { [fieldName]: fieldValue };
  
    if (step === 0) {
      setBusinessData({ ...business, ...field });
    } else if (step === 1) {
      setIndividual({ ...individual, ...field });
    } else if (step === 2) {
      setDirectors({ ...directors, ...field });
    } else if (step === 3) {
      setPsc({ ...psc, ...field });
    } else if (step === 4) {
      setRegistrant({ ...registrant, ...field });
    }
  };
  

  const handleNext = () => {
    if (step === formSteps.length - 1) return;

    if (validateForm()) {
      setStep(step + 1);
    } else {
      addErrorMessage(
        "step",
        "Please complete the form correctly before proceeding"
      );
    }
  };

  const handleSubmit = (event: React.FormEvent<HTMLFormElement | HTMLInputElement>) => {
    event.preventDefault();

    if (validateForm()) {
      // Send form data
      // Update display
    } else {
      // Invalid Form
      addErrorMessage(
        "step",
        "Please correct issues with the form before submitting"
      );
    }
  };

  const addErrorMessage = (key: string, message: string) => {
    setErrors((prevErrors) => ({ ...prevErrors, [key]: message }));
  };

  const deleteErrorMessage = (key: string) => {
    setErrors((prevErrors) => {
      const updatedErrors = { ...prevErrors };
      delete updatedErrors[key];
      return updatedErrors;
    });
  };

  const validateField = (
    event: React.FocusEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const target = event.target as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
    const contentType = target.name;
    const contentValue = target.value;
  
    let valid: boolean | undefined;
    let message: string | undefined;
  
    switch (contentType) {
      case "businessName":
        ({ valid, message } = validateBusinessName(contentValue));
        break;
      case "registrationType":
        ({ valid, message } = validateRegistrationType(contentValue));
        break;
      case "businessEmail":
        ({ valid, message } = validateEmail(contentValue));
        break;
      case "businessAddress":
        ({ valid, message } = validateAddress(contentValue));
        break;
      case "businessDescription":
        ({ valid, message } = validateDescription(contentValue));
        break;
      default:
        break;
    }
  
    if (!valid && message) {
      addErrorMessage(contentType, message);
    } else {
      deleteErrorMessage(contentType);
      relationalChecks();
    }
  
    validationStatus[contentType] = valid;
  };
  

  const relationalChecks = () => {
    // Check if name includes LTD, PLC, etc and set the reg&company type and such
  };

  const validateForm = () => {
    let valid = true;

    // if(Object.keys(errors).length) valid = false;

    switch (step) {
      case 0:
        if (!Object.keys(business).length) valid = false;
        break;
      case 1:
        break;
      case 2:
        break;
      case 3:
        break;
      case 4:
        break;
      default:
        break;
    }

    return valid;
  };

  const checkValidity = (fieldName: keyof Business): boolean | undefined => {
    // Check not empty
    let value = business[fieldName];
    if (!value) return;

    return validationStatus[fieldName];
  };

  useEffect(() => contextBox("Default"), []);

  return (
    <>
      <Header />
      <div className="container">
        <div className="row">
          <div className="page-content">
            <ProgressSteps totalSteps={formSteps.length} currentStep={step} />
            <ContentContainer>
              <form onSubmit={handleSubmit}>
                <Tab identifier="0" activeTab={String(step)}>
                  <h2>Tell us about your business</h2>

                  <Input
                    name="businessName"
                    placeholder="Name of business"
                    onFocus={() => contextBox("Business Name")}
                    onInput={handleChange}
                    onBlur={validateField}
                    validity={validationStatus["businessName"]}
                  />

                  <Select
                    name="registrationType"
                    placeholder="Registration Type"
                    options={["Business Name", "Company"]}
                    onFocus={() => contextBox("Registration type")}
                    onInput={handleChange}
                    onBlur={validateField}
                    validity={validationStatus["registrationType"]}
                  />

                  {business["registrationType"] === "Company" && (
                    <div>
                      <Select
                        name="companyType"
                        placeholder="Company Type"
                        options={[
                          "Private Limited Company",
                          "Public Limited Company",
                          "Company Limited by Guarantee",
                          "Unlimited Company",
                        ]}
                        onFocus={() => contextBox("Company type")}
                        onInput={handleChange}
                        onBlur={validateField}
                        validity={validationStatus["companyType"]}
                      />

                      <Input
                        name="totalShareCapital"
                        placeholder="Total Share Capital"
                        onFocus={() => contextBox("Total Share Capital")}
                        onInput={handleChange}
                        onBlur={validateField}
                        validity={validationStatus["totalShareCapital"]}
                      />
                    </div>
                  )}

                  <Input
                    name="businessEmail"
                    type="email"
                    placeholder="Business Email"
                    onFocus={() => contextBox("Business Email")}
                    onInput={handleChange}
                    onBlur={validateField}
                    validity={validationStatus["businessEmail"]}
                  />

                  <Textbox
                    name="businessAddress"
                    placeholder="Business address"
                    onFocus={() => contextBox("Business Address")}
                    onInput={handleTextAreaChange}
                    onBlur={validateField}
                    validity={validationStatus["businessAddress"]}
                  />

                  <Textbox
                    name="businessDescription"
                    placeholder="Description of your business"
                    onFocus={() => contextBox("Business Description")}
                    onInput={handleTextAreaChange}
                    onBlur={validateField}
                    validity={validationStatus["businessDescription"]}
                  />
                </Tab>

                <Tab identifier="1" activeTab={String(step)}>
                  <h2>Shareholders</h2>
                  <h4>Who owns the company?</h4>

                  <Input
                    name="shareholderName"
                    placeholder="Shareholder's Name"
                    onFocus={() => contextBox("Shareholders Name")}
                    onInput={handleChange}
                    onBlur={validateField}
                    validity={validationStatus["shareholderName"]}
                  />

                  <Input
                    name="phoneNumber"
                    placeholder="Phone number"
                    onFocus={() => contextBox("Phone Number")}
                    onInput={handleChange}
                    onBlur={validateField}
                    validity={validationStatus["phoneNumber"]}
                  />

                  <Select
                    name="gender"
                    placeholder="Gender"
                    options={["Male", "Female"]}
                    onFocus={() => contextBox("Gender")}
                    onInput={handleChange}
                    onBlur={validateField}
                    validity={validationStatus["gender"]}
                  />

                  <Input
                    name="dateofbirth"
                    type="date"
                    placeholder="Date of birth"
                    onFocus={() => contextBox("Date of Birth")}
                    onInput={handleChange}
                    onBlur={validateField}
                    validity={validationStatus["dateofbirth"]}
                  />

                  <Textbox
                    name="contactAddress"
                    placeholder="Contact address"
                    onFocus={() => contextBox("Contact Address")}
                    onInput={handleTextAreaChange}
                    onBlur={validateField}
                    validity={validationStatus["contactAddress"]}
                  />

                  <Input
                    name="email"
                    type="email"
                    placeholder="Email"
                    onFocus={() => contextBox("Email")}
                    onInput={handleChange}
                    onBlur={validateField}
                    validity={validationStatus["email"]}
                  />

                  <Input
                    name="equityShare"
                    placeholder="Number/pecentage of shares"
                    onFocus={() => contextBox("Equity Share")}
                    onInput={handleChange}
                    onBlur={validateField}
                    validity={validationStatus["equityShare"]}
                  />

                  <button
                    type="button"
                    className="tm-btn-primary"
                    onClick={saveShareholder}
                  >
                    <FontAwesomeIcon icon={faSave} /> Add Shareholder
                  </button>

                  <button type="button" className="tm-btn-primary">
                    <FontAwesomeIcon
                      icon={faPlus}
                      onClick={() => console.log("Test")}
                    />{" "}
                    New Shareholder
                  </button>
                </Tab>

                <Tab identifier="2" activeTab={String(step)}>
                  <h2>Directors</h2>
                  <h4>Who runs the company?</h4>

                  <Input placeholder="Shareholder's Name" />

                  <Input placeholder="Phone number" />

                  <Select placeholder="Gender" options={["Male", "Female"]} name="Select Shareholder" />

                  <Input type="date" placeholder="Date of birth" />

                  <Textbox placeholder="Contact address" name="Shareholder's tect" />

                  <Input type="email" placeholder="Email" />

                  <button type="button" className="tm-btn-primary">
                    <FontAwesomeIcon
                      icon={faSave}
                      onClick={() => console.log("Test")}
                    />{" "}
                    Save Director
                  </button>

                  <button type="button" className="tm-btn-primary">
                    <FontAwesomeIcon
                      icon={faPlus}
                      onClick={() => console.log("Test")}
                    />{" "}
                    Add Director
                  </button>
                </Tab>

                <Tab identifier="3" activeTab={String(step)}>
                  <h2>Secretary</h2>
                  <h4>Who handles the statutory obligations?</h4>

                  <Input placeholder="Shareholder's Name" />

                  <Input placeholder="Phone number" />

                  <Select placeholder="Gender" options={["Male", "Female"]} name="Select Shareholder" />

                  <Input type="date" placeholder="Date of birth" />

                  <Textbox placeholder="Contact address" name="Shareholder's tect" />

                  <Input type="email" placeholder="Email" />
                </Tab>

                <div className="formButtons">
                  <button
                    type="button"
                    id="prevBtn"
                    onClick={() => setStep(step - 1)}
                    className={step === 0 ? "hidden" : "tm-btn-primary"}
                  >
                    Previous
                  </button>
                  <button
                    type={step === formSteps.length - 1 ? "submit" : "button"}
                    id="nextBtn"
                    onClick={handleNext}
                    className="tm-btn-primary"
                  >
                    {step === formSteps.length - 1 ? "Submit" : "Next"}
                  </button>
                </div>
              </form>
            </ContentContainer>
          </div>

          {/* Messenger Chat Plugin Code */}
          <Sidebar sidebox={sidebox} errors={Object.keys(errors).length ? errors : null} />
        </div>
      </div>
      <Footer className={styles.footer} />
    </>
  );
}
