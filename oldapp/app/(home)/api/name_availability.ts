import { ObjectId } from "mongodb";
import { connectToDatabase } from "./_connector";

export default async (req: any, res: any) => {
  const db = await connectToDatabase();

  const entry = await db
    .db("venturedirection")
    .collection("cac_businesses")
    .findOne({ business_name: req.query.name as string });

  if (entry !== null) {
    res.statusCode = 201;
    return res.json({ similar_businesses: `${entry.insertedId}` });
  }

  res.statusCode = 200;
  res.json({
    error: "no_business_found",
    error_description: "No businesses found",
  });
};
