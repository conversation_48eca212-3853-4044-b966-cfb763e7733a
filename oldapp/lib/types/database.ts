export type User = {
  id: string
  email: string
  name: string | null
  created_at: string
  updated_at: string
}

export type Registry = {
  id: number
  country_code: string
  name: string
  api_base_url: string
  api_key_name: string
  required_fields: Record<string, string>
  filing_types: string[]
  created_at: string
  updated_at: string
}

export type Company = {
  id: string
  company_number: string
  name: string
  status: string | null
  incorporation_date: string | null
  company_type: string | null
  registered_address: {
    address_line_1?: string
    address_line_2?: string
    locality?: string
    postal_code?: string
    country?: string
  } | null
  sic_codes: string[] | null
  next_filing_date: string | null
  last_filing_date: string | null
  created_at: string
  updated_at: string
  user_id: string | null
  country_code: string
  registry_specific_data: Record<string, any> | null
}

export type Filing = {
  id: string
  company_id: string
  filing_type: string
  due_date: string | null
  status: string
  submission_date: string | null
  filing_data: any | null
  companies_house_reference: string | null
  created_at: string
  updated_at: string
  user_id: string | null
  country_code: string
  registry_specific_requirements: Record<string, any> | null
}

export type Reminder = {
  id: string
  company_id: string
  filing_id: string | null
  reminder_date: string
  notification_types: string[]
  status: string
  created_at: string
  updated_at: string
  user_id: string | null
}

export type CompanyWithRelations = Company & {
  filings?: Filing[]
  reminders?: Reminder[]
}

export type FilingWithCompany = Filing & {
  company?: Company
}

export type ReminderWithRelations = Reminder & {
  company?: Company
  filing?: Filing
}
