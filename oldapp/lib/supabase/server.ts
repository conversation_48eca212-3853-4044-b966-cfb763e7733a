import { createClient } from "@supabase/supabase-js"

// Create a single supabase client for server-side operations
const createServerClient = () => {
  const supabaseUrl = process.env.SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error("Missing Supabase environment variables")
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      persistSession: false,
    },
  })
}

// Singleton pattern to avoid multiple instances
let serverClient: ReturnType<typeof createClient> | null = null

export const getSupabaseServerClient = () => {
  if (!serverClient) {
    serverClient = createServerClient()
  }
  return serverClient
}
