import { getSupabaseServerClient } from "@/oldapp/lib/supabase/server"
import type { Registry } from "@/oldapp/lib/types/database"

export class RegistryService {
  private static instance: RegistryService
  private registries: Map<string, Registry> = new Map()
  private initialized = false

  private constructor() {}

  public static getInstance(): RegistryService {
    if (!RegistryService.instance) {
      RegistryService.instance = new RegistryService()
    }
    return RegistryService.instance
  }

  public async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      const supabase = getSupabaseServerClient()
      const { data, error } = await supabase.from("registries").select("*")

      if (error) {
        throw new Error(`Failed to fetch registries: ${error.message}`)
      }

      data.forEach((registry: Registry) => {
        this.registries.set(registry.country_code, registry)
      })

      this.initialized = true
    } catch (error) {
      console.error("Error initializing registry service:", error)
      throw error
    }
  }

  public async getRegistry(countryCode: string): Promise<Registry | null> {
    if (!this.initialized) {
      await this.initialize()
    }

    return this.registries.get(countryCode) || null
  }

  public async getAllRegistries(): Promise<Registry[]> {
    if (!this.initialized) {
      await this.initialize()
    }

    return Array.from(this.registries.values())
  }

  public async getApiKey(countryCode: string): Promise<string | null> {
    const registry = await this.getRegistry(countryCode)
    if (!registry) return null

    const apiKeyName = registry.api_key_name
    return process.env[apiKeyName] || null
  }

  public async getApiBaseUrl(countryCode: string): Promise<string | null> {
    const registry = await this.getRegistry(countryCode)
    return registry?.api_base_url || null
  }

  public async getFilingTypes(countryCode: string): Promise<string[]> {
    const registry = await this.getRegistry(countryCode)
    return registry?.filing_types || []
  }

  public async getRequiredFields(countryCode: string): Promise<Record<string, string>> {
    const registry = await this.getRegistry(countryCode)
    return registry?.required_fields || {}
  }
}

export default RegistryService.getInstance()
