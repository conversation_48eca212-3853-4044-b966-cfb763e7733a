export function validateBusinessName(name: string | any[]){
  let valid = true;
  let message = "Business name valid";

  if(name.length < 3) {
    valid = false;
    message = "Your business name is not long enough";
  }

  if(!name) {
    valid = false;
    message = "Your business name is required";
  }

  // Return tuple
  return { "valid": valid, "message": message };
}


export function validateRegistrationType(type: string){
  let valid = true;
  let message = "Registration Type valid";


  if(!(type==="Company" || type==="Business Name")) {
    valid = false;
    message = "Your registration type is not a valid option";
  }

  if(!type) {
    valid = false;
    message = "Your registration type is required";
  }
  
  // Return tuple
  return { "valid": valid, "message": message };
}



const validEmailRegex = RegExp(
  /^(([^<>()\[\]\.,;:\s@\"]+(\.[^<>()\[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i
);

export function validateEmail(type: string){

  let valid = validEmailRegex.test(type);
  let message = "Email valid";



  if(!valid) {
    valid = false;
    message = "The email is not valid";
  }

  if(!type) {
    valid = false;
    message = "Email is required";
  }
  
  // Return tuple
  return { "valid": valid, "message": message };
}



// TODO: Confirm as much as possible that the address is a real one - check Google, other databases, etc.
export function validateAddress(address: string | any[]){
  let valid = true;
  let message = "Address valid";

  if(address.length < 10) {
    valid = false;
    message = "Please enter a more accurate address";
  }

  if(!address) {
    valid = false;
    message = "Address is required";
  }
  
  // Return tuple
  return { "valid": valid, "message": message };
}



export function validateDescription(description: string | any[]){
  let valid = true;
  let message = "Description valid";

  if(description.length < 10) {
    valid = false;
    message = "Please enter a more accurate description";
  }

  if(!description) {
    valid = false;
    message = "Description is required";
  }
  
  // Return tuple
  return { "valid": valid, "message": message };
}

