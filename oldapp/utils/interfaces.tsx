export interface FormValues {
  name: string;
}

export interface ApiResponse {
  similar_businesses: string;
}

export interface ApiError {
  error: string;
  error_description: string;
}

type BusinessType = 'Business Name' | 'Company';

export interface Business {
  name: string;
  email: string;
  businessType: BusinessType;
  companyType?: string;
  shareCapital?: number;
  description: string;
  address: string;
}

export interface Person {
    name?: string;
    phone?: string;
    email?: string;
    gender?: string;
    dateofbirth?: string;
    address?: string;
    nationality?: string;
    min?: string;
    className?: string;
};

export interface Shareholder extends Person {
    numberOfShares: number;
};

export interface Director extends Person {
 
};

export interface PSC extends Person {
 
};