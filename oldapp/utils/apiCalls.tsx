const checkBusinessName = async ({}) => {
  try {
    // const response = await axios.post<ShortenLinkResponse>('/api/name_availability', {name});
    // setStatus('success');
    // setMessage(response.data?.similar_businesses);
  } catch (e) {
    // const error = e as AxiosError<ShortenLinkError>;
    // setStatus('error');
    // setMessage(error.response?.data?.error_description || 'Something went wrong');
  }
};
