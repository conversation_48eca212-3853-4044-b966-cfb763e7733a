
export function contextualContent(contentName: string){
  var codeBlock;

  contentName = contentName.toLowerCase();

  if(contentName==="default") {
    codeBlock = "<h2 className='tm-text-primary'>Welcome</h2> \
      Start the process of registering your business by completing the form.";
  }

  if(contentName==="business name") {
    codeBlock = "<h2 className='tm-text-primary'>Business name</h2> \
      What do you want the official name of your business to be?";
  }

  if(contentName==="business email") {
    codeBlock = "<h2 className='tm-text-primary'>Business Email</h2> \
      Enter an email through which your business can be reached.  \
      This may, but does not need to be your personal email. \
      Note that this email will be available on the companies register \
      if anyone requests your company information.";
  }

  if(contentName==="registration type") {
    codeBlock = "<h2 className='tm-text-primary'>Registration Type</h2> \
      Business name means that your business has no seperate identity from you.";
  }

  if(contentName==="business address") {
    codeBlock = "<h2 className='tm-text-primary'>Business Address</h2> \
      This should be the full address of your business, including the state. <br /><br /> \
      If your business has multiple offices/addresses, this should be the address \
      that you want registered as the official address for your business.";
  }


  if(contentName==="business description") {
    codeBlock = "<h2 className='tm-text-primary'>Business Description</h2> \
      What does your business do? Include everything that is relevant. \
      Remember that your business may not be allowed to do anything that is not mentioned here";
  }

  return codeBlock;
}