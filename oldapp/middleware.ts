import createMiddleware from 'next-intl/middleware';
import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { routing } from '../i18n/routing';

const intlMiddleware = createMiddleware(routing);


const publicRoutes = createRouteMatcher([
  "/",
  "/sign-in(.*)",
  "/sign-up(.*)",
  "/forgot-password(.*)",
  "/reset-password(.*)",
  "/verify-email(.*)",
  "/sso-callback(.*)",
])

export default clerkMiddleware(async (auth, req) => {
  // Public routes that don't require authentication
  if (!publicRoutes(req)) await auth.protect()

  // Apply internationalization only to accessible routes
  return intlMiddleware(req)
})

// Stop Middleware running on static files and API routes
export const config = {
  // Match all request paths except for the ones starting with:
  // - _next/static (static files)
  // - _next/image (image optimization files)
  // - favicon.ico (favicon file)
  // - public folder
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\..*).*)',
    '/',
    '/(api|trpc)(.*)',
  ],
};
