// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://dbgxxetodlxqdwugbacv.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRiZ3h4ZXRvZGx4cWR3dWdiYWN2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU5NzMyMzcsImV4cCI6MjA2MTU0OTIzN30.3VRUIcNQPS6miWzJuH_W7nCF3mr13znBYuZkFeE28Cw";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);