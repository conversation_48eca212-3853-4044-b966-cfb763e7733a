import { render, screen } from '@testing-library/react'
import { DashboardOverview } from '@/components/dashboard/dashboardOverview'

// Mock the dashboard components
jest.mock('@/components/common/pageHeader', () => ({
  PageHeader: ({ title, description }: any) => (
    <div data-testid="page-header">
      <h1>{title}</h1>
      {description && <p>{description}</p>}
    </div>
  ),
}))

describe('Dashboard Integration', () => {
  it('renders dashboard overview with correct structure', () => {
    render(<DashboardOverview />)

    // Check if main container exists
    const container = screen.getByTestId('dashboard-overview') || 
                     document.querySelector('.space-y-6')
    expect(container).toBeInTheDocument()
  })

  it('displays business metrics cards', () => {
    render(<DashboardOverview />)

    // Check for metric cards
    expect(screen.getByText('totalBusinesses')).toBeInTheDocument()
    expect(screen.getByText('pendingCompliance')).toBeInTheDocument()
    expect(screen.getByText('totalDocuments')).toBeInTheDocument()
    expect(screen.getByText('Active Users')).toBeInTheDocument()
  })

  it('shows correct initial values', () => {
    render(<DashboardOverview />)

    // Check for zero values in new dashboard
    const zeroValues = screen.getAllByText('0')
    expect(zeroValues.length).toBeGreaterThan(0)

    // Check for the one active user
    expect(screen.getByText('1')).toBeInTheDocument()
  })

  it('displays welcome section', () => {
    render(<DashboardOverview />)

    expect(screen.getByText('overview')).toBeInTheDocument()
    expect(screen.getByText('description')).toBeInTheDocument()
  })

  it('shows next steps list', () => {
    render(<DashboardOverview />)

    expect(screen.getByText('Next steps:')).toBeInTheDocument()
    expect(screen.getByText('• Add your first business')).toBeInTheDocument()
    expect(screen.getByText('• Set up compliance templates')).toBeInTheDocument()
    expect(screen.getByText('• Upload important documents')).toBeInTheDocument()
    expect(screen.getByText('• Configure notification preferences')).toBeInTheDocument()
  })

  it('has proper responsive grid layout', () => {
    render(<DashboardOverview />)

    const gridContainer = document.querySelector('.grid')
    expect(gridContainer).toHaveClass('gap-4', 'md:grid-cols-2', 'lg:grid-cols-4')
  })

  it('applies correct styling to metric cards', () => {
    render(<DashboardOverview />)

    const cards = document.querySelectorAll('.rounded-lg.border.bg-card')
    expect(cards.length).toBeGreaterThan(0)

    cards.forEach(card => {
      expect(card).toHaveClass('text-card-foreground', 'shadow-sm', 'p-6')
    })
  })
})
