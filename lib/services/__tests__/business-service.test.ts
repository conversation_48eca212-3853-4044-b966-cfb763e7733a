import { businessService } from '../business-service'

// Mock Prisma
jest.mock('@/lib/db/prisma', () => ({
  prisma: {
    business: {
      findMany: jest.fn(),
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    scope: {
      findMany: jest.fn(),
    },
  },
}))

// Mock user service
jest.mock('../user-service', () => ({
  userService: {
    getUserScopes: jest.fn(),
  },
}))

import { prisma } from '@/lib/db/prisma'
import { userService } from '../user-service'

const mockPrisma = prisma as jest.Mocked<typeof prisma>
const mockUserService = userService as jest.Mocked<typeof userService>

describe('BusinessService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('getUserBusinesses', () => {
    it('returns businesses for user with proper scopes', async () => {
      const userId = 'test-user-id'
      const mockScopes = [
        { resourceType: 'BUSINESS', resourceId: 'business-1' },
        { resourceType: 'BUSINESS', resourceId: 'business-2' },
      ]
      const mockBusinesses = [
        {
          id: 'business-1',
          name: 'Business 1',
          country: { name: 'USA', code: 'US' },
          businessIndustries: [],
          compliance: [],
          _count: { compliance: 0, notes: 0, secureVaults: 0 },
        },
        {
          id: 'business-2',
          name: 'Business 2',
          country: { name: 'Canada', code: 'CA' },
          businessIndustries: [],
          compliance: [],
          _count: { compliance: 0, notes: 0, secureVaults: 0 },
        },
      ]

      mockUserService.getUserScopes.mockResolvedValue(mockScopes)
      mockPrisma.business.findMany.mockResolvedValue(mockBusinesses)

      const result = await businessService.getUserBusinesses(userId)

      expect(result).toEqual(mockBusinesses)
      expect(mockUserService.getUserScopes).toHaveBeenCalledWith(userId, 'BUSINESS')
      expect(mockPrisma.business.findMany).toHaveBeenCalledWith({
        where: {
          id: {
            in: ['business-1', 'business-2'],
          },
        },
        include: {
          country: true,
          businessIndustries: {
            include: {
              industry: true,
            },
          },
          compliance: {
            where: {
              status: {
                in: ['PENDING', 'OVERDUE'],
              },
            },
            take: 5,
          },
          _count: {
            select: {
              compliance: true,
              notes: true,
              secureVaults: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      })
    })

    it('returns empty array when user has no business scopes', async () => {
      const userId = 'test-user-id'
      mockUserService.getUserScopes.mockResolvedValue([])

      const result = await businessService.getUserBusinesses(userId)

      expect(result).toEqual([])
      expect(mockPrisma.business.findMany).toHaveBeenCalledWith({
        where: {
          id: {
            in: [],
          },
        },
        include: expect.any(Object),
        orderBy: {
          createdAt: 'desc',
        },
      })
    })

    it('handles database errors', async () => {
      const userId = 'test-user-id'
      mockUserService.getUserScopes.mockRejectedValue(new Error('Database error'))

      await expect(businessService.getUserBusinesses(userId)).rejects.toThrow('Database error')
    })
  })

  describe('createBusiness', () => {
    it('creates a new business successfully', async () => {
      const userId = 'test-user-id'
      const businessData = {
        name: 'New Business',
        description: 'Test description',
        countryId: 'country-1',
      }
      const mockCreatedBusiness = {
        id: 'new-business-id',
        ...businessData,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockPrisma.business.create.mockResolvedValue(mockCreatedBusiness)

      const result = await businessService.createBusiness(userId, businessData)

      expect(result).toEqual(mockCreatedBusiness)
      expect(mockPrisma.business.create).toHaveBeenCalledWith({
        data: {
          ...businessData,
          createdBy: userId,
        },
        include: expect.any(Object),
      })
    })

    it('handles creation errors', async () => {
      const userId = 'test-user-id'
      const businessData = {
        name: 'New Business',
        description: 'Test description',
        countryId: 'country-1',
      }

      mockPrisma.business.create.mockRejectedValue(new Error('Creation failed'))

      await expect(businessService.createBusiness(userId, businessData)).rejects.toThrow('Creation failed')
    })
  })

  describe('getBusinessById', () => {
    it('returns business when found', async () => {
      const businessId = 'business-1'
      const mockBusiness = {
        id: businessId,
        name: 'Test Business',
        description: 'Test description',
      }

      mockPrisma.business.findUnique.mockResolvedValue(mockBusiness)

      const result = await businessService.getBusinessById(businessId)

      expect(result).toEqual(mockBusiness)
      expect(mockPrisma.business.findUnique).toHaveBeenCalledWith({
        where: { id: businessId },
        include: expect.any(Object),
      })
    })

    it('returns null when business not found', async () => {
      const businessId = 'non-existent-id'
      mockPrisma.business.findUnique.mockResolvedValue(null)

      const result = await businessService.getBusinessById(businessId)

      expect(result).toBeNull()
    })
  })
})
