import { prisma } from '@/lib/db/prisma'
import type { 
  OnboardingFeatureModule, 
  BusinessOnboardingStatus, 
  OnboardingStep,
  OnboardingProgressUpdate,
  ONBOARDING_MODULES,
  ONBOARDING_STEP_CONFIG
} from '@/lib/types/onboarding'

export class OnboardingService {
  
  /**
   * Get business onboarding status with all steps
   */
  async getBusinessOnboardingStatus(businessId: string): Promise<BusinessOnboardingStatus> {
    // Get all progress records for this business
    const progressRecords = await prisma.businessOnboardingProgress.findMany({
      where: {
        businessId,
      },
    })

    // Create a map for quick lookup
    const progressMap = new Map(
      progressRecords.map(record => [record.featureModule as OnboardingFeatureModule, record])
    )

    // Build steps array with completion status
    const steps: OnboardingStep[] = (await import('@/lib/types/onboarding')).ONBOARDING_MODULES.map(module => {
      const config = (await import('@/lib/types/onboarding')).ONBOARDING_STEP_CONFIG[module]
      const progress = progressMap.get(module)
      
      return {
        module,
        title: config.title,
        description: config.description,
        actionUrl: config.actionUrl,
        icon: config.icon,
        completed: progress?.completed || false,
        completedAt: progress?.completedAt || undefined,
      }
    })

    const completedSteps = steps.filter(step => step.completed).length
    const totalSteps = steps.length
    const completionPercentage = Math.round((completedSteps / totalSteps) * 100)
    const isFullyOnboarded = completedSteps === totalSteps

    // Get last updated time
    const lastUpdated = progressRecords.length > 0 
      ? new Date(Math.max(...progressRecords.map(r => r.updatedAt.getTime())))
      : new Date()

    return {
      businessId,
      totalSteps,
      completedSteps,
      completionPercentage,
      isFullyOnboarded,
      steps,
      lastUpdated,
    }
  }

  /**
   * Update onboarding progress for a specific feature module
   */
  async updateOnboardingProgress(update: OnboardingProgressUpdate): Promise<void> {
    await prisma.businessOnboardingProgress.upsert({
      where: {
        businessId_featureModule: {
          businessId: update.businessId,
          featureModule: update.featureModule,
        },
      },
      update: {
        completed: update.completed,
        completedAt: update.completed ? new Date() : null,
      },
      create: {
        businessId: update.businessId,
        featureModule: update.featureModule,
        completed: update.completed,
        completedAt: update.completed ? new Date() : null,
      },
    })
  }

  /**
   * Mark multiple modules as completed (bulk update)
   */
  async updateMultipleModules(
    businessId: string, 
    modules: { module: OnboardingFeatureModule; completed: boolean }[]
  ): Promise<void> {
    const updates = modules.map(({ module, completed }) => 
      prisma.businessOnboardingProgress.upsert({
        where: {
          businessId_featureModule: {
            businessId,
            featureModule: module,
          },
        },
        update: {
          completed,
          completedAt: completed ? new Date() : null,
        },
        create: {
          businessId,
          featureModule: module,
          completed,
          completedAt: completed ? new Date() : null,
        },
      })
    )

    await prisma.$transaction(updates)
  }

  /**
   * Check if business is fully onboarded
   */
  async isBusinessFullyOnboarded(businessId: string): Promise<boolean> {
    const status = await this.getBusinessOnboardingStatus(businessId)
    return status.isFullyOnboarded
  }

  /**
   * Initialize onboarding for a new business
   */
  async initializeBusinessOnboarding(businessId: string): Promise<void> {
    const { ONBOARDING_MODULES } = await import('@/lib/types/onboarding')
    
    // Create initial progress records for all modules
    const initialRecords = ONBOARDING_MODULES.map(module => ({
      businessId,
      featureModule: module,
      completed: false,
    }))

    await prisma.businessOnboardingProgress.createMany({
      data: initialRecords,
      skipDuplicates: true, // In case some records already exist
    })
  }

  /**
   * Auto-detect and update onboarding progress based on actual data
   */
  async autoDetectProgress(businessId: string): Promise<void> {
    const updates: { module: OnboardingFeatureModule; completed: boolean }[] = []

    // Check Store: Has products/services
    const productCount = await this.checkStoreCompletion(businessId)
    updates.push({ module: 'store', completed: productCount > 0 })

    // Check Orders: Has orders/invoices
    const orderCount = await this.checkOrdersCompletion(businessId)
    updates.push({ module: 'orders', completed: orderCount > 0 })

    // Check Finance: Has transactions or payment methods
    const financeCompleted = await this.checkFinanceCompletion(businessId)
    updates.push({ module: 'finance', completed: financeCompleted })

    // Check CRM: Has customers/contacts
    const customerCount = await this.checkCrmCompletion(businessId)
    updates.push({ module: 'crm', completed: customerCount > 0 })

    // Check Compliance: Has compliance records
    const complianceCompleted = await this.checkComplianceCompletion(businessId)
    updates.push({ module: 'compliance', completed: complianceCompleted })

    // Check Documents: Has uploaded documents
    const documentCount = await this.checkDocumentsCompletion(businessId)
    updates.push({ module: 'documents', completed: documentCount > 0 })

    // Check Settings: Has customized business settings
    const settingsCompleted = await this.checkSettingsCompletion(businessId)
    updates.push({ module: 'settings', completed: settingsCompleted })

    // Update all progress
    await this.updateMultipleModules(businessId, updates)
  }

  // Helper methods for checking completion (implement based on your data models)
  private async checkStoreCompletion(businessId: string): Promise<number> {
    // TODO: Implement based on your product/service model
    // Example: return await prisma.product.count({ where: { businessId } })
    return 0
  }

  private async checkOrdersCompletion(businessId: string): Promise<number> {
    // TODO: Implement based on your order/invoice model
    // Example: return await prisma.order.count({ where: { businessId } })
    return 0
  }

  private async checkFinanceCompletion(businessId: string): Promise<boolean> {
    // TODO: Implement based on your transaction/payment model
    // Example: Check for transactions or connected payment methods
    return false
  }

  private async checkCrmCompletion(businessId: string): Promise<number> {
    // TODO: Implement based on your customer/contact model
    // Example: return await prisma.customer.count({ where: { businessId } })
    return 0
  }

  private async checkComplianceCompletion(businessId: string): Promise<boolean> {
    // TODO: Implement based on your compliance model
    // Example: Check for completed compliance tasks
    return false
  }

  private async checkDocumentsCompletion(businessId: string): Promise<number> {
    // TODO: Implement based on your document model
    // Example: return await prisma.document.count({ where: { businessId } })
    return 0
  }

  private async checkSettingsCompletion(businessId: string): Promise<boolean> {
    // TODO: Implement based on your business settings
    // Example: Check if business has logo, description, etc.
    const business = await prisma.business.findUnique({
      where: { id: businessId },
    })
    
    // Consider settings complete if business has basic info
    return !!(business?.description && business?.type)
  }
}

export const onboardingService = new OnboardingService()
