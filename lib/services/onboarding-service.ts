import { prisma } from '@/lib/db/prisma'
import type {
  OnboardingFeatureModule,
  BusinessOnboardingStatus,
  OnboardingStep,
  OnboardingProgressUpdate
} from '@/lib/types/onboarding'

export class OnboardingService {
  
  /**
   * Get business onboarding status with all steps
   */
  async getBusinessOnboardingStatus(businessId: string): Promise<BusinessOnboardingStatus> {
    let progressRecords: any[] = []

    try {
      // Get all progress records for this business
      if ('businessOnboardingProgress' in prisma) {
        progressRecords = await (prisma as any).businessOnboardingProgress.findMany({
          where: {
            businessId,
          },
        })
      }
    } catch (error) {
      console.warn('BusinessOnboardingProgress model not available yet:', error)
    }

    // Create a map for quick lookup
    const progressMap = new Map(
      progressRecords.map((record: any) => [record.featureModule as OnboardingFeatureModule, record])
    )

    // Build steps array with completion status
    const { ONBOARDING_MODULES, ONBOARDING_STEP_CONFIG } = await import('@/lib/types/onboarding')
    const steps: OnboardingStep[] = ONBOARDING_MODULES.map(module => {
      const config = ONBOARDING_STEP_CONFIG[module]
      const progress = progressMap.get(module)

      return {
        module,
        title: config.title,
        description: config.description,
        actionUrl: config.actionUrl,
        icon: config.icon,
        completed: progress?.completed || false,
        completedAt: progress?.completedAt || undefined,
      }
    })

    const completedSteps = steps.filter(step => step.completed).length
    const totalSteps = steps.length
    const completionPercentage = Math.round((completedSteps / totalSteps) * 100)
    const isFullyOnboarded = completedSteps === totalSteps

    // Get last updated time
    const lastUpdated = progressRecords.length > 0
      ? new Date(Math.max(...progressRecords.map((r: any) => r.updatedAt.getTime())))
      : new Date()

    return {
      businessId,
      totalSteps,
      completedSteps,
      completionPercentage,
      isFullyOnboarded,
      steps,
      lastUpdated,
    }
  }

  /**
   * Update onboarding progress for a specific feature module
   */
  async updateOnboardingProgress(update: OnboardingProgressUpdate): Promise<void> {
    try {
      if ('businessOnboardingProgress' in prisma) {
        await (prisma as any).businessOnboardingProgress.upsert({
          where: {
            businessId_featureModule: {
              businessId: update.businessId,
              featureModule: update.featureModule,
            },
          },
          update: {
            completed: update.completed,
            completedAt: update.completed ? new Date() : null,
          },
          create: {
            businessId: update.businessId,
            featureModule: update.featureModule,
            completed: update.completed,
            completedAt: update.completed ? new Date() : null,
          },
        })
      }
    } catch (error) {
      console.warn('BusinessOnboardingProgress model not available yet:', error)
    }
  }

  /**
   * Mark multiple modules as completed (bulk update)
   */
  async updateMultipleModules(
    businessId: string,
    modules: { module: OnboardingFeatureModule; completed: boolean }[]
  ): Promise<void> {
    try {
      if ('businessOnboardingProgress' in prisma) {
        const updates = modules.map(({ module, completed }) =>
          (prisma as any).businessOnboardingProgress.upsert({
            where: {
              businessId_featureModule: {
                businessId,
                featureModule: module,
              },
            },
            update: {
              completed,
              completedAt: completed ? new Date() : null,
            },
            create: {
              businessId,
              featureModule: module,
              completed,
              completedAt: completed ? new Date() : null,
            },
          })
        )

        await prisma.$transaction(updates)
      }
    } catch (error) {
      console.warn('BusinessOnboardingProgress model not available yet:', error)
    }
  }

  /**
   * Check if business is fully onboarded
   */
  async isBusinessFullyOnboarded(businessId: string): Promise<boolean> {
    const status = await this.getBusinessOnboardingStatus(businessId)
    return status.isFullyOnboarded
  }

  /**
   * Initialize onboarding for a new business
   */
  async initializeBusinessOnboarding(businessId: string): Promise<void> {
    try {
      if ('businessOnboardingProgress' in prisma) {
        const { ONBOARDING_MODULES } = await import('@/lib/types/onboarding')

        // Create initial progress records for all modules
        const initialRecords = ONBOARDING_MODULES.map(module => ({
          businessId,
          featureModule: module,
          completed: false,
        }))

        await (prisma as any).businessOnboardingProgress.createMany({
          data: initialRecords,
          skipDuplicates: true, // In case some records already exist
        })
      }
    } catch (error) {
      console.warn('BusinessOnboardingProgress model not available yet:', error)
    }
  }

  /**
   * Auto-detect and update onboarding progress based on actual data
   */
  async autoDetectProgress(businessId: string): Promise<void> {
    const updates: { module: OnboardingFeatureModule; completed: boolean }[] = []

    // Check Store: Has products/services
    const productCount = await this.checkStoreCompletion(businessId)
    updates.push({ module: 'store', completed: productCount > 0 })

    // Check Orders: Has orders/invoices
    const orderCount = await this.checkOrdersCompletion(businessId)
    updates.push({ module: 'orders', completed: orderCount > 0 })

    // Check Finance: Has transactions or payment methods
    const financeCompleted = await this.checkFinanceCompletion(businessId)
    updates.push({ module: 'finance', completed: financeCompleted })

    // Check CRM: Has customers/contacts
    const customerCount = await this.checkCrmCompletion(businessId)
    updates.push({ module: 'crm', completed: customerCount > 0 })

    // Check Compliance: Has compliance records
    const complianceCompleted = await this.checkComplianceCompletion(businessId)
    updates.push({ module: 'compliance', completed: complianceCompleted })

    // Check Documents: Has uploaded documents
    const documentCount = await this.checkDocumentsCompletion(businessId)
    updates.push({ module: 'documents', completed: documentCount > 0 })

    // Check Settings: Has customized business settings
    const settingsCompleted = await this.checkSettingsCompletion(businessId)
    updates.push({ module: 'settings', completed: settingsCompleted })

    // Update all progress
    await this.updateMultipleModules(businessId, updates)
  }

  // Helper methods for checking completion using actual database models
  private async checkStoreCompletion(businessId: string): Promise<number> {
    try {
      // Check if Product model exists in Prisma client
      if ('product' in prisma) {
        return await (prisma as any).product.count({
          where: {
            businessId,
            isActive: true
          }
        })
      }
    } catch (error) {
      console.warn('Product model not available yet:', error)
    }
    return 0
  }

  private async checkOrdersCompletion(businessId: string): Promise<number> {
    try {
      if ('order' in prisma) {
        return await (prisma as any).order.count({
          where: { businessId }
        })
      }
    } catch (error) {
      console.warn('Order model not available yet:', error)
    }
    return 0
  }

  private async checkFinanceCompletion(businessId: string): Promise<boolean> {
    try {
      let paymentCount = 0
      let invoiceCount = 0

      if ('payment' in prisma) {
        paymentCount = await (prisma as any).payment.count({
          where: { businessId }
        })
      }

      if ('invoice' in prisma) {
        invoiceCount = await (prisma as any).invoice.count({
          where: { businessId }
        })
      }

      return paymentCount > 0 || invoiceCount > 0
    } catch (error) {
      console.warn('Finance models not available yet:', error)
    }
    return false
  }

  private async checkCrmCompletion(businessId: string): Promise<number> {
    try {
      if ('customer' in prisma) {
        return await (prisma as any).customer.count({
          where: {
            businessId,
            status: 'ACTIVE'
          }
        })
      }
    } catch (error) {
      console.warn('Customer model not available yet:', error)
    }
    return 0
  }

  private async checkComplianceCompletion(_businessId: string): Promise<boolean> {
    // TODO: Implement based on your compliance model
    // Example: Check for completed compliance tasks
    return false
  }

  private async checkDocumentsCompletion(businessId: string): Promise<number> {
    try {
      // Check existing document-related models
      const noteCount = await prisma.note.count({
        where: { businessId }
      })

      // Check secure vault entries (documents stored securely)
      const vaultCount = await prisma.secureVault.count({
        where: { businessId }
      })

      return noteCount + vaultCount
    } catch (error) {
      console.warn('Document models not available yet:', error)
    }
    return 0
  }

  private async checkSettingsCompletion(businessId: string): Promise<boolean> {
    try {
      // Check if business has basic settings configured
      const business = await prisma.business.findUnique({
        where: { id: businessId },
        select: {
          name: true,
          type: true,
          settings: true
        }
      })

      // Consider settings complete if business has basic info
      return !!(business?.name && business?.type)
    } catch (error) {
      console.warn('Error checking settings completion:', error)
    }
    return false
  }
}

export const onboardingService = new OnboardingService()
