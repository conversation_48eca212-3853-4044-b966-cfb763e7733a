import { prisma } from '@/lib/db/prisma'
import type { CreateBusinessData, UpdateBusinessData } from '@/lib/types/business'
import { BusinessRegistrationStatus, BusinessSystemStatus, BusinessStatus } from '@prisma/client'

export class BusinessService {
  async getUserBusinesses(userId: string) {
    // First, get user's role assignments to find accessible businesses
    const roleAssignments = await prisma.roleAssignment.findMany({
      where: {
        userId,
        isActive: true,
        revokedAt: null,
      },
      include: {
        scope: true,
      },
    })

    // Extract business scope IDs
    const businessScopeIds = roleAssignments
      .filter(assignment => assignment.scope.type === 'BUSINESS')
      .map(assignment => assignment.scope.entityId)

    // Get businesses
    const businesses = await prisma.business.findMany({
      where: {
        id: {
          in: businessScopeIds,
        },
      },
      include: {
        country: true,
        businessIndustries: {
          include: {
            industry: true,
          },
        },
        compliance: {
          where: {
            status: {
              in: ['PENDING', 'OVERDUE'],
            },
          },
          take: 5,
        },
        _count: {
          select: {
            compliance: true,
            notes: true,
            secureVaults: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return businesses
  }

  async getBusinessById(businessId: string, userId?: string) {
    // Check if user has access to this business (if userId provided)
    if (userId) {
      const hasAccess = await this.checkUserBusinessAccess(userId, businessId)
      if (!hasAccess) {
        throw new Error('Access denied to this business')
      }
    }

    const business = await prisma.business.findUnique({
      where: { id: businessId },
      include: {
        country: true,
        businessIndustries: {
          include: {
            industry: true,
          },
        },
        compliance: {
          include: {
            template: true,
          },
          orderBy: {
            dueDate: 'asc',
          },
        },
        subscriptions: {
          where: {
            status: 'Active',
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
        },
        notes: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 10,
        },
        secureVaults: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 10,
        },
      },
    })

    return business
  }

  async createBusiness(userId: string, data: CreateBusinessData) {
    return await prisma.$transaction(async (tx) => {
      // Create the business
      const business = await tx.business.create({
        data: {
          name: data.name,
          type: data.type,
          countryId: data.countryId,
          registrationStatus: data.registrationStatus || BusinessRegistrationStatus.UNREGISTERED,
          systemStatus: BusinessSystemStatus.ACTIVE,
          businessStatus: BusinessStatus.ACTIVE,
          businessStatusUpdatedAt: new Date(),
          registrationNumber: data.registrationNumber || '',
          incorporationDate: data.incorporationDate || new Date(),
          settings: data.settings || {},
        },
        include: {
          country: true,
        },
      })

      // Create a scope for this business
      const scope = await tx.scope.create({
        data: {
          type: 'BUSINESS',
          entityId: business.id,
        },
      })

      // Create default owner role for the user
      const ownerRole = await tx.role.findFirst({
        where: {
          title: 'Owner',
          type: 'business',
        },
      })

      if (ownerRole) {
        await tx.roleAssignment.create({
          data: {
            userId,
            roleId: ownerRole.id,
            scopeId: scope.id,
            assignedBy: userId,
            isActive: true,
          },
        })
      }

      // Add industries if provided
      if (data.industryIds && data.industryIds.length > 0) {
        await tx.businessIndustry.createMany({
          data: data.industryIds.map(industryId => ({
            businessId: business.id,
            industryId,
          })),
        })
      }

      return business
    })
  }

  async updateBusiness(businessId: string, userId: string, data: UpdateBusinessData) {
    // Check access
    const hasAccess = await this.checkUserBusinessAccess(userId, businessId)
    if (!hasAccess) {
      throw new Error('Access denied to this business')
    }

    const business = await prisma.business.update({
      where: { id: businessId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
      include: {
        country: true,
        businessIndustries: {
          include: {
            industry: true,
          },
        },
      },
    })

    return business
  }

  async deleteBusiness(businessId: string, userId: string) {
    // Check access and ownership
    const hasOwnerAccess = await this.checkUserBusinessOwnership(userId, businessId)
    if (!hasOwnerAccess) {
      throw new Error('Only business owners can delete a business')
    }

    await prisma.$transaction(async (tx) => {
      // Soft delete by updating status
      await tx.business.update({
        where: { id: businessId },
        data: {
          systemStatus: BusinessSystemStatus.DEREGISTERED,
          businessStatus: BusinessStatus.CLOSED,
          businessStatusUpdatedAt: new Date(),
        },
      })

      // Deactivate all role assignments for this business
      const scope = await tx.scope.findFirst({
        where: {
          type: 'BUSINESS',
          entityId: businessId,
        },
      })

      if (scope) {
        await tx.roleAssignment.updateMany({
          where: {
            scopeId: scope.id,
            isActive: true,
          },
          data: {
            isActive: false,
            revokedAt: new Date(),
          },
        })
      }
    })
  }

  async checkUserBusinessAccess(userId: string, businessId: string): Promise<boolean> {
    const roleAssignment = await prisma.roleAssignment.findFirst({
      where: {
        userId,
        isActive: true,
        revokedAt: null,
        scope: {
          type: 'BUSINESS',
          entityId: businessId,
        },
      },
    })

    return !!roleAssignment
  }

  private async checkUserBusinessOwnership(userId: string, businessId: string): Promise<boolean> {
    const ownerRole = await prisma.role.findFirst({
      where: {
        title: 'Owner',
        type: 'business',
      },
    })

    if (!ownerRole) return false

    const roleAssignment = await prisma.roleAssignment.findFirst({
      where: {
        userId,
        roleId: ownerRole.id,
        isActive: true,
        revokedAt: null,
        scope: {
          type: 'BUSINESS',
          entityId: businessId,
        },
      },
    })

    return !!roleAssignment
  }
}

export const businessService = new BusinessService()
