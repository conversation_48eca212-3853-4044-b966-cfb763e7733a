import { prisma } from '@/lib/db/prisma'
import { auth, clerkClient } from '@clerk/nextjs/server'
import type { 
  UserSessionContext, 
  SessionBusiness, 
  SessionBusinessGroup,
  ClerkSessionMetadata,
  SwitchBusinessRequest,
  SwitchBusinessGroupRequest 
} from '@/lib/types/session'

export class SessionContextService {
  
  /**
   * Get complete user session context including businesses and groups
   */
  async getUserSessionContext(userId: string): Promise<UserSessionContext> {
    // Get user's business memberships
    const memberships = await prisma.userBusinessMembership.findMany({
      where: {
        userId,
        isActive: true,
      },
      include: {
        business: {
          include: {
            businessWithGroupRelationships: {
              include: {
                businessGroup: true,
              },
            },
          },
        },
        businessGroup: true,
      },
      orderBy: {
        lastAccessedAt: 'desc',
      },
    })

    // Transform to session format
    const businesses: SessionBusiness[] = memberships.map(membership => ({
      id: membership.business.id,
      name: membership.business.name,
      type: membership.business.type || 'Business',
      businessGroupId: membership.businessGroupId || undefined,
      businessGroupName: membership.businessGroup?.name,
      roleTitle: membership.roleTitle,
      isActive: membership.isActive,
    }))

    // Get unique business groups
    const businessGroups: SessionBusinessGroup[] = []
    const groupMap = new Map<string, SessionBusinessGroup>()
    
    memberships.forEach(membership => {
      if (membership.businessGroup && !groupMap.has(membership.businessGroup.id)) {
        const group: SessionBusinessGroup = {
          id: membership.businessGroup.id,
          name: membership.businessGroup.name,
          businessCount: 0,
          isActive: true,
        }
        groupMap.set(membership.businessGroup.id, group)
        businessGroups.push(group)
      }
    })

    // Count businesses per group
    businessGroups.forEach(group => {
      group.businessCount = businesses.filter(b => b.businessGroupId === group.id).length
    })

    // Get current context from Clerk session
    const { sessionClaims } = await auth()
    const metadata = sessionClaims?.metadata as ClerkSessionMetadata || {}

    const activeBusinessId = metadata.activeBusinessId || null
    const activeBusinessGroupId = metadata.activeBusinessGroupId || null

    return {
      userId,
      activeBusinessId,
      activeBusinessGroupId,
      accessibleBusinessIds: businesses.map(b => b.id),
      accessibleBusinessGroupIds: businessGroups.map(g => g.id),
      businesses,
      businessGroups,
      currentBusiness: activeBusinessId ? businesses.find(b => b.id === activeBusinessId) : undefined,
      currentBusinessGroup: activeBusinessGroupId ? businessGroups.find(g => g.id === activeBusinessGroupId) : undefined,
    }
  }

  /**
   * Switch user's active business context
   */
  async switchBusiness(userId: string, request: SwitchBusinessRequest): Promise<void> {
    // Verify user has access to this business
    const membership = await prisma.userBusinessMembership.findFirst({
      where: {
        userId,
        businessId: request.businessId,
        isActive: true,
      },
    })

    if (!membership) {
      throw new Error('Access denied to this business')
    }

    // Update last accessed time for the new business
    await this.updateLastAccessed(userId, request.businessId)

    // Update Clerk session metadata
    const { sessionId } = await auth()
    if (sessionId) {
      await clerkClient.sessions.updateSession(sessionId, {
        metadata: {
          activeBusinessId: request.businessId,
          activeBusinessGroupId: request.businessGroupId || membership.businessGroupId,
          lastSwitchedAt: new Date().toISOString(),
        },
      })
    }
  }

  /**
   * Switch user's active business group context
   */
  async switchBusinessGroup(userId: string, request: SwitchBusinessGroupRequest): Promise<void> {
    // Verify user has access to this business group
    const membership = await prisma.userBusinessMembership.findFirst({
      where: {
        userId,
        businessGroupId: request.businessGroupId,
        isActive: true,
      },
    })

    if (!membership) {
      throw new Error('Access denied to this business group')
    }

    // Get default business for this group if specified
    let defaultBusinessId = request.defaultBusinessId
    if (!defaultBusinessId) {
      const firstBusiness = await prisma.userBusinessMembership.findFirst({
        where: {
          userId,
          businessGroupId: request.businessGroupId,
          isActive: true,
        },
        orderBy: {
          lastAccessedAt: 'desc',
        },
      })
      defaultBusinessId = firstBusiness?.businessId
    }

    // Update Clerk session metadata
    const { sessionId } = await auth()
    if (sessionId) {
      await clerkClient.sessions.updateSession(sessionId, {
        metadata: {
          activeBusinessGroupId: request.businessGroupId,
          activeBusinessId: defaultBusinessId,
        },
      })
    }
  }

  /**
   * Initialize user's business context on first login
   */
  async initializeUserContext(userId: string): Promise<void> {
    // Sync user business memberships from role assignments
    await this.syncUserBusinessMemberships(userId)

    // Check if user already has an active business in session
    const { sessionClaims } = await auth()
    const metadata = sessionClaims?.metadata as ClerkSessionMetadata || {}

    if (metadata.activeBusinessId) {
      // Verify the user still has access to this business
      const hasAccess = await this.verifyBusinessAccess(userId, metadata.activeBusinessId)
      if (hasAccess) {
        // Update last accessed time for current business
        await this.updateLastAccessed(userId, metadata.activeBusinessId)
        return // User already has a valid active business
      }
    }

    // Get user's businesses ordered by last accessed (most recent first)
    const memberships = await prisma.userBusinessMembership.findMany({
      where: {
        userId,
        isActive: true,
      },
      orderBy: [
        { lastAccessedAt: 'desc' },
        { createdAt: 'asc' }, // Fallback to oldest if no lastAccessedAt
      ],
    })

    // Set default business if user has any
    if (memberships.length > 0) {
      const defaultMembership = memberships[0] // Most recently accessed or first created
      await this.switchBusiness(userId, {
        businessId: defaultMembership.businessId,
        businessGroupId: defaultMembership.businessGroupId || undefined,
      })
    }
  }

  /**
   * Verify user still has access to a business
   */
  private async verifyBusinessAccess(userId: string, businessId: string): Promise<boolean> {
    const membership = await prisma.userBusinessMembership.findFirst({
      where: {
        userId,
        businessId,
        isActive: true,
      },
    })
    return !!membership
  }

  /**
   * Update last accessed time for a business
   */
  private async updateLastAccessed(userId: string, businessId: string): Promise<void> {
    await prisma.userBusinessMembership.updateMany({
      where: {
        userId,
        businessId,
        isActive: true,
      },
      data: {
        lastAccessedAt: new Date(),
      },
    })
  }

  /**
   * Sync user business memberships from role assignments
   */
  async syncUserBusinessMemberships(userId: string): Promise<void> {
    // Get all active business role assignments for user
    const roleAssignments = await prisma.roleAssignment.findMany({
      where: {
        userId,
        isActive: true,
        revokedAt: null,
        scope: {
          type: 'BUSINESS',
        },
      },
      include: {
        role: true,
        scope: true,
      },
    })

    // Process each assignment
    for (const assignment of roleAssignments) {
      const businessId = assignment.scope.entityId

      // Get business and its group relationship
      const business = await prisma.business.findUnique({
        where: { id: businessId },
        include: {
          businessWithGroupRelationships: {
            include: {
              businessGroup: true,
            },
          },
        },
      })

      if (!business) continue

      const businessGroupId = business.businessWithGroupRelationships[0]?.businessGroupId

      // Upsert membership record
      await prisma.userBusinessMembership.upsert({
        where: {
          userId_businessId: {
            userId,
            businessId,
          },
        },
        update: {
          roleTitle: assignment.role.title,
          businessGroupId,
          isActive: true,
        },
        create: {
          userId,
          businessId,
          businessGroupId,
          roleTitle: assignment.role.title,
          isActive: true,
        },
      })
    }

    // Deactivate memberships for revoked role assignments
    const activeMembershipBusinessIds = roleAssignments.map(ra => ra.scope.entityId)
    await prisma.userBusinessMembership.updateMany({
      where: {
        userId,
        businessId: {
          notIn: activeMembershipBusinessIds,
        },
        isActive: true,
      },
      data: {
        isActive: false,
      },
    })
  }

  /**
   * Require business access - throws if user doesn't have access
   */
  async requireBusinessAccess(userId: string, businessId: string): Promise<void> {
    const membership = await prisma.userBusinessMembership.findFirst({
      where: {
        userId,
        businessId,
        isActive: true,
      },
    })

    if (!membership) {
      throw new Error('Access denied to this business')
    }
  }

  /**
   * Require business group access - throws if user doesn't have access
   */
  async requireBusinessGroupAccess(userId: string, businessGroupId: string): Promise<void> {
    const membership = await prisma.userBusinessMembership.findFirst({
      where: {
        userId,
        businessGroupId,
        isActive: true,
      },
    })

    if (!membership) {
      throw new Error('Access denied to this business group')
    }
  }

  /**
   * Get user's last accessed business (for smart defaults)
   */
  async getLastAccessedBusiness(userId: string): Promise<string | null> {
    const membership = await prisma.userBusinessMembership.findFirst({
      where: {
        userId,
        isActive: true,
        lastAccessedAt: {
          not: null,
        },
      },
      orderBy: {
        lastAccessedAt: 'desc',
      },
    })

    return membership?.businessId || null
  }

  /**
   * Get user's default business (last accessed or first created)
   */
  async getDefaultBusiness(userId: string): Promise<string | null> {
    // First try to get last accessed business
    const lastAccessed = await this.getLastAccessedBusiness(userId)
    if (lastAccessed) {
      return lastAccessed
    }

    // Fallback to first business user has access to
    const membership = await prisma.userBusinessMembership.findFirst({
      where: {
        userId,
        isActive: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    })

    return membership?.businessId || null
  }
}

export const sessionContextService = new SessionContextService()
