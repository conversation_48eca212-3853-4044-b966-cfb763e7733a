import { prisma } from '@/lib/db/prisma'
import type { ComplianceFilters, CreateComplianceData } from '@/lib/types/compliance'
import { ComplianceStatus } from '@prisma/client'

export class ComplianceService {
  async getCompliance(filters: ComplianceFilters & { userId: string }) {
    const { userId, businessId, status } = filters

    // Get user's accessible businesses
    const accessibleBusinesses = await this.getUserAccessibleBusinesses(userId)
    
    const whereClause: any = {
      businessId: {
        in: businessId ? [businessId] : accessibleBusinesses,
      },
    }

    if (status) {
      whereClause.status = status
    }

    const compliance = await prisma.compliance.findMany({
      where: whereClause,
      include: {
        business: {
          select: {
            id: true,
            name: true,
            country: {
              select: {
                name: true,
                code: true,
              },
            },
          },
        },
        template: {
          select: {
            name: true,
            description: true,
            frequency: true,
            regulatoryBody: true,
            category: true,
            penalty: true,
          },
        },
      },
      orderBy: [
        { status: 'asc' },
        { dueDate: 'asc' },
      ],
    })

    return compliance
  }

  async getComplianceById(complianceId: string, userId: string) {
    const compliance = await prisma.compliance.findUnique({
      where: { id: complianceId },
      include: {
        business: true,
        template: {
          include: {
            rules: {
              orderBy: { order: 'asc' },
            },
          },
        },
      },
    })

    if (!compliance) {
      throw new Error('Compliance not found')
    }

    // Check if user has access to this business
    const hasAccess = await this.checkUserBusinessAccess(userId, compliance.businessId)
    if (!hasAccess) {
      throw new Error('Access denied to this compliance')
    }

    return compliance
  }

  async createCompliance(userId: string, data: CreateComplianceData) {
    // Check if user has access to the business
    const hasAccess = await this.checkUserBusinessAccess(userId, data.businessId)
    if (!hasAccess) {
      throw new Error('Access denied to this business')
    }

    const compliance = await prisma.compliance.create({
      data: {
        businessId: data.businessId,
        templateId: data.templateId,
        status: data.status || ComplianceStatus.PENDING,
        dueDate: data.dueDate,
        penalty: data.penalty || '',
        responses: data.responses || {},
      },
      include: {
        business: true,
        template: true,
      },
    })

    return compliance
  }

  async updateCompliance(complianceId: string, userId: string, data: Partial<CreateComplianceData>) {
    const compliance = await prisma.compliance.findUnique({
      where: { id: complianceId },
      select: { businessId: true },
    })

    if (!compliance) {
      throw new Error('Compliance not found')
    }

    // Check access
    const hasAccess = await this.checkUserBusinessAccess(userId, compliance.businessId)
    if (!hasAccess) {
      throw new Error('Access denied to this compliance')
    }

    const updatedCompliance = await prisma.compliance.update({
      where: { id: complianceId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
      include: {
        business: true,
        template: true,
      },
    })

    return updatedCompliance
  }

  async getComplianceTemplates(countryId?: string) {
    const templates = await prisma.complianceTemplate.findMany({
      where: countryId ? { countryId } : undefined,
      include: {
        country: {
          select: {
            name: true,
            code: true,
          },
        },
        rules: {
          orderBy: { order: 'asc' },
        },
        _count: {
          select: {
            compliance: true,
          },
        },
      },
      orderBy: [
        { category: 'asc' },
        { name: 'asc' },
      ],
    })

    return templates
  }

  async getOverdueCompliance(userId: string) {
    const accessibleBusinesses = await this.getUserAccessibleBusinesses(userId)
    
    const overdueCompliance = await prisma.compliance.findMany({
      where: {
        businessId: {
          in: accessibleBusinesses,
        },
        dueDate: {
          lt: new Date(),
        },
        status: {
          not: ComplianceStatus.COMPLETED,
        },
      },
      include: {
        business: {
          select: {
            id: true,
            name: true,
          },
        },
        template: {
          select: {
            name: true,
            penalty: true,
          },
        },
      },
      orderBy: {
        dueDate: 'asc',
      },
    })

    // Update status to OVERDUE if not already
    const overdueIds = overdueCompliance
      .filter(c => c.status !== ComplianceStatus.OVERDUE)
      .map(c => c.id)

    if (overdueIds.length > 0) {
      await prisma.compliance.updateMany({
        where: {
          id: {
            in: overdueIds,
          },
        },
        data: {
          status: ComplianceStatus.OVERDUE,
        },
      })
    }

    return overdueCompliance
  }

  async getUpcomingCompliance(userId: string, days: number = 30) {
    const accessibleBusinesses = await this.getUserAccessibleBusinesses(userId)
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + days)

    const upcomingCompliance = await prisma.compliance.findMany({
      where: {
        businessId: {
          in: accessibleBusinesses,
        },
        dueDate: {
          gte: new Date(),
          lte: futureDate,
        },
        status: {
          in: [ComplianceStatus.PENDING, ComplianceStatus.IN_PROGRESS],
        },
      },
      include: {
        business: {
          select: {
            id: true,
            name: true,
          },
        },
        template: {
          select: {
            name: true,
            description: true,
          },
        },
      },
      orderBy: {
        dueDate: 'asc',
      },
    })

    return upcomingCompliance
  }

  private async getUserAccessibleBusinesses(userId: string): Promise<string[]> {
    const roleAssignments = await prisma.roleAssignment.findMany({
      where: {
        userId,
        isActive: true,
        revokedAt: null,
      },
      include: {
        scope: true,
      },
    })

    return roleAssignments
      .filter(assignment => assignment.scope.type === 'BUSINESS')
      .map(assignment => assignment.scope.entityId)
  }

  private async checkUserBusinessAccess(userId: string, businessId: string): Promise<boolean> {
    const roleAssignment = await prisma.roleAssignment.findFirst({
      where: {
        userId,
        isActive: true,
        revokedAt: null,
        scope: {
          type: 'BUSINESS',
          entityId: businessId,
        },
      },
    })

    return !!roleAssignment
  }
}

export const complianceService = new ComplianceService()
