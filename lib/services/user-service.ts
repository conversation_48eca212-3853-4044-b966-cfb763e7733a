import { prisma } from '@/lib/db/prisma'
import type { CreateUserData, UpdateUserData } from '@/lib/types/user'

export class UserService {
  async createUser(clerkUserId: string, data: CreateUserData) {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { id: clerkUserId },
    })

    if (existingUser) {
      return existingUser
    }

    return await prisma.$transaction(async (tx) => {
      // Create user
      const user = await tx.user.create({
        data: {
          id: clerkUserId,
          firstName: data.firstName,
          lastName: data.lastName,
          phone: data.phone,
          language: data.language || 'en',
          timezone: data.timezone || 'UTC',
        },
      })

      // Create email address if provided
      if (data.emailAddress) {
        await tx.emailAddresses.create({
          data: {
            userId: user.id,
            emailAddress: data.emailAddress,
            verified: data.emailVerified || false,
          },
        })
      }

      // Create user scope
      await tx.scope.create({
        data: {
          type: 'USER',
          entityId: user.id,
        },
      })

      return user
    })
  }

  async getUserById(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        emailAddresses: true,
        userSettings: true,
        roleAssignments: {
          where: {
            isActive: true,
            revokedAt: null,
          },
          include: {
            role: true,
            scope: true,
          },
        },
        _count: {
          select: {
            notes: true,
            secureVaults: true,
            documents: true,
          },
        },
      },
    })

    return user
  }

  async updateUser(userId: string, data: UpdateUserData) {
    const user = await prisma.user.update({
      where: { id: userId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
      include: {
        emailAddresses: true,
        userSettings: true,
      },
    })

    return user
  }

  async getUserSettings(userId: string) {
    const settings = await prisma.userSetting.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    })

    return settings
  }

  async updateUserSettings(userId: string, settingsData: Record<string, any>) {
    // Delete existing settings and create new ones
    await prisma.$transaction(async (tx) => {
      await tx.userSetting.deleteMany({
        where: { userId },
      })

      await tx.userSetting.create({
        data: {
          userId,
          data: settingsData,
        },
      })
    })

    return this.getUserSettings(userId)
  }

  async addEmailAddress(userId: string, emailAddress: string) {
    const email = await prisma.emailAddresses.create({
      data: {
        userId,
        emailAddress,
        verified: false,
      },
    })

    return email
  }

  async verifyEmailAddress(emailId: string) {
    const email = await prisma.emailAddresses.update({
      where: { id: emailId },
      data: { verified: true },
    })

    return email
  }

  async removeEmailAddress(emailId: string, userId: string) {
    // Check if this email belongs to the user
    const email = await prisma.emailAddresses.findFirst({
      where: {
        id: emailId,
        userId,
      },
    })

    if (!email) {
      throw new Error('Email address not found or access denied')
    }

    await prisma.emailAddresses.delete({
      where: { id: emailId },
    })
  }

  async getUserBusinesses(userId: string) {
    const roleAssignments = await prisma.roleAssignment.findMany({
      where: {
        userId,
        isActive: true,
        revokedAt: null,
      },
      include: {
        scope: true,
        role: true,
      },
    })

    const businessScopeIds = roleAssignments
      .filter(assignment => assignment.scope.type === 'BUSINESS')
      .map(assignment => assignment.scope.entityId)

    const businesses = await prisma.business.findMany({
      where: {
        id: {
          in: businessScopeIds,
        },
      },
      include: {
        country: true,
        _count: {
          select: {
            compliance: true,
          },
        },
      },
    })

    // Add role information to each business
    return businesses.map(business => {
      const roleAssignment = roleAssignments.find(
        assignment => assignment.scope.entityId === business.id
      )
      
      return {
        ...business,
        userRole: roleAssignment?.role.title,
        roleAssignedAt: roleAssignment?.createdAt,
      }
    })
  }

  async deleteUser(userId: string) {
    await prisma.$transaction(async (tx) => {
      // Soft delete by deactivating all role assignments
      await tx.roleAssignment.updateMany({
        where: {
          userId,
          isActive: true,
        },
        data: {
          isActive: false,
          revokedAt: new Date(),
        },
      })

      // Note: We don't actually delete the user record to maintain data integrity
      // Instead, we could add a 'deletedAt' field to the User model for soft deletion
      // For now, we'll just deactivate their access
    })
  }
}

export const userService = new UserService()
