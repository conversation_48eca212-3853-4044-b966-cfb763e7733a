'use client'

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { useUser } from '@clerk/nextjs'
import type { UserSessionContext, SessionBusiness, SessionBusinessGroup } from '@/lib/types/session'

interface BusinessContextType extends UserSessionContext {
  isLoading: boolean
  error: string | null
  switchBusiness: (businessId: string, businessGroupId?: string) => Promise<void>
  switchBusinessGroup: (businessGroupId: string, defaultBusinessId?: string) => Promise<void>
  refreshContext: () => Promise<void>
}

const BusinessContext = createContext<BusinessContextType | undefined>(undefined)

interface BusinessProviderProps {
  children: React.ReactNode
}

export function BusinessProvider({ children }: BusinessProviderProps) {
  const { user, isLoaded } = useUser()
  const [context, setContext] = useState<UserSessionContext | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchContext = useCallback(async () => {
    if (!user?.id) return

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/switch-business')
      if (!response.ok) {
        throw new Error('Failed to fetch business context')
      }

      const data: UserSessionContext = await response.json()

      // If no active business but user has businesses, initialize with default
      if (!data.activeBusinessId && data.businesses.length > 0) {
        await initializeDefaultBusiness()
        return
      }

      setContext(data)
    } catch (err) {
      console.error('Error fetching business context:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id])

  const initializeDefaultBusiness = useCallback(async () => {
    try {
      const response = await fetch('/api/default-business', {
        method: 'POST'
      })

      if (response.ok) {
        const data = await response.json()
        if (data.context) {
          setContext(data.context)
        }
      }
    } catch (err) {
      console.error('Error initializing default business:', err)
    }
  }, [])

  const switchBusiness = useCallback(async (businessId: string, businessGroupId?: string) => {
    try {
      setError(null)

      const response = await fetch('/api/switch-business', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessId,
          businessGroupId,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to switch business')
      }

      // Refresh context after successful switch
      await fetchContext()
      
      // Reload the page to ensure all components get the new context
      window.location.reload()
    } catch (err) {
      console.error('Error switching business:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
      throw err
    }
  }, [fetchContext])

  const switchBusinessGroup = useCallback(async (businessGroupId: string, defaultBusinessId?: string) => {
    try {
      setError(null)

      const response = await fetch('/api/switch-business-group', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessGroupId,
          defaultBusinessId,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to switch business group')
      }

      // Refresh context after successful switch
      await fetchContext()
      
      // Reload the page to ensure all components get the new context
      window.location.reload()
    } catch (err) {
      console.error('Error switching business group:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
      throw err
    }
  }, [fetchContext])

  const refreshContext = useCallback(async () => {
    await fetchContext()
  }, [fetchContext])

  // Fetch context when user is loaded
  useEffect(() => {
    if (isLoaded && user?.id) {
      fetchContext()
    } else if (isLoaded && !user?.id) {
      setIsLoading(false)
      setContext(null)
    }
  }, [isLoaded, user?.id, fetchContext])

  const value: BusinessContextType = {
    userId: context?.userId || '',
    activeBusinessId: context?.activeBusinessId || null,
    activeBusinessGroupId: context?.activeBusinessGroupId || null,
    accessibleBusinessIds: context?.accessibleBusinessIds || [],
    accessibleBusinessGroupIds: context?.accessibleBusinessGroupIds || [],
    businesses: context?.businesses || [],
    businessGroups: context?.businessGroups || [],
    currentBusiness: context?.currentBusiness,
    currentBusinessGroup: context?.currentBusinessGroup,
    isLoading,
    error,
    switchBusiness,
    switchBusinessGroup,
    refreshContext,
  }

  return (
    <BusinessContext.Provider value={value}>
      {children}
    </BusinessContext.Provider>
  )
}

export function useBusiness(): BusinessContextType {
  const context = useContext(BusinessContext)
  if (context === undefined) {
    throw new Error('useBusiness must be used within a BusinessProvider')
  }
  return context
}

export function useBusinessGroup(): SessionBusinessGroup | undefined {
  const { currentBusinessGroup } = useBusiness()
  return currentBusinessGroup
}

export function useCurrentBusiness(): SessionBusiness | undefined {
  const { currentBusiness } = useBusiness()
  return currentBusiness
}
