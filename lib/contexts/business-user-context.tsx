'use client'

import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react'
import { useVentureUser } from '@/lib/hooks/useVentureUser'
import { useBusiness } from './business-context'
import type { 
  BusinessUserContextData, 
  BusinessUserProviderProps 
} from '@/lib/types/business-user-context'
import { BUSINESS_PERMISSIONS, BUSINESS_ROLES } from '@/lib/types/business-user-context'

const BusinessUserContext = createContext<BusinessUserContextData | null>(null)

export function BusinessUserProvider({ 
  businessId, 
  children, 
  fallback,
  loadingFallback,
  errorFallback 
}: BusinessUserProviderProps) {
  const { user, isLoading, error, refetch } = useVentureUser({
    businessId,
    includeRoles: true,
    includePermissions: true,
    includeBusinessContext: true
  })
  
  const { currentBusiness } = useBusiness()
  const [lastBusinessId, setLastBusinessId] = useState<string | null>(null)

  // Force refetch when business changes
  useEffect(() => {
    if (businessId !== lastBusinessId) {
      setLastBusinessId(businessId)
      refetch()
    }
  }, [businessId, lastBusinessId, refetch])

  // Get business-specific user data
  const businessUser = useMemo(() => {
    if (!user || !businessId) return null
    
    return user.businessMemberships.find(
      membership => membership.businessId === businessId
    ) || null
  }, [user, businessId])

  // Permission helpers (scoped to current business)
  const hasPermission = useCallback((permission: string): boolean => {
    if (!user || !businessId) return false
    
    // Check if user has permission for this specific business
    return user.currentPermissions.some(p => 
      p.name === permission && 
      p.scope.entityId === businessId
    )
  }, [user, businessId])

  const hasRole = useCallback((role: string): boolean => {
    if (!user || !businessId) return false
    
    // Check if user has role for this specific business
    return user.currentRoles.some(r => 
      r.title.toLowerCase() === role.toLowerCase() && 
      r.scope.entityId === businessId
    )
  }, [user, businessId])

  const canAccess = useCallback((resource: string, action: string): boolean => {
    if (!user || !businessId) return false
    
    // Check if user can perform action on resource for this business
    return user.currentPermissions.some(p => 
      p.resource === resource && 
      p.action === action && 
      p.scope.entityId === businessId
    )
  }, [user, businessId])

  // Derived role checks
  const isOwner = useMemo(() => 
    hasRole(BUSINESS_ROLES.OWNER), 
    [hasRole]
  )
  
  const isAdmin = useMemo(() => 
    hasRole(BUSINESS_ROLES.ADMIN) || isOwner, 
    [hasRole, isOwner]
  )
  
  const isMember = useMemo(() => 
    hasRole(BUSINESS_ROLES.MEMBER) || isAdmin, 
    [hasRole, isAdmin]
  )

  // Derived permission checks
  const canManageUsers = useMemo(() => 
    hasPermission(BUSINESS_PERMISSIONS.USERS_MANAGE) || isOwner,
    [hasPermission, isOwner]
  )
  
  const canManageSettings = useMemo(() => 
    hasPermission(BUSINESS_PERMISSIONS.SETTINGS_MANAGE) || isAdmin,
    [hasPermission, isAdmin]
  )
  
  const canViewFinance = useMemo(() => 
    hasPermission(BUSINESS_PERMISSIONS.FINANCE_VIEW) || isAdmin,
    [hasPermission, isAdmin]
  )
  
  const canManageOrders = useMemo(() => 
    hasPermission(BUSINESS_PERMISSIONS.ORDERS_MANAGE) || isAdmin,
    [hasPermission, isAdmin]
  )

  // Get all permissions and roles for current business
  const permissions = useMemo(() => {
    if (!user || !businessId) return []
    
    return user.currentPermissions
      .filter(p => p.scope.entityId === businessId)
      .map(p => p.name)
  }, [user, businessId])

  const roles = useMemo(() => {
    if (!user || !businessId) return []
    
    return user.currentRoles
      .filter(r => r.scope.entityId === businessId)
      .map(r => r.title)
  }, [user, businessId])

  // Refresh function (alias for refetch)
  const refresh = useCallback(async () => {
    await refetch()
  }, [refetch])

  // Context value
  const contextValue: BusinessUserContextData = {
    user,
    businessUser,
    isLoading,
    error,
    businessId,
    businessName: businessUser?.businessName || currentBusiness?.name || null,
    userRole: businessUser?.userRole || null,
    hasPermission,
    hasRole,
    canAccess,
    isOwner,
    isAdmin,
    isMember,
    canManageUsers,
    canManageSettings,
    canViewFinance,
    canManageOrders,
    permissions,
    roles,
    refetch,
    refresh
  }

  // Loading state
  if (isLoading) {
    if (loadingFallback) {
      return <>{loadingFallback}</>
    }
    
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-3 text-gray-600">Loading business context...</span>
      </div>
    )
  }

  // Error state
  if (error) {
    if (errorFallback) {
      return <>{errorFallback}</>
    }
    
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="text-red-600 mb-2">Failed to load business context</div>
          <button 
            onClick={refetch}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            Try again
          </button>
        </div>
      </div>
    )
  }

  // No access to business
  if (!user || !businessUser) {
    if (fallback) {
      return <>{fallback}</>
    }
    
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="text-gray-600 mb-2">No access to this business</div>
          <p className="text-sm text-gray-500">
            You don't have permission to access this business workspace.
          </p>
        </div>
      </div>
    )
  }

  return (
    <BusinessUserContext.Provider value={contextValue}>
      {children}
    </BusinessUserContext.Provider>
  )
}

/**
 * Hook to access business-scoped user context
 * Can only be used within a BusinessUserProvider
 */
export function useBusinessUser(): BusinessUserContextData {
  const context = useContext(BusinessUserContext)
  
  if (!context) {
    throw new Error(
      'useBusinessUser must be used within a BusinessUserProvider. ' +
      'Make sure you wrap your business components with <BusinessUserProvider>.'
    )
  }
  
  return context
}

/**
 * Hook to check if component is within a business context
 */
export function useIsInBusinessContext(): boolean {
  const context = useContext(BusinessUserContext)
  return !!context
}
