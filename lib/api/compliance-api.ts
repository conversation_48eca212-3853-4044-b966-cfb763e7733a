import { apiClient } from './client'
import { API_ENDPOINTS } from './endpoints'
import type { 
  Compliance, 
  ComplianceTemplate, 
  CreateComplianceData,
  ComplianceFilters 
} from '@/lib/types/compliance'

export class ComplianceApi {
  async getCompliance(filters?: ComplianceFilters): Promise<Compliance[]> {
    const params = new URLSearchParams()
    if (filters?.businessId) params.append('businessId', filters.businessId)
    if (filters?.status) params.append('status', filters.status)
    if (filters?.dueDate) params.append('dueDate', filters.dueDate)
    
    const queryString = params.toString()
    const url = queryString ? `${API_ENDPOINTS.COMPLIANCE}?${queryString}` : API_ENDPOINTS.COMPLIANCE
    
    return apiClient.get<Compliance[]>(url)
  }

  async getComplianceById(id: string): Promise<Compliance> {
    return apiClient.get<Compliance>(API_ENDPOINTS.COMPLIANCE_BY_ID(id))
  }

  async createCompliance(data: CreateComplianceData): Promise<Compliance> {
    return apiClient.post<Compliance>(API_ENDPOINTS.COMPLIANCE, data)
  }

  async updateCompliance(id: string, data: Partial<Compliance>): Promise<Compliance> {
    return apiClient.put<Compliance>(API_ENDPOINTS.COMPLIANCE_BY_ID(id), data)
  }

  async deleteCompliance(id: string): Promise<void> {
    return apiClient.delete<void>(API_ENDPOINTS.COMPLIANCE_BY_ID(id))
  }

  async getComplianceTemplates(countryId?: string): Promise<ComplianceTemplate[]> {
    const url = countryId 
      ? `${API_ENDPOINTS.COMPLIANCE_TEMPLATES}?countryId=${countryId}`
      : API_ENDPOINTS.COMPLIANCE_TEMPLATES
    
    return apiClient.get<ComplianceTemplate[]>(url)
  }

  async getOverdueCompliance(): Promise<Compliance[]> {
    return apiClient.get<Compliance[]>(`${API_ENDPOINTS.COMPLIANCE}?status=OVERDUE`)
  }

  async getUpcomingCompliance(days: number = 30): Promise<Compliance[]> {
    const dueDate = new Date()
    dueDate.setDate(dueDate.getDate() + days)
    
    return apiClient.get<Compliance[]>(
      `${API_ENDPOINTS.COMPLIANCE}?dueBefore=${dueDate.toISOString()}`
    )
  }
}

// Export singleton instance
export const complianceApi = new ComplianceApi()
