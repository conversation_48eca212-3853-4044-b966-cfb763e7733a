// API endpoints configuration
// This makes it easy to update URLs when migrating to external API

export const API_ENDPOINTS = {
  // Business endpoints
  BUSINESSES: '/businesses',
  BUSINESS_BY_ID: (id: string) => `/businesses/${id}`,
  
  // Compliance endpoints
  COMPLIANCE: '/compliance',
  COMPLIANCE_BY_ID: (id: string) => `/compliance/${id}`,
  COMPLIANCE_TEMPLATES: '/compliance/templates',
  
  // Document endpoints
  DOCUMENTS: '/documents',
  DOCUMENT_BY_ID: (id: string) => `/documents/${id}`,
  DOCUMENT_UPLOAD: '/documents/upload',
  
  // User endpoints
  USERS: '/users',
  USER_PROFILE: '/users/profile',
  USER_SETTINGS: '/users/settings',
  
  // Role and Permission endpoints
  ROLES: '/roles',
  PERMISSIONS: '/permissions',
  ROLE_ASSIGNMENTS: '/role-assignments',
  
  // Integration endpoints
  INTEGRATIONS: '/integrations',
  INTEGRATION_BY_ID: (id: string) => `/integrations/${id}`,
  
  // Secure Vault endpoints
  SECURE_VAULTS: '/secure-vaults',
  SECURE_VAULT_BY_ID: (id: string) => `/secure-vaults/${id}`,
  
  // Notes endpoints
  NOTES: '/notes',
  NOTE_BY_ID: (id: string) => `/notes/${id}`,
  
  // Country and Industry endpoints
  COUNTRIES: '/countries',
  INDUSTRIES: '/industries',
  
  // Subscription endpoints
  SUBSCRIPTIONS: '/subscriptions',
  SUBSCRIPTION_BY_ID: (id: string) => `/subscriptions/${id}`,
} as const

// Environment-based API configuration
export const getApiConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production'
  const externalApiUrl = process.env.NEXT_PUBLIC_API_URL
  
  return {
    baseURL: externalApiUrl || '/api',
    isExternal: !!externalApiUrl,
    timeout: 10000,
    retries: 3,
  }
}
