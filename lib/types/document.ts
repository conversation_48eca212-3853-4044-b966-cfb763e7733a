import type {
  Document as PrismaDocument,
  User
} from '@prisma/client'

// Base Document type from Prisma
export type Document = PrismaDocument & {
  uploadedBy?: User
}

// Create Document Data
export interface CreateDocumentData {
  title: string
  category: string
  fileMimeType: string
  fileSize?: number
  source: 'External' | 'System' | 'User'
  filePath: string
  uploadedById?: string
}

// Update Document Data
export interface UpdateDocumentData {
  title?: string
  category?: string
  fileMimeType?: string
  fileSize?: number
  filePath?: string
}

// Document with metadata
export interface DocumentWithMetadata extends Document {
  metadata?: {
    businessId?: string
    businessName?: string
    complianceId?: string
    complianceName?: string
    tags?: string[]
    description?: string
  }
  permissions?: {
    canView: boolean
    canEdit: boolean
    canDelete: boolean
    canShare: boolean
  }
}

// Document Upload Data
export interface DocumentUploadData {
  file: File
  title: string
  category: string
  businessId?: string
  complianceId?: string
  tags?: string[]
  description?: string
}

// Document Filters
export interface DocumentFilters {
  search?: string
  category?: string
  businessId?: string
  complianceId?: string
  uploadedById?: string
  source?: 'External' | 'System' | 'User'
  fileMimeType?: string
  createdAfter?: Date
  createdBefore?: Date
  sortBy?: 'title' | 'category' | 'createdAt' | 'fileSize'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

// Document Category
export interface DocumentCategory {
  id: string
  name: string
  description?: string
  icon?: string
  color?: string
  documentCount: number
  isSystem: boolean
}

// Document Statistics
export interface DocumentStats {
  total: number
  byCategory: Record<string, number>
  bySource: Record<string, number>
  byMimeType: Record<string, number>
  totalSize: number
  averageSize: number
  recentUploads: Document[]
}

// Document Share
export interface DocumentShare {
  id: string
  documentId: string
  sharedWith: string // user ID or email
  sharedBy: string
  permissions: {
    canView: boolean
    canDownload: boolean
    canComment: boolean
  }
  expiresAt?: Date
  createdAt: Date
  accessCount: number
  lastAccessedAt?: Date
}

// Document Version
export interface DocumentVersion {
  id: string
  documentId: string
  version: number
  filePath: string
  fileSize: number
  uploadedBy: string
  uploadedAt: Date
  changeLog?: string
  isActive: boolean
}

// Document Comment
export interface DocumentComment {
  id: string
  documentId: string
  userId: string
  userName: string
  content: string
  createdAt: Date
  updatedAt: Date
  parentId?: string // for replies
  isResolved: boolean
}

// Document Activity
export interface DocumentActivity {
  id: string
  documentId: string
  userId: string
  userName: string
  action: 'uploaded' | 'viewed' | 'downloaded' | 'shared' | 'commented' | 'updated' | 'deleted'
  metadata?: Record<string, any>
  timestamp: Date
  ipAddress?: string
}

// Document Preview
export interface DocumentPreview {
  id: string
  title: string
  category: string
  fileMimeType: string
  fileSize: number
  thumbnailUrl?: string
  previewUrl?: string
  canPreview: boolean
  downloadUrl: string
}

// Bulk Document Operation
export interface BulkDocumentOperation {
  documentIds: string[]
  operation: 'delete' | 'move' | 'copy' | 'share' | 'categorize'
  targetCategory?: string
  targetBusinessId?: string
  shareSettings?: {
    userIds: string[]
    permissions: {
      canView: boolean
      canDownload: boolean
    }
    expiresAt?: Date
  }
}
