export interface BusinessContext {
  activeBusinessId: string | null
  activeBusinessGroupId: string | null
  accessibleBusinessIds: string[]
  accessibleBusinessGroupIds: string[]
}

export interface SessionBusiness {
  id: string
  name: string
  type: string
  logo?: string
  businessGroupId?: string
  businessGroupName?: string
  roleTitle: string
  isActive: boolean
}

export interface SessionBusinessGroup {
  id: string
  name: string
  description?: string
  businessCount: number
  isActive: boolean
}

export interface UserSessionContext extends BusinessContext {
  userId: string
  businesses: SessionBusiness[]
  businessGroups: SessionBusinessGroup[]
  currentBusiness?: SessionBusiness
  currentBusinessGroup?: SessionBusinessGroup
}

export interface SwitchBusinessRequest {
  businessId: string
  businessGroupId?: string
}

export interface SwitchBusinessGroupRequest {
  businessGroupId: string
  defaultBusinessId?: string
}

// Clerk session metadata structure
export interface ClerkSessionMetadata {
  activeBusinessId?: string
  activeBusinessGroupId?: string
  accessibleBusinessIds?: string[]
  accessibleBusinessGroupIds?: string[]
  lastSwitchedAt?: string
}

// WorkOS organization mapping
export interface WorkOSOrganizationMapping {
  workosOrgId: string
  businessGroupId: string
  defaultBusinessId?: string
  autoAssignRole?: string
}
