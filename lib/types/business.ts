import type { 
  Business as PrismaBusiness,
  BusinessRegistrationStatus,
  BusinessSystemStatus,
  BusinessStatus,
  Country,
  Industries,
  BusinessIndustry,
  Compliance,
  Subscription,
  Note,
  SecureVault
} from '@prisma/client'

// Base Business type from Prisma
export type Business = PrismaBusiness & {
  country: Country
  businessIndustries: (BusinessIndustry & {
    industry: Industries
  })[]
  compliance?: Compliance[]
  subscriptions?: Subscription[]
  notes?: Note[]
  secureVaults?: SecureVault[]
  _count?: {
    compliance: number
    notes: number
    secureVaults: number
  }
}

// Create Business Data
export interface CreateBusinessData {
  name: string
  type?: string
  countryId: string
  registrationStatus?: BusinessRegistrationStatus
  registrationNumber?: string
  incorporationDate?: Date
  settings?: Record<string, any>
  industryIds?: string[]
}

// Update Business Data
export interface UpdateBusinessData {
  name?: string
  type?: string
  countryId?: string
  registrationStatus?: BusinessRegistrationStatus
  systemStatus?: BusinessSystemStatus
  businessStatus?: BusinessStatus
  registrationNumber?: string
  incorporationDate?: Date
  settings?: Record<string, any>
}

// Business with additional computed fields
export interface BusinessWithRole extends Business {
  userRole?: string
  roleAssignedAt?: Date
}

// Business Summary for lists
export interface BusinessSummary {
  id: string
  name: string
  type?: string
  country: {
    name: string
    code: string
  }
  registrationStatus: BusinessRegistrationStatus
  systemStatus: BusinessSystemStatus
  businessStatus: BusinessStatus
  complianceCount: number
  overdueComplianceCount: number
  createdAt: Date
}

// Business Statistics
export interface BusinessStats {
  totalBusinesses: number
  activeBusinesses: number
  pendingCompliance: number
  overdueCompliance: number
  totalDocuments: number
  recentActivity: {
    type: 'compliance' | 'document' | 'note'
    title: string
    date: Date
    businessName: string
  }[]
}

// Business Filters
export interface BusinessFilters {
  search?: string
  countryId?: string
  registrationStatus?: BusinessRegistrationStatus
  systemStatus?: BusinessSystemStatus
  businessStatus?: BusinessStatus
  industryId?: string
  sortBy?: 'name' | 'createdAt' | 'updatedAt'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

// Business Industry Assignment
export interface BusinessIndustryAssignment {
  businessId: string
  industryIds: string[]
}

// Export Prisma enums for convenience
export { BusinessRegistrationStatus, BusinessSystemStatus, BusinessStatus }
