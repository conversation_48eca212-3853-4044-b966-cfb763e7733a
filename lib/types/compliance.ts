import type {
  Compliance as PrismaCompliance,
  ComplianceTemplate as PrismaComplianceTemplate,
  ComplianceRule as PrismaComplianceRule,
  ComplianceStatus,
  Business,
  Country
} from '@prisma/client'

// Base Compliance type from Prisma
export type Compliance = PrismaCompliance & {
  business: {
    id: string
    name: string
    country: {
      name: string
      code: string
    }
  }
  template: {
    name: string
    description: string
    frequency: string
    regulatoryBody: string
    category: string
    penalty: string
  }
}

// Compliance Template with relations
export type ComplianceTemplate = PrismaComplianceTemplate & {
  country: {
    name: string
    code: string
  }
  rules: ComplianceRule[]
  _count: {
    compliance: number
  }
}

// Compliance Rule
export type ComplianceRule = PrismaComplianceRule

// Create Compliance Data
export interface CreateComplianceData {
  businessId: string
  templateId: string
  status?: ComplianceStatus
  dueDate?: Date
  penalty?: string
  responses?: Record<string, any>
}

// Update Compliance Data
export interface UpdateComplianceData {
  status?: ComplianceStatus
  dueDate?: Date
  penalty?: string
  responses?: Record<string, any>
}

// Compliance Filters
export interface ComplianceFilters {
  businessId?: string
  status?: ComplianceStatus
  templateId?: string
  countryId?: string
  regulatoryBody?: string
  category?: string
  dueDate?: string
  dueBefore?: Date
  dueAfter?: Date
  search?: string
  sortBy?: 'dueDate' | 'status' | 'createdAt' | 'updatedAt'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

// Compliance Summary for dashboard
export interface ComplianceSummary {
  id: string
  businessName: string
  templateName: string
  status: ComplianceStatus
  dueDate?: Date
  penalty: string
  daysUntilDue?: number
  isOverdue: boolean
}

// Compliance Statistics
export interface ComplianceStats {
  total: number
  pending: number
  inProgress: number
  completed: number
  overdue: number
  byCategory: Record<string, number>
  byRegulatoryBody: Record<string, number>
  upcomingDeadlines: ComplianceSummary[]
}

// Compliance Dashboard Data
export interface ComplianceDashboard {
  stats: ComplianceStats
  recentCompliance: Compliance[]
  overdueCompliance: Compliance[]
  upcomingCompliance: Compliance[]
  complianceByBusiness: {
    businessId: string
    businessName: string
    total: number
    pending: number
    overdue: number
  }[]
}

// Compliance Template Filters
export interface ComplianceTemplateFilters {
  countryId?: string
  category?: string
  regulatoryBody?: string
  frequency?: string
  search?: string
}

// Compliance Rule Configuration
export interface ComplianceRuleConfig {
  min?: number
  max?: number
  options?: string[]
  maxFileSize?: number
  minFileSize?: number
  maxFileCount?: number
  minFileCount?: number
  fileTypes?: string[]
  placeholder?: string
  helpText?: string
  validation?: {
    pattern?: string
    message?: string
  }
}

// Compliance Response
export interface ComplianceResponse {
  ruleId: string
  value: any
  files?: {
    name: string
    url: string
    type: string
    size: number
  }[]
}

// Compliance Submission
export interface ComplianceSubmission {
  complianceId: string
  responses: ComplianceResponse[]
  status: ComplianceStatus
  submittedAt: Date
  submittedBy: string
}

// Export Prisma enums for convenience
export { ComplianceStatus }
