import type {
  User as PrismaUser,
  Em<PERSON>Addresses,
  userSetting,
  RoleAssignment,
  Role,
  Scope
} from '@prisma/client'

// Base User type from Prisma
export type User = PrismaUser & {
  emailAddresses: EmailAddresses[]
  userSettings: userSetting[]
  roleAssignments: (RoleAssignment & {
    role: Role
    scope: Scope
  })[]
  _count: {
    notes: number
    secureVaults: number
    documents: number
  }
}

// Create User Data
export interface CreateUserData {
  firstName: string
  lastName: string
  phone: string
  language?: string
  timezone?: string
  emailAddress?: string
  emailVerified?: boolean
}

// Update User Data
export interface UpdateUserData {
  firstName?: string
  lastName?: string
  phone?: string
  language?: string
  timezone?: string
}

// User Profile
export interface UserProfile {
  id: string
  firstName: string
  lastName: string
  fullName: string
  phone: string
  language: string
  timezone: string
  emailAddresses: EmailAddresses[]
  primaryEmail?: string
  createdAt: Date
  updatedAt: Date
}

// User Settings
export interface UserSettings {
  theme?: 'light' | 'dark' | 'system'
  language: string
  timezone: string
  notifications: {
    email: boolean
    push: boolean
    compliance: boolean
    documents: boolean
    reminders: boolean
  }
  dashboard: {
    defaultView: 'overview' | 'businesses' | 'compliance'
    itemsPerPage: number
    showWelcome: boolean
  }
  privacy: {
    profileVisibility: 'public' | 'private'
    shareAnalytics: boolean
  }
}

// User Role Information
export interface UserRole {
  id: string
  title: string
  type: string
  scopeType: string
  scopeEntityId: string
  scopeEntityName?: string
  assignedAt: Date
  assignedBy?: string
  isActive: boolean
}

// User Business Access
export interface UserBusinessAccess {
  businessId: string
  businessName: string
  role: string
  permissions: string[]
  assignedAt: Date
  isActive: boolean
}

// User Activity
export interface UserActivity {
  id: string
  type: 'login' | 'business_created' | 'compliance_updated' | 'document_uploaded' | 'note_created'
  description: string
  metadata?: Record<string, any>
  timestamp: Date
  ipAddress?: string
  userAgent?: string
}

// User Statistics
export interface UserStats {
  businessesCount: number
  complianceCount: number
  documentsCount: number
  notesCount: number
  secureVaultsCount: number
  lastLoginAt?: Date
  accountAge: number // in days
  activityScore: number
}

// User Preferences
export interface UserPreferences {
  emailNotifications: {
    complianceReminders: boolean
    documentSharing: boolean
    systemUpdates: boolean
    marketingEmails: boolean
  }
  dashboardLayout: {
    widgets: string[]
    layout: 'grid' | 'list'
    density: 'compact' | 'comfortable' | 'spacious'
  }
  accessibility: {
    highContrast: boolean
    largeText: boolean
    reducedMotion: boolean
  }
}

// User Invitation
export interface UserInvitation {
  id: string
  email: string
  businessId: string
  businessName: string
  roleId: string
  roleName: string
  invitedBy: string
  invitedByName: string
  expiresAt: Date
  acceptedAt?: Date
  status: 'pending' | 'accepted' | 'expired' | 'revoked'
}

// User Session
export interface UserSession {
  id: string
  userId: string
  deviceInfo: {
    browser: string
    os: string
    device: string
  }
  location?: {
    country: string
    city: string
    ip: string
  }
  createdAt: Date
  lastActiveAt: Date
  isActive: boolean
}

// User Filters
export interface UserFilters {
  search?: string
  role?: string
  businessId?: string
  isActive?: boolean
  createdAfter?: Date
  createdBefore?: Date
  sortBy?: 'firstName' | 'lastName' | 'createdAt' | 'lastActiveAt'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}
