import { useState, useEffect, useCallback, useMemo } from 'react'
import { useUser } from '@clerk/nextjs'
import { useBusiness } from '@/lib/contexts/business-context'
import type { VentureUser, VentureUserContextData, ResolveUserOptions } from '@/lib/types/venture-user'

interface UseVentureUserOptions extends ResolveUserOptions {
  autoFetch?: boolean
  cacheTimeout?: number // Cache timeout in milliseconds
}

interface CachedVentureUser {
  user: VentureUser
  timestamp: number
  businessId?: string
}

// Global cache for VentureUser data
const ventureUserCache = new Map<string, CachedVentureUser>()

export function useVentureUser(options: UseVentureUserOptions = {}): VentureUserContextData {
  const { user: clerkUser, isLoaded } = useUser()
  const { currentBusiness } = useBusiness()
  
  const {
    autoFetch = true,
    cacheTimeout = 5 * 60 * 1000, // 5 minutes default
    includeRoles = true,
    includePermissions = true,
    includeBusinessContext = true,
    businessId,
    businessGroupId
  } = options

  const [ventureUser, setVentureUser] = useState<VentureUser | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Use current business context if not explicitly provided
  const contextBusinessId = businessId || currentBusiness?.id
  const contextBusinessGroupId = businessGroupId || currentBusiness?.businessGroupId

  // Cache key based on user and business context
  const cacheKey = useMemo(() => {
    if (!clerkUser?.id) return null
    return `${clerkUser.id}-${contextBusinessId || 'no-business'}-${contextBusinessGroupId || 'no-group'}`
  }, [clerkUser?.id, contextBusinessId, contextBusinessGroupId])

  // Check cache for valid data
  const getCachedUser = useCallback((): VentureUser | null => {
    if (!cacheKey) return null
    
    const cached = ventureUserCache.get(cacheKey)
    if (!cached) return null
    
    const isExpired = Date.now() - cached.timestamp > cacheTimeout
    if (isExpired) {
      ventureUserCache.delete(cacheKey)
      return null
    }
    
    return cached.user
  }, [cacheKey, cacheTimeout])

  // Cache user data
  const setCachedUser = useCallback((user: VentureUser) => {
    if (!cacheKey) return
    
    ventureUserCache.set(cacheKey, {
      user,
      timestamp: Date.now(),
      businessId: contextBusinessId
    })
  }, [cacheKey, contextBusinessId])

  // Fetch VentureUser from API
  const fetchVentureUser = useCallback(async () => {
    if (!isLoaded || !clerkUser?.id) return

    // Check cache first
    const cachedUser = getCachedUser()
    if (cachedUser) {
      setVentureUser(cachedUser)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      const params = new URLSearchParams()
      if (includeRoles) params.append('includeRoles', 'true')
      if (includePermissions) params.append('includePermissions', 'true')
      if (includeBusinessContext) params.append('includeBusinessContext', 'true')
      if (contextBusinessId) params.append('businessId', contextBusinessId)
      if (contextBusinessGroupId) params.append('businessGroupId', contextBusinessGroupId)

      const response = await fetch(`/api/venture-user?${params.toString()}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          // VentureUser doesn't exist, create it
          await createVentureUser()
          return
        }
        throw new Error('Failed to fetch VentureUser')
      }

      const user: VentureUser = await response.json()
      setVentureUser(user)
      setCachedUser(user)
    } catch (err) {
      console.error('Error fetching VentureUser:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }, [
    isLoaded, 
    clerkUser?.id, 
    includeRoles, 
    includePermissions, 
    includeBusinessContext,
    contextBusinessId,
    contextBusinessGroupId,
    getCachedUser,
    setCachedUser
  ])

  // Create VentureUser if it doesn't exist
  const createVentureUser = useCallback(async () => {
    if (!clerkUser?.id) return

    try {
      const response = await fetch('/api/venture-user', {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Failed to create VentureUser')
      }

      const result = await response.json()
      setVentureUser(result.user)
      setCachedUser(result.user)
    } catch (err) {
      console.error('Error creating VentureUser:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    }
  }, [clerkUser?.id, setCachedUser])

  // Refetch user data (bypasses cache)
  const refetch = useCallback(async () => {
    if (cacheKey) {
      ventureUserCache.delete(cacheKey)
    }
    await fetchVentureUser()
  }, [cacheKey, fetchVentureUser])

  // Permission helper functions
  const hasPermission = useCallback((permission: string, businessId?: string): boolean => {
    if (!ventureUser) return false
    
    const targetBusinessId = businessId || contextBusinessId
    
    // Check current permissions for the business context
    if (targetBusinessId === contextBusinessId) {
      return ventureUser.currentPermissions.some(p => p.name === permission)
    }
    
    // For other businesses, we'd need to fetch separately or check global permissions
    return ventureUser.globalRoles.some(role => 
      role.permissions.includes(permission)
    )
  }, [ventureUser, contextBusinessId])

  const hasRole = useCallback((role: string, businessId?: string): boolean => {
    if (!ventureUser) return false
    
    const targetBusinessId = businessId || contextBusinessId
    
    // Check current roles for the business context
    if (targetBusinessId === contextBusinessId) {
      return ventureUser.currentRoles.some(r => r.title === role)
    }
    
    // Check global roles
    return ventureUser.globalRoles.some(r => r.title === role)
  }, [ventureUser, contextBusinessId])

  const canAccess = useCallback((resource: string, action: string, businessId?: string): boolean => {
    if (!ventureUser) return false
    
    const targetBusinessId = businessId || contextBusinessId
    
    // Check current permissions for the business context
    if (targetBusinessId === contextBusinessId) {
      return ventureUser.currentPermissions.some(p => 
        p.resource === resource && p.action === action
      )
    }
    
    return false
  }, [ventureUser, contextBusinessId])

  const getCurrentBusinessRole = useCallback((): string | null => {
    if (!ventureUser || !contextBusinessId) return null
    
    const membership = ventureUser.businessMemberships.find(
      m => m.businessId === contextBusinessId
    )
    
    return membership?.userRole || null
  }, [ventureUser, contextBusinessId])

  const getCurrentBusinessPermissions = useCallback((): string[] => {
    if (!ventureUser) return []
    return ventureUser.currentPermissions.map(p => p.name)
  }, [ventureUser])

  const isOwnerOfBusiness = useCallback((businessId: string): boolean => {
    if (!ventureUser) return false
    
    const membership = ventureUser.businessMemberships.find(
      m => m.businessId === businessId
    )
    
    return membership?.userRole.toLowerCase().includes('owner') || false
  }, [ventureUser])

  const isAdminOfBusiness = useCallback((businessId: string): boolean => {
    if (!ventureUser) return false
    
    const membership = ventureUser.businessMemberships.find(
      m => m.businessId === businessId
    )
    
    const role = membership?.userRole.toLowerCase() || ''
    return role.includes('admin') || role.includes('owner')
  }, [ventureUser])

  // Auto-fetch when dependencies change
  useEffect(() => {
    if (autoFetch && isLoaded && clerkUser?.id) {
      fetchVentureUser()
    }
  }, [autoFetch, isLoaded, clerkUser?.id, contextBusinessId, fetchVentureUser])

  // Clear cache when user changes
  useEffect(() => {
    if (!clerkUser?.id) {
      setVentureUser(null)
      ventureUserCache.clear()
    }
  }, [clerkUser?.id])

  return {
    user: ventureUser,
    isLoading,
    error,
    refetch,
    hasPermission,
    hasRole,
    canAccess,
    getCurrentBusinessRole,
    getCurrentBusinessPermissions,
    isOwnerOfBusiness,
    isAdminOfBusiness
  }
}

/**
 * Simplified hook for basic user data without business context
 */
export function useVentureUserBasic() {
  return useVentureUser({
    includeRoles: false,
    includePermissions: false,
    includeBusinessContext: false
  })
}

/**
 * Hook for checking specific permission
 */
export function usePermission(permission: string, businessId?: string) {
  const { hasPermission, isLoading } = useVentureUser()
  
  return {
    hasPermission: hasPermission(permission, businessId),
    isLoading
  }
}

/**
 * Hook for checking specific role
 */
export function useRole(role: string, businessId?: string) {
  const { hasRole, isLoading } = useVentureUser()
  
  return {
    hasRole: hasRole(role, businessId),
    isLoading
  }
}
