// Clerk configuration and utilities

export const clerkConfig = {
  // Appearance customization
  appearance: {
    elements: {
      formButtonPrimary: 'bg-primary hover:bg-primary/90 text-primary-foreground',
      card: 'shadow-lg border',
      headerTitle: 'text-foreground',
      headerSubtitle: 'text-muted-foreground',
      socialButtonsBlockButton: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
      formFieldInput: 'border border-input bg-background',
      footerActionLink: 'text-primary hover:text-primary/90',
    },
    variables: {
      colorPrimary: 'hsl(var(--primary))',
      colorBackground: 'hsl(var(--background))',
      colorInputBackground: 'hsl(var(--background))',
      colorInputText: 'hsl(var(--foreground))',
    },
  },
  
  // Sign-in configuration
  signIn: {
    redirectUrl: '/dashboard',
    fallbackRedirectUrl: '/dashboard',
  },
  
  // Sign-up configuration
  signUp: {
    redirectUrl: '/dashboard',
    fallbackRedirectUrl: '/dashboard',
  },
  
  // User profile configuration
  userProfile: {
    appearance: {
      elements: {
        card: 'shadow-lg border',
        navbar: 'bg-background border-b',
        navbarButton: 'text-foreground hover:bg-accent',
        pageScrollBox: 'bg-background',
      },
    },
  },
}

// Helper function to get redirect URL from query params
export function getRedirectUrl(searchParams: URLSearchParams): string {
  const redirectUrl = searchParams.get('redirect_url')
  
  // Validate redirect URL to prevent open redirects
  if (redirectUrl && isValidRedirectUrl(redirectUrl)) {
    return redirectUrl
  }
  
  return '/dashboard'
}

// Validate redirect URL to prevent open redirects
function isValidRedirectUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url, window.location.origin)
    
    // Only allow same-origin redirects
    return parsedUrl.origin === window.location.origin
  } catch {
    // If URL parsing fails, treat as invalid
    return false
  }
}

// User metadata interface for Clerk
export interface ClerkUserMetadata {
  businessIds?: string[]
  primaryBusinessId?: string
  onboardingCompleted?: boolean
  lastLoginAt?: string
  preferences?: {
    theme?: 'light' | 'dark' | 'system'
    language?: string
    timezone?: string
  }
}

// Helper to update user metadata
export async function updateUserMetadata(
  userId: string,
  metadata: Partial<ClerkUserMetadata>
) {
  // This would be implemented with Clerk's backend API
  // For now, we'll use the frontend user object
  console.log('Updating user metadata:', { userId, metadata })
}
