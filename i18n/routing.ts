import { defineRouting } from 'next-intl/routing'

export const routing = defineRouting({
  // A list of all locales that are supported
  locales: ['en', 'fr', 'es', 'de', 'ie', 'ng', 'ee'],

  // Used when no locale matches
  defaultLocale: 'en',

  // Use locale prefix only when needed (cleaner URLs for default locale)
  localePrefix: 'never',

  // Enable automatic locale detection
  localeDetection: true,

  // Paths that should not be internationalized
  pathnames: {
    // '/': '/',
    // '/dashboard': '/dashboard',
    // '/businesses': '/businesses',
    // '/compliance': '/compliance',
    // '/documents': '/documents',
    // '/settings': '/settings',
    // '/auth/sign-in': '/auth/sign-in',
    // '/auth/sign-up': '/auth/sign-up',
  }
})
