{"extends": ["next/core-web-vitals", "next/typescript"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "@typescript-eslint/no-non-null-assertion": "warn", "react/no-unescaped-entities": "warn", "react-hooks/exhaustive-deps": "warn", "import/no-anonymous-default-export": "warn", "no-console": "warn", "no-debugger": "warn", "prefer-const": "warn"}}