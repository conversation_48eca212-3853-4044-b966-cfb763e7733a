import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
import createIntlMiddleware from 'next-intl/middleware'
import { routing } from './i18n/routing'

// Create the internationalization middleware
const intlMiddleware = createIntlMiddleware(routing)

// Define public routes that don't require authentication
const isPublicRoute = createRouteMatcher([
  '/',
  '/(en|fr|es|de|ie|ng|ee)',
  '/(en|fr|es|de|ie|ng|ee)/auth/sign-in(.*)',
  '/(en|fr|es|de|ie|ng|ee)/auth/sign-up(.*)',
  '/auth/sign-in(.*)',
  '/auth/sign-up(.*)',
  '/api/health',
  '/api/webhooks(.*)',
])

// Define API routes that need authentication
const isApiRoute = createRouteMatcher(['/api(.*)'])

// Define dashboard routes that need authentication
const isDashboardRoute = createRouteMatcher([
  '/dashboard(.*)',
  '/businesses(.*)',
  '/settings(.*)',
  '/(en|fr|es|de|ie|ng|ee)/dashboard(.*)',
  '/(en|fr|es|de|ie|ng|ee)/businesses(.*)',
  '/(en|fr|es|de|ie|ng|ee)/settings(.*)',
])

// Define business-scoped routes that need authentication and business access
const isBusinessRoute = createRouteMatcher([
  '/(en|fr|es|de|ie|ng|ee)/[^/]+/(dashboard|store|orders|inventory|crm|finance|compliance|documents|secure-vault|planner|hr|roles|marketing|analytics|integrations|business-groups|logistics|settings|profile)(.*)',
  '/[^/]+/(dashboard|store|orders|inventory|crm|finance|compliance|documents|secure-vault|planner|hr|roles|marketing|analytics|integrations|business-groups|logistics|settings|profile)(.*)',
])

export default clerkMiddleware(async (auth, req) => {
  const { userId } = await auth()
  const { pathname } = req.nextUrl

  // Handle API routes first (no i18n needed)
  if (isApiRoute(req)) {
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    return NextResponse.next()
  }

  // Apply internationalization middleware for non-API routes
  const intlResponse = intlMiddleware(req)

  // Allow public routes
  if (isPublicRoute(req)) {
    return intlResponse
  }

  // Handle dashboard routes
  if (isDashboardRoute(req)) {
    if (!userId) {
      const signInUrl = new URL('/auth/sign-in', req.url)
      signInUrl.searchParams.set('redirect_url', pathname)
      return NextResponse.redirect(signInUrl)
    }
    return intlResponse
  }

  // Handle business-scoped routes
  if (isBusinessRoute(req)) {
    if (!userId) {
      const signInUrl = new URL('/auth/sign-in', req.url)
      signInUrl.searchParams.set('redirect_url', pathname)
      return NextResponse.redirect(signInUrl)
    }
    // Business access validation will be handled in the layout component
    return intlResponse
  }

  // Redirect authenticated users from root to dashboard
  if ((pathname === '/' || pathname.match(/^\/(en|fr|es|de|ie|ng|ee)\/?$/)) && userId) {
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }

  // Redirect unauthenticated users to sign-in
  if (!userId) {
    const signInUrl = new URL('/auth/sign-in', req.url)
    signInUrl.searchParams.set('redirect_url', pathname)
    return NextResponse.redirect(signInUrl)
  }

  return intlResponse
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}