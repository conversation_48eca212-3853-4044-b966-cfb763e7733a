import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
import createIntlMiddleware from 'next-intl/middleware'
import { routing } from './i18n/routing'

// Create the internationalization middleware
const intlMiddleware = createIntlMiddleware(routing)

// Define public routes that don't require authentication
const isPublicRoute = createRouteMatcher([
  '/',
  '/(en|fr|es|de|ie|ng|ee)',
  '/(en|fr|es|de|ie|ng|ee)/auth/sign-in(.*)',
  '/(en|fr|es|de|ie|ng|ee)/auth/sign-up(.*)',
  '/auth/sign-in(.*)',
  '/auth/sign-up(.*)',
  '/api/health',
  '/api/webhooks(.*)',
])

// Define API routes that need authentication
const isApiRoute = createRouteMatcher(['/api(.*)'])

// Define dashboard routes that need authentication (includes all business features)
const isDashboardRoute = createRouteMatcher([
  '/dashboard(.*)',
  '/onboard(.*)',
  '/businesses(.*)',
  '/store(.*)',
  '/orders(.*)',
  '/inventory(.*)',
  '/crm(.*)',
  '/finance(.*)',
  '/compliance(.*)',
  '/documents(.*)',
  '/secure-vault(.*)',
  '/planner(.*)',
  '/hr(.*)',
  '/roles(.*)',
  '/marketing(.*)',
  '/analytics(.*)',
  '/integrations(.*)',
  '/business-groups(.*)',
  '/logistics(.*)',
  '/settings(.*)',
  '/select-business(.*)',
  '/(en|fr|es|de|ie|ng|ee)/dashboard(.*)',
  '/(en|fr|es|de|ie|ng|ee)/onboard(.*)',
  '/(en|fr|es|de|ie|ng|ee)/businesses(.*)',
  '/(en|fr|es|de|ie|ng|ee)/store(.*)',
  '/(en|fr|es|de|ie|ng|ee)/orders(.*)',
  '/(en|fr|es|de|ie|ng|ee)/inventory(.*)',
  '/(en|fr|es|de|ie|ng|ee)/crm(.*)',
  '/(en|fr|es|de|ie|ng|ee)/finance(.*)',
  '/(en|fr|es|de|ie|ng|ee)/compliance(.*)',
  '/(en|fr|es|de|ie|ng|ee)/documents(.*)',
  '/(en|fr|es|de|ie|ng|ee)/secure-vault(.*)',
  '/(en|fr|es|de|ie|ng|ee)/planner(.*)',
  '/(en|fr|es|de|ie|ng|ee)/hr(.*)',
  '/(en|fr|es|de|ie|ng|ee)/roles(.*)',
  '/(en|fr|es|de|ie|ng|ee)/marketing(.*)',
  '/(en|fr|es|de|ie|ng|ee)/analytics(.*)',
  '/(en|fr|es|de|ie|ng|ee)/integrations(.*)',
  '/(en|fr|es|de|ie|ng|ee)/business-groups(.*)',
  '/(en|fr|es|de|ie|ng|ee)/logistics(.*)',
  '/(en|fr|es|de|ie|ng|ee)/settings(.*)',
  '/(en|fr|es|de|ie|ng|ee)/select-business(.*)',
])

// Business feature routes are now part of dashboard routes (session-scoped)

export default clerkMiddleware(async (auth, req) => {
  const { userId, sessionClaims } = await auth()
  const { pathname } = req.nextUrl

  // Handle API routes first (no i18n needed)
  if (isApiRoute(req)) {
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Inject business context headers for API routes
    const response = NextResponse.next()
    if (sessionClaims?.metadata) {
      const metadata = sessionClaims.metadata as any
      if (metadata.activeBusinessId) {
        response.headers.set('x-business-id', metadata.activeBusinessId)
      }
      if (metadata.activeBusinessGroupId) {
        response.headers.set('x-business-group-id', metadata.activeBusinessGroupId)
      }
    }
    return response
  }

  // Apply internationalization middleware for non-API routes
  const intlResponse = intlMiddleware(req)

  // Inject business context headers for authenticated routes
  if (userId && sessionClaims?.metadata) {
    const metadata = sessionClaims.metadata as any
    if (metadata.activeBusinessId) {
      intlResponse.headers.set('x-business-id', metadata.activeBusinessId)
    }
    if (metadata.activeBusinessGroupId) {
      intlResponse.headers.set('x-business-group-id', metadata.activeBusinessGroupId)
    }
  }

  // Allow public routes
  if (isPublicRoute(req)) {
    return intlResponse
  }

  // Handle dashboard routes
  if (isDashboardRoute(req)) {
    if (!userId) {
      const signInUrl = new URL('/auth/sign-in', req.url)
      signInUrl.searchParams.set('redirect_url', pathname)
      return NextResponse.redirect(signInUrl)
    }
    return intlResponse
  }

  // Business feature routes are now handled as dashboard routes with session context

  // Redirect authenticated users from root to dashboard
  if ((pathname === '/' || pathname.match(/^\/(en|fr|es|de|ie|ng|ee)\/?$/)) && userId) {
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }

  // Redirect unauthenticated users to sign-in
  if (!userId) {
    const signInUrl = new URL('/auth/sign-in', req.url)
    signInUrl.searchParams.set('redirect_url', pathname)
    return NextResponse.redirect(signInUrl)
  }

  return intlResponse
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}