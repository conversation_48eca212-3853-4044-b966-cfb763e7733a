{"name": "venturedirection", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest"}, "dependencies": {"@clerk/nextjs": "^6.20.2", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.8.2", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.8", "@tanstack/react-query": "^5.79.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "mongodb": "^6.16.0", "next": "15.3.3", "next-intl": "4.1.0", "next-themes": "^0.4.6", "prisma": "^6.8.2", "react": "19", "react-day-picker": "^9.7.0", "react-dom": "19", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.2", "react-router-dom": "^7.6.1", "recharts": "^2.15.3", "sonner": "^2.0.4", "svix": "^1.67.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.42"}, "devDependencies": {"@eslint/js": "^9.28.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query-devtools": "^5.80.7", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/mongodb": "^4.0.7", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.3", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "postcss": "^8", "tailwindcss": "^4.1.8", "ts-node": "^10.9.2", "typescript": "5.7.3", "typescript-eslint": "^8.33.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}