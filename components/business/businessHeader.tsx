'use client'

import { UserButton } from '@clerk/nextjs'
import { useBusinessUser } from '@/lib/contexts/business-user-context'
import { BusinessSwitcher } from './businessSwitcher'
import { LanguageSwitcherCompact } from '@/components/languageSwitcher'
import { Badge } from '@/components/common/badge'
import { Button } from '@/components/common/button'
import { 
  Building2, 
  Crown, 
  Shield, 
  Users, 
  Bell,
  Search,
  Menu
} from 'lucide-react'
import { useState } from 'react'

export function BusinessHeader() {
  const { 
    businessName, 
    userRole, 
    isOwner, 
    isAdmin, 
    canManageUsers,
    permissions 
  } = useBusinessUser()
  
  const [searchOpen, setSearchOpen] = useState(false)

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm">
      <div className="flex items-center justify-between h-16 px-4 lg:px-6">
        {/* Left side */}
        <div className="flex items-center gap-4">
          {/* Mobile menu button */}
          <Button variant="ghost" size="sm" className="lg:hidden">
            <Menu className="h-5 w-5" />
          </Button>

          {/* Business info */}
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 rounded bg-venture-500 flex items-center justify-center">
              <Building2 className="h-4 w-4 text-white" />
            </div>
            <div className="hidden sm:block">
              <h1 className="font-semibold text-gray-900 truncate max-w-48">
                {businessName}
              </h1>
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-500">{userRole}</span>
                {isOwner && (
                  <Badge variant="warning" className="text-xs">
                    <Crown className="h-3 w-3 mr-1" />
                    Owner
                  </Badge>
                )}
                {isAdmin && !isOwner && (
                  <Badge variant="info" className="text-xs">
                    <Shield className="h-3 w-3 mr-1" />
                    Admin
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Center - Search */}
        <div className="hidden md:flex flex-1 max-w-md mx-8">
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search business data..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              onFocus={() => setSearchOpen(true)}
              onBlur={() => setSearchOpen(false)}
            />
          </div>
        </div>

        {/* Right side */}
        <div className="flex items-center gap-3">
          {/* Quick actions */}
          {canManageUsers && (
            <Button variant="ghost" size="sm" className="hidden lg:flex">
              <Users className="h-4 w-4 mr-2" />
              Manage Users
            </Button>
          )}
          
          {/* Notifications */}
          <Button variant="ghost" size="sm" className="relative">
            <Bell className="h-4 w-4" />
            <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">
              3
            </span>
          </Button>
          
          {/* Business switcher */}
          <BusinessSwitcher />
          
          {/* Language switcher */}
          <LanguageSwitcherCompact />
          
          {/* User menu */}
          <UserButton 
            appearance={{
              elements: {
                avatarBox: "h-8 w-8"
              }
            }}
          />
        </div>
      </div>

      {/* Mobile business info */}
      <div className="sm:hidden px-4 py-2 border-t border-gray-100 bg-gray-50">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="font-medium text-gray-900 truncate">
              {businessName}
            </h1>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500">{userRole}</span>
              {isOwner && (
                <Badge variant="warning" className="text-xs">
                  Owner
                </Badge>
              )}
              {isAdmin && !isOwner && (
                <Badge variant="info" className="text-xs">
                  Admin
                </Badge>
              )}
            </div>
          </div>
          
          {/* Mobile search button */}
          <Button variant="ghost" size="sm">
            <Search className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Search overlay for mobile */}
      {searchOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-white border-b border-gray-200 p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search business data..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              autoFocus
            />
          </div>
        </div>
      )}
    </header>
  )
}
