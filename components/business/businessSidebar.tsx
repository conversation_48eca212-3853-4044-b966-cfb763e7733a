'use client'

import { useBusinessUser } from '@/lib/contexts/business-user-context'
import { useTranslations } from 'next-intl'
import { Link } from '@/i18n/navigation'
import { usePathname } from 'next/navigation'
import { Badge } from '@/components/common/badge'
import { BUSINESS_PERMISSIONS } from '@/lib/types/business-user-context'
import { 
  LayoutDashboard,
  Store,
  ShoppingCart,
  Package,
  Users,
  DollarSign,
  FileCheck,
  FileText,
  Shield,
  Calendar,
  UserCheck,
  Megaphone,
  BarChart3,
  Zap,
  Building2,
  Truck,
  Settings,
  Lock,
  CheckSquare,
  Network,
  Rocket,
  ArrowLeft
} from 'lucide-react'

const iconMap = {
  LayoutDashboard,
  Store,
  ShoppingCart,
  Package,
  Users,
  DollarSign,
  FileCheck,
  FileText,
  Shield,
  Calendar,
  UserCheck,
  Megaphone,
  BarChart3,
  Zap,
  Building2,
  Truck,
  Settings,
  Lock,
  CheckSquare,
  Network,
  Rocket,
  ArrowLeft
}

interface NavigationItem {
  name: string
  href: string
  icon: keyof typeof iconMap
  permission?: string
  adminOnly?: boolean
  ownerOnly?: boolean
  badge?: string
}

export function BusinessSidebar() {
  const t = useTranslations('navigation')
  const pathname = usePathname()
  const { 
    hasPermission, 
    isOwner, 
    isAdmin, 
    businessName,
    userRole 
  } = useBusinessUser()

  const navigation: NavigationItem[] = [
    {
      name: 'Business Dashboard',
      href: '/business',
      icon: 'LayoutDashboard'
    },
    {
      name: t('onboard'),
      href: '/business/onboard',
      icon: 'Rocket'
    },
    {
      name: t('store'),
      href: '/business/store',
      icon: 'Store',
      permission: BUSINESS_PERMISSIONS.ORDERS_VIEW
    },
    {
      name: t('orders'),
      href: '/business/orders',
      icon: 'ShoppingCart',
      permission: BUSINESS_PERMISSIONS.ORDERS_VIEW
    },
    {
      name: t('inventory'),
      href: '/business/inventory',
      icon: 'Package',
      permission: BUSINESS_PERMISSIONS.INVENTORY_VIEW
    },
    {
      name: t('crm'),
      href: '/business/crm',
      icon: 'Users',
      permission: BUSINESS_PERMISSIONS.CRM_VIEW
    },
    {
      name: t('finance'),
      href: '/business/finance',
      icon: 'DollarSign',
      permission: BUSINESS_PERMISSIONS.FINANCE_VIEW
    },
    {
      name: t('compliance'),
      href: '/business/compliance',
      icon: 'FileCheck',
      permission: BUSINESS_PERMISSIONS.COMPLIANCE_VIEW
    },
    {
      name: t('documents'),
      href: '/business/documents',
      icon: 'FileText',
      permission: BUSINESS_PERMISSIONS.DOCUMENTS_VIEW
    },
    {
      name: t('secureVault'),
      href: '/business/secure-vault',
      icon: 'Lock',
      adminOnly: true
    },
    {
      name: t('planner'),
      href: '/business/planner',
      icon: 'CheckSquare'
    },
    {
      name: t('hr'),
      href: '/business/hr',
      icon: 'UserCheck',
      permission: BUSINESS_PERMISSIONS.USERS_VIEW
    },
    {
      name: t('roles'),
      href: '/business/roles',
      icon: 'Shield',
      permission: BUSINESS_PERMISSIONS.USERS_MANAGE,
      adminOnly: true
    },
    {
      name: t('marketing'),
      href: '/business/marketing',
      icon: 'Megaphone'
    },
    {
      name: t('analytics'),
      href: '/business/analytics',
      icon: 'BarChart3',
      permission: BUSINESS_PERMISSIONS.ANALYTICS_VIEW
    },
    {
      name: t('integrations'),
      href: '/business/integrations',
      icon: 'Zap',
      adminOnly: true
    },
    {
      name: t('businessGroups'),
      href: '/business-group',
      icon: 'Network',
      ownerOnly: true
    },
    {
      name: t('logistics'),
      href: '/business/logistics',
      icon: 'Truck'
    },
    {
      name: t('settings'),
      href: '/business/settings',
      icon: 'Settings',
      permission: BUSINESS_PERMISSIONS.SETTINGS_VIEW
    },
  ]

  // Filter navigation based on permissions
  const filteredNavigation = navigation.filter(item => {
    // Owner can see everything
    if (isOwner) return true
    
    // Check owner-only items
    if (item.ownerOnly) return false
    
    // Check admin-only items
    if (item.adminOnly && !isAdmin) return false
    
    // Check specific permissions
    if (item.permission && !hasPermission(item.permission)) return false
    
    return true
  })

  return (
    <div className="fixed left-0 top-16 bottom-0 w-64 bg-white border-r border-gray-200 overflow-y-auto">
      {/* Business info header */}
      <div className="p-4 border-b border-gray-200">
        <Link href="/dashboard" className="flex items-center gap-2 text-sm text-gray-500 hover:text-gray-700 mb-3">
          <ArrowLeft className="h-4 w-4" />
          Back to Dashboard
        </Link>
        
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded bg-venture-500 flex items-center justify-center">
            <Building2 className="h-5 w-5 text-white" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="font-semibold text-gray-900 truncate">{businessName}</p>
            <p className="text-sm text-gray-500">{userRole}</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-3 py-4">
        {filteredNavigation.map((item) => {
          const IconComponent = iconMap[item.icon]
          const isActive = pathname === item.href
          
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`group flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                isActive
                  ? 'bg-venture-500 text-white shadow-sm'
                  : 'text-gray-600 hover:bg-venture-50 hover:text-venture-700'
              }`}
            >
              <div className="flex items-center">
                <IconComponent className="mr-3 h-5 w-5 flex-shrink-0" />
                {item.name}
              </div>
              
              {item.badge && (
                <Badge variant="secondary" className="text-xs">
                  {item.badge}
                </Badge>
              )}
              
              {item.ownerOnly && (
                <Badge variant="warning" className="text-xs">
                  Owner
                </Badge>
              )}
              
              {item.adminOnly && !item.ownerOnly && (
                <Badge variant="info" className="text-xs">
                  Admin
                </Badge>
              )}
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="text-xs text-gray-500">
          <p>Business Workspace</p>
          <p className="mt-1">
            {filteredNavigation.length} features available
          </p>
        </div>
      </div>
    </div>
  )
}
