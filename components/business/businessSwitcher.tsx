'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { useBusiness } from '@/lib/contexts/business-context'
import { useLastBusiness } from '@/lib/hooks/useLastBusiness'
import { Button } from '@/components/common/button'
import {
  Building2,
  ChevronDown,
  Check,
  Users,
  Loader2,
  AlertCircle,
  Clock
} from 'lucide-react'

export function BusinessSwitcher() {
  const t = useTranslations('business.switcher')
  const {
    currentBusiness,
    currentBusinessGroup,
    businesses,
    businessGroups,
    switchBusiness,
    switchBusinessGroup,
    isLoading,
    error
  } = useBusiness()

  const { lastBusinessId } = useLastBusiness()
  const [isOpen, setIsOpen] = useState(false)
  const [switching, setSwitching] = useState(false)

  const handleBusinessSwitch = async (businessId: string, businessGroupId?: string) => {
    try {
      setSwitching(true)
      await switchBusiness(businessId, businessGroupId)
      setIsOpen(false)
    } catch (err) {
      console.error('Failed to switch business:', err)
    } finally {
      setSwitching(false)
    }
  }

  const handleBusinessGroupSwitch = async (businessGroupId: string) => {
    try {
      setSwitching(true)
      await switchBusinessGroup(businessGroupId)
      setIsOpen(false)
    } catch (err) {
      console.error('Failed to switch business group:', err)
    } finally {
      setSwitching(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 text-sm text-gray-500">
        <Loader2 className="h-4 w-4 animate-spin" />
        Loading...
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 text-sm text-red-600">
        <AlertCircle className="h-4 w-4" />
        Error loading businesses
      </div>
    )
  }

  if (!currentBusiness) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 text-sm text-gray-500">
        <Building2 className="h-4 w-4" />
        No business selected
      </div>
    )
  }

  return (
    <div className="relative">
      <Button
        variant="ghost"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full justify-between px-3 py-2 h-auto"
        disabled={switching}
      >
        <div className="flex items-center gap-3 min-w-0">
          <div className="h-8 w-8 rounded bg-venture-500 flex items-center justify-center flex-shrink-0">
            <Building2 className="h-4 w-4 text-white" />
          </div>
          <div className="text-left min-w-0">
            <p className="font-medium text-sm truncate">{currentBusiness.name}</p>
            {currentBusinessGroup && (
              <p className="text-xs text-muted-foreground truncate">
                {currentBusinessGroup.name}
              </p>
            )}
          </div>
        </div>
        {switching ? (
          <Loader2 className="h-4 w-4 animate-spin flex-shrink-0" />
        ) : (
          <ChevronDown className="h-4 w-4 flex-shrink-0" />
        )}
      </Button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border rounded-md shadow-lg z-50 max-h-96 overflow-y-auto">
          {/* Business Groups */}
          {businessGroups.length > 0 && (
            <div className="p-2">
              <div className="text-xs font-medium text-gray-500 px-2 py-1 mb-1">
                Business Groups
              </div>
              {businessGroups.map((group) => (
                <button
                  key={group.id}
                  onClick={() => handleBusinessGroupSwitch(group.id)}
                  className="w-full flex items-center gap-3 px-2 py-2 text-sm hover:bg-gray-50 rounded"
                  disabled={switching}
                >
                  <Users className="h-4 w-4 text-gray-400" />
                  <div className="flex-1 text-left">
                    <p className="font-medium">{group.name}</p>
                    <p className="text-xs text-gray-500">
                      {group.businessCount} businesses
                    </p>
                  </div>
                  {currentBusinessGroup?.id === group.id && (
                    <Check className="h-4 w-4 text-venture-600" />
                  )}
                </button>
              ))}
            </div>
          )}

          {/* Businesses */}
          <div className="p-2">
            {businessGroups.length > 0 && (
              <div className="text-xs font-medium text-gray-500 px-2 py-1 mb-1 border-t pt-3">
                Businesses
              </div>
            )}
            {businesses.map((business) => {
              const isLastAccessed = lastBusinessId === business.id && currentBusiness?.id !== business.id

              return (
                <button
                  key={business.id}
                  onClick={() => handleBusinessSwitch(business.id, business.businessGroupId)}
                  className="w-full flex items-center gap-3 px-2 py-2 text-sm hover:bg-gray-50 rounded"
                  disabled={switching}
                >
                  <div className="h-6 w-6 rounded bg-venture-500 flex items-center justify-center">
                    <Building2 className="h-3 w-3 text-white" />
                  </div>
                  <div className="flex-1 text-left">
                    <div className="flex items-center gap-2">
                      <p className="font-medium">{business.name}</p>
                      {isLastAccessed && (
                        <Clock className="h-3 w-3 text-venture-500" title="Last accessed" />
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <span>{business.roleTitle}</span>
                      {business.businessGroupName && (
                        <>
                          <span>•</span>
                          <span>{business.businessGroupName}</span>
                        </>
                      )}
                      {isLastAccessed && (
                        <>
                          <span>•</span>
                          <span className="text-venture-500">Last used</span>
                        </>
                      )}
                    </div>
                  </div>
                  {currentBusiness?.id === business.id && (
                    <Check className="h-4 w-4 text-venture-600" />
                  )}
                </button>
              )
            })}
          </div>

          {businesses.length === 0 && (
            <div className="p-4 text-center text-sm text-gray-500">
              No businesses available
            </div>
          )}
        </div>
      )}

      {/* Overlay to close dropdown */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}
