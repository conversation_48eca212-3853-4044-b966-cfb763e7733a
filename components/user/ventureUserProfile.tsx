'use client'

import { useVentureUser } from '@/lib/hooks/useVentureUser'
import { Badge } from '@/components/common/badge'
import { Button } from '@/components/common/button'
import { 
  User, 
  Mail, 
  Calendar, 
  Shield, 
  Building2, 
  Crown,
  Settings,
  RefreshCw
} from 'lucide-react'

interface VentureUserProfileProps {
  showBusinessContext?: boolean
  showPermissions?: boolean
  showActions?: boolean
}

export function VentureUserProfile({ 
  showBusinessContext = true,
  showPermissions = true,
  showActions = true
}: VentureUserProfileProps) {
  const { user, isLoading, error, refetch } = useVentureUser()

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <div className="animate-pulse">
          <div className="flex items-center space-x-4">
            <div className="rounded-full bg-gray-200 h-16 w-16"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-32"></div>
              <div className="h-3 bg-gray-200 rounded w-48"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <div className="text-center text-red-600">
          <p>Error loading user profile: {error}</p>
          <Button onClick={refetch} className="mt-2" size="sm">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <div className="text-center text-gray-500">
          <User className="h-12 w-12 mx-auto mb-2" />
          <p>User profile not found</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg border shadow-sm">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="relative">
              {user.avatar ? (
                <img
                  src={user.avatar}
                  alt={user.displayName}
                  className="h-16 w-16 rounded-full object-cover"
                />
              ) : (
                <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
                  <User className="h-8 w-8 text-gray-400" />
                </div>
              )}
              {user.isBusinessOwner && (
                <Crown className="absolute -top-1 -right-1 h-5 w-5 text-yellow-500" />
              )}
            </div>
            
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{user.displayName}</h2>
              <div className="flex items-center text-gray-600 mt-1">
                <Mail className="h-4 w-4 mr-1" />
                <span className="text-sm">{user.email}</span>
                {user.emailVerified && (
                  <Badge variant="success" className="ml-2">Verified</Badge>
                )}
              </div>
              <div className="flex items-center text-gray-500 mt-1">
                <Calendar className="h-4 w-4 mr-1" />
                <span className="text-xs">
                  Joined {new Date(user.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
          
          {showActions && (
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={refetch}>
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-1" />
                Settings
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Business Context */}
      {showBusinessContext && user.currentBusinessId && (
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
            <Building2 className="h-5 w-5 mr-2" />
            Current Business Context
          </h3>
          
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-blue-900">
                  {user.businessMemberships.find(m => m.businessId === user.currentBusinessId)?.businessName}
                </p>
                <p className="text-sm text-blue-700">
                  Role: {user.businessMemberships.find(m => m.businessId === user.currentBusinessId)?.userRole}
                </p>
              </div>
              <div className="flex gap-1">
                {user.isBusinessOwner && <Badge variant="warning">Owner</Badge>}
                {user.isBusinessAdmin && <Badge variant="info">Admin</Badge>}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Business Memberships */}
      {showBusinessContext && user.businessMemberships.length > 0 && (
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Business Memberships</h3>
          <div className="space-y-2">
            {user.businessMemberships.map((membership) => (
              <div
                key={membership.businessId}
                className={`flex items-center justify-between p-3 rounded-lg border ${
                  membership.businessId === user.currentBusinessId
                    ? 'bg-blue-50 border-blue-200'
                    : 'bg-gray-50 border-gray-200'
                }`}
              >
                <div>
                  <p className="font-medium text-gray-900">{membership.businessName}</p>
                  <p className="text-sm text-gray-600">{membership.userRole}</p>
                  {membership.businessGroupName && (
                    <p className="text-xs text-gray-500">Group: {membership.businessGroupName}</p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {membership.businessId === user.currentBusinessId && (
                    <Badge variant="info">Active</Badge>
                  )}
                  <Badge variant={membership.isActive ? 'success' : 'secondary'}>
                    {membership.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Permissions & Roles */}
      {showPermissions && (user.currentRoles.length > 0 || user.globalRoles.length > 0) && (
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
            <Shield className="h-5 w-5 mr-2" />
            Roles & Permissions
          </h3>
          
          {/* Current Business Roles */}
          {user.currentRoles.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Current Business Roles</h4>
              <div className="flex flex-wrap gap-2">
                {user.currentRoles.map((role) => (
                  <Badge key={role.id} variant="info" className="text-xs">
                    {role.title}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Global Roles */}
          {user.globalRoles.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Global Roles</h4>
              <div className="flex flex-wrap gap-2">
                {user.globalRoles.map((role) => (
                  <Badge key={role.id} variant="warning" className="text-xs">
                    {role.title}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Current Permissions */}
          {user.currentPermissions.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Current Permissions</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                {user.currentPermissions.slice(0, 8).map((permission) => (
                  <div key={permission.id} className="flex items-center text-gray-600">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                    {permission.name}
                  </div>
                ))}
                {user.currentPermissions.length > 8 && (
                  <div className="text-gray-500 italic">
                    +{user.currentPermissions.length - 8} more...
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
