'use client'

import { useTranslations } from 'next-intl'

interface InventoryOverviewProps {
  businessId: string
}

export function InventoryOverview({ businessId }: InventoryOverviewProps) {
  const t = useTranslations('business.inventory')
  
  return (
    <div className="space-y-6">
      {/* Inventory Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('totalItems')}</h3>
          <div className="text-2xl font-bold">1,234</div>
          <p className="text-xs text-muted-foreground">Across 45 categories</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('lowStockItems')}</h3>
          <div className="text-2xl font-bold text-red-600">23</div>
          <p className="text-xs text-muted-foreground">Needs restocking</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('totalValue')}</h3>
          <div className="text-2xl font-bold">$125,430</div>
          <p className="text-xs text-muted-foreground">Current inventory value</p>
        </div>
        
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h3 className="tracking-tight text-sm font-medium">{t('turnoverRate')}</h3>
          <div className="text-2xl font-bold">4.2x</div>
          <p className="text-xs text-muted-foreground">Annual turnover</p>
        </div>
      </div>

      {/* Low Stock Alerts */}
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <div className="p-6">
          <h3 className="text-lg font-medium mb-4">{t('lowStockAlerts')}</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
              <div>
                <p className="font-medium">Coffee Mug - White</p>
                <p className="text-sm text-muted-foreground">SKU: MUG-001</p>
              </div>
              <div className="text-right">
                <p className="font-medium text-red-600">3 left</p>
                <p className="text-sm text-muted-foreground">Min: 10</p>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 border border-yellow-200 rounded-lg bg-yellow-50">
              <div>
                <p className="font-medium">Wireless Headphones</p>
                <p className="text-sm text-muted-foreground">SKU: HEAD-002</p>
              </div>
              <div className="text-right">
                <p className="font-medium text-yellow-600">8 left</p>
                <p className="text-sm text-muted-foreground">Min: 15</p>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
              <div>
                <p className="font-medium">USB Cable - Type C</p>
                <p className="text-sm text-muted-foreground">SKU: USB-003</p>
              </div>
              <div className="text-right">
                <p className="font-medium text-red-600">1 left</p>
                <p className="text-sm text-muted-foreground">Min: 25</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
