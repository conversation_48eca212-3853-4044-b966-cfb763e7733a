'use client'

import { useTranslations } from 'next-intl'
import { Link } from '@/i18n/navigation'
import { Button } from '@/components/common/button'
import { Building2, ArrowRight, Plus } from 'lucide-react'

interface Business {
  id: string
  name: string
  type: string
  logo?: string
}

interface BusinessSelectorProps {
  businesses: Business[]
  onBusinessSelect?: (businessId: string, businessGroupId?: string) => Promise<void>
}

export function BusinessSelector({ businesses, onBusinessSelect }: BusinessSelectorProps) {
  const t = useTranslations('businesses')

  const handleBusinessClick = async (business: Business) => {
    if (onBusinessSelect) {
      await onBusinessSelect(business.id)
    }
  }
  
  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {t('selectBusiness')}
        </h2>
        <p className="text-gray-600">
          {t('selectBusinessDescription')}
        </p>
      </div>

      {businesses.length === 0 ? (
        <div className="text-center py-12">
          <Building2 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t('noBusinessesFound')}
          </h3>
          <p className="text-gray-600 mb-6">
            Get started by creating your first business
          </p>
          <Link href="/businesses/create">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('createBusiness')}
            </Button>
          </Link>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {businesses.map((business) => {
            const BusinessCard = (
              <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6 hover:shadow-md transition-shadow">
                <div className="flex items-center gap-4 mb-4">
                  {business.logo ? (
                    <img
                      src={business.logo}
                      alt={business.name}
                      className="h-12 w-12 rounded-lg object-cover"
                    />
                  ) : (
                    <div className="h-12 w-12 rounded-lg bg-primary flex items-center justify-center">
                      <Building2 className="h-6 w-6 text-primary-foreground" />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900 truncate">
                      {business.name}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {business.type}
                    </p>
                  </div>
                  <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
                </div>

                <div className="text-sm text-gray-600">
                  Click to access business workspace
                </div>
              </div>
            )

            return onBusinessSelect ? (
              <button
                key={business.id}
                onClick={() => handleBusinessClick(business)}
                className="group block w-full text-left"
              >
                {BusinessCard}
              </button>
            ) : (
              <Link
                key={business.id}
                href={`/${business.id}/dashboard`}
                className="group block"
              >
                {BusinessCard}
              </Link>
            )
          })}
        </div>
      )}

      {businesses.length > 0 && (
        <div className="text-center">
          <Link href="/businesses">
            <Button variant="outline">
              <Building2 className="h-4 w-4 mr-2" />
              {t('manageBusinesses')}
            </Button>
          </Link>
        </div>
      )}
    </div>
  )
}
