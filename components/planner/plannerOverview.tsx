'use client'

import { useTranslations } from 'next-intl'

interface PlannerOverviewProps {
  businessId: string
}

export function PlannerOverview({ businessId }: PlannerOverviewProps) {
  const t = useTranslations('business.planner')
  
  return (
    <div className="space-y-6">
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h3 className="text-lg font-medium mb-4">{t('overview')}</h3>
        <p className="text-muted-foreground">
          Task management and project planning for your business operations.
        </p>
        
        <div className="mt-6 grid gap-4 md:grid-cols-4">
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Active Tasks</h4>
            <p className="text-2xl font-bold">45</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Completed</h4>
            <p className="text-2xl font-bold">128</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Projects</h4>
            <p className="text-2xl font-bold">8</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Team Members</h4>
            <p className="text-2xl font-bold">12</p>
          </div>
        </div>
      </div>
    </div>
  )
}
