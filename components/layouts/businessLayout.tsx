'use client'

import { UserButton } from '@clerk/nextjs'
import { useTranslations } from 'next-intl'
import { Link } from '@/i18n/navigation'
import { LanguageSwitcherCompact } from '@/components/languageSwitcher'
import { Button } from '@/components/common/button'
import { useState } from 'react'
import { 
  LayoutDashboard, 
  Store,
  ShoppingCart,
  Package,
  Users,
  DollarSign,
  FileCheck,
  FileText,
  Shield,
  Calendar,
  UserCheck,
  Megaphone,
  BarChart3,
  Zap,
  Building2,
  Truck,
  Settings,
  User,
  Menu,
  X,
  ArrowLeft
} from 'lucide-react'

interface BusinessLayoutProps {
  children: React.ReactNode
  business: {
    id: string
    name: string
    logo?: string
  }
  locale: string
}

export function BusinessLayout({ children, business, locale }: BusinessLayoutProps) {
  const t = useTranslations('business.navigation')
  const [sidebarOpen, setSidebarOpen] = useState(false)
  
  const navigation = [
    { name: t('dashboard'), href: `/${business.id}/dashboard`, icon: LayoutDashboard },
    { name: t('store'), href: `/${business.id}/store`, icon: Store },
    { name: t('orders'), href: `/${business.id}/orders`, icon: ShoppingCart },
    { name: t('inventory'), href: `/${business.id}/inventory`, icon: Package },
    { name: t('crm'), href: `/${business.id}/crm`, icon: Users },
    { name: t('finance'), href: `/${business.id}/finance`, icon: DollarSign },
    { name: t('compliance'), href: `/${business.id}/compliance`, icon: FileCheck },
    { name: t('documents'), href: `/${business.id}/documents`, icon: FileText },
    { name: t('secureVault'), href: `/${business.id}/secure-vault`, icon: Shield },
    { name: t('planner'), href: `/${business.id}/planner`, icon: Calendar },
    { name: t('hr'), href: `/${business.id}/hr`, icon: UserCheck },
    { name: t('roles'), href: `/${business.id}/roles`, icon: Shield },
    { name: t('marketing'), href: `/${business.id}/marketing`, icon: Megaphone },
    { name: t('analytics'), href: `/${business.id}/analytics`, icon: BarChart3 },
    { name: t('integrations'), href: `/${business.id}/integrations`, icon: Zap },
    { name: t('businessGroups'), href: `/${business.id}/business-groups`, icon: Building2 },
    { name: t('logistics'), href: `/${business.id}/logistics`, icon: Truck },
    { name: t('settings'), href: `/${business.id}/settings`, icon: Settings },
    { name: t('profile'), href: `/${business.id}/profile`, icon: User },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white">
          <div className="flex h-16 items-center justify-between px-4">
            <div className="flex items-center gap-3">
              {business.logo ? (
                <img src={business.logo} alt={business.name} className="h-8 w-8 rounded" />
              ) : (
                <div className="h-8 w-8 rounded bg-venture-500 flex items-center justify-center">
                  <Building2 className="h-4 w-4 text-venture-600-foreground" />
                </div>
              )}
              <span className="font-semibold text-gray-900 truncate">{business.name}</span>
            </div>
            <Button variant="ghost" size="sm" onClick={() => setSidebarOpen(false)}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                onClick={() => setSidebarOpen(false)}
              >
                <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white border-r border-gray-200">
          <div className="flex h-16 items-center justify-between px-4 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <Link href="/dashboard" className="flex items-center gap-2 text-sm text-gray-500 hover:text-gray-700">
                <ArrowLeft className="h-4 w-4" />
                {t('backToDashboard')}
              </Link>
            </div>
          </div>
          
          <div className="flex items-center gap-3 px-4 py-3 border-b border-gray-200">
            {business.logo ? (
              <img src={business.logo} alt={business.name} className="h-10 w-10 rounded" />
            ) : (
              <div className="h-10 w-10 rounded bg-venture-500 flex items-center justify-center">
                <Building2 className="h-5 w-5 text-venture-600-foreground" />
              </div>
            )}
            <div className="flex-1 min-w-0">
              <p className="font-semibold text-gray-900 truncate">{business.name}</p>
              <p className="text-sm text-gray-500">{t('businessWorkspace')}</p>
            </div>
          </div>

          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              >
                <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 items-center justify-between bg-white px-4 shadow-sm border-b border-gray-200 lg:px-6">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <Menu className="h-5 w-5" />
            </Button>
            <div className="lg:hidden">
              <span className="font-semibold text-gray-900">{business.name}</span>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <LanguageSwitcherCompact />
            <UserButton />
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 p-4 lg:p-6">
          {children}
        </main>
      </div>
    </div>
  )
}
