'use client'

import { ReactNode } from 'react'
import { BusinessUserProvider } from '@/lib/contexts/business-user-context'
import { BusinessHeader } from '@/components/business/businessHeader'
import { BusinessSidebar } from '@/components/business/businessSidebar'
import { useBusiness } from '@/lib/contexts/business-context'
import { AlertCircle, Building2 } from 'lucide-react'
import { Button } from '@/components/common/button'
import { Link } from '@/i18n/navigation'

interface BusinessWorkspaceLayoutProps {
  children: ReactNode
  businessId?: string
  showSidebar?: boolean
  showHeader?: boolean
}

export function BusinessWorkspaceLayout({ 
  children, 
  businessId,
  showSidebar = true,
  showHeader = true
}: BusinessWorkspaceLayoutProps) {
  const { currentBusiness } = useBusiness()
  
  // Use provided businessId or current business
  const targetBusinessId = businessId || currentBusiness?.id

  if (!targetBusinessId) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md">
          <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            No Business Selected
          </h2>
          <p className="text-gray-600 mb-6">
            Please select a business to access this workspace.
          </p>
          <Link href="/dashboard">
            <Button>
              Select Business
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <BusinessUserProvider 
      businessId={targetBusinessId}
      loadingFallback={<BusinessLoadingFallback />}
      errorFallback={<BusinessErrorFallback businessId={targetBusinessId} />}
      fallback={<BusinessAccessDeniedFallback businessId={targetBusinessId} />}
    >
      <div className="min-h-screen bg-gray-50">
        {showHeader && <BusinessHeader />}
        
        <div className="flex">
          {showSidebar && <BusinessSidebar />}
          
          <main className={`flex-1 ${showSidebar ? 'ml-64' : ''} ${showHeader ? 'pt-16' : ''}`}>
            <div className="p-6">
              {children}
            </div>
          </main>
        </div>
      </div>
    </BusinessUserProvider>
  )
}

function BusinessLoadingFallback() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <h2 className="text-lg font-medium text-gray-900 mb-2">
          Loading Business Workspace
        </h2>
        <p className="text-gray-600">
          Setting up your business context...
        </p>
      </div>
    </div>
  )
}

function BusinessErrorFallback({ businessId }: { businessId: string }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center max-w-md">
        <AlertCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Failed to Load Business
        </h2>
        <p className="text-gray-600 mb-6">
          There was an error loading the business workspace. This might be a temporary issue.
        </p>
        <div className="flex gap-3 justify-center">
          <Button 
            variant="outline" 
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
          <Link href="/dashboard">
            <Button>
              Back to Dashboard
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}

function BusinessAccessDeniedFallback({ businessId }: { businessId: string }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center max-w-md">
        <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Access Denied
        </h2>
        <p className="text-gray-600 mb-6">
          You don't have permission to access this business workspace. 
          Contact the business owner to request access.
        </p>
        <div className="flex gap-3 justify-center">
          <Button variant="outline">
            Request Access
          </Button>
          <Link href="/dashboard">
            <Button>
              Back to Dashboard
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
