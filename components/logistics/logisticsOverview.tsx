'use client'

import { useTranslations } from 'next-intl'

interface LogisticsOverviewProps {
  businessId: string
}

export function LogisticsOverview({ businessId }: LogisticsOverviewProps) {
  const t = useTranslations('business.logistics')
  
  return (
    <div className="space-y-6">
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h3 className="text-lg font-medium mb-4">{t('overview')}</h3>
        <div className="grid gap-4 md:grid-cols-4">
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Active Shipments</h4>
            <p className="text-2xl font-bold">23</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Delivered Today</h4>
            <p className="text-2xl font-bold">45</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Carriers</h4>
            <p className="text-2xl font-bold">8</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Avg Delivery</h4>
            <p className="text-2xl font-bold">2.3 days</p>
          </div>
        </div>
      </div>
    </div>
  )
}
