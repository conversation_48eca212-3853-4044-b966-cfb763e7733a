'use client'

import { useTranslations } from 'next-intl'

interface HrOverviewProps {
  businessId: string
}

export function HrOverview({ businessId }: HrOverviewProps) {
  const t = useTranslations('business.hr')
  
  return (
    <div className="space-y-6">
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h3 className="text-lg font-medium mb-4">{t('overview')}</h3>
        <div className="grid gap-4 md:grid-cols-4">
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Total Employees</h4>
            <p className="text-2xl font-bold">24</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">On Leave</h4>
            <p className="text-2xl font-bold">3</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Pending Reviews</h4>
            <p className="text-2xl font-bold">8</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Open Positions</h4>
            <p className="text-2xl font-bold">5</p>
          </div>
        </div>
      </div>
    </div>
  )
}
