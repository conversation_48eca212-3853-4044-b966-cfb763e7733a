import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-venture-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "text-white shadow-sm hover:shadow-md" + " " + "bg-[#3b82f6] hover:bg-[#2563eb] active:bg-[#1d4ed8]",
        destructive: "text-white shadow-sm hover:shadow-md" + " " + "bg-[#ef4444] hover:bg-[#dc2626] active:bg-[#b91c1c]",
        outline: "border bg-white shadow-sm" + " " + "border-[#bfdbfe] text-[#2563eb] hover:bg-[#eff6ff] active:bg-[#dbeafe]",
        secondary: "bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300 shadow-sm",
        ghost: "text-venture-600 hover:bg-venture-50 active:bg-venture-100",
        link: "text-venture-600 underline-offset-4 hover:underline hover:text-venture-700",
        success: "text-white shadow-sm hover:shadow-md" + " " + "bg-[#22c55e] hover:bg-[#16a34a] active:bg-[#15803d]",
        warning: "text-white shadow-sm hover:shadow-md" + " " + "bg-[#f59e0b] hover:bg-[#d97706] active:bg-[#b45309]",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-12 rounded-lg px-6 text-base",
        icon: "h-10 w-10",
        xs: "h-6 rounded px-2 text-xs",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
