'use client'

import { cn } from '@/lib/utils'
import { cva, type VariantProps } from 'class-variance-authority'

const badgeVariants = cva(
  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-venture-500 focus:ring-offset-2',
  {
    variants: {
      variant: {
        default: 'hover:bg-venture-200' + ' ' + 'bg-[#dbeafe] text-[#1e40af]',
        secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
        success: 'bg-[#dcfce7] text-[#166534] hover:bg-[#bbf7d0]',
        warning: 'bg-[#fef3c7] text-[#92400e] hover:bg-[#fde68a]',
        error: 'bg-[#fee2e2] text-[#991b1b] hover:bg-[#fecaca]',
        info: 'bg-[#dbeafe] text-[#1e40af] hover:bg-[#bfdbfe]',
        outline: 'border border-[#bfdbfe] text-[#2563eb] hover:bg-[#eff6ff]',
        solid: 'bg-[#3b82f6] text-white hover:bg-[#2563eb]',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

export function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}
