'use client'

import { useTranslations } from 'next-intl'

interface SecureVaultOverviewProps {
  businessId: string
}

export function SecureVaultOverview({ businessId }: SecureVaultOverviewProps) {
  const t = useTranslations('business.secureVault')
  
  return (
    <div className="space-y-6">
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h3 className="text-lg font-medium mb-4">{t('overview')}</h3>
        <p className="text-muted-foreground">
          Secure vault for storing sensitive business credentials, passwords, and certificates.
        </p>
        
        <div className="mt-6 grid gap-4 md:grid-cols-3">
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Stored Credentials</h4>
            <p className="text-2xl font-bold">24</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Certificates</h4>
            <p className="text-2xl font-bold">8</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">API Keys</h4>
            <p className="text-2xl font-bold">12</p>
          </div>
        </div>
      </div>
    </div>
  )
}
