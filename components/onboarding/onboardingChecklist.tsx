'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { useOnboarding } from '@/lib/hooks/useOnboarding'
import { Link } from '@/i18n/navigation'
import { Button } from '@/components/common/button'
import { Progress } from '@/components/common/progress'
import { 
  CheckCircle, 
  Circle, 
  X, 
  ChevronRight,
  Store,
  ShoppingCart,
  DollarSign,
  Users,
  FileCheck,
  FileText,
  Settings,
  Loader2,
  Trophy
} from 'lucide-react'

const iconMap = {
  Store,
  ShoppingCart,
  DollarSign,
  Users,
  FileCheck,
  FileText,
  Settings,
}

interface OnboardingChecklistProps {
  onDismiss?: () => void
  compact?: boolean
}

export function OnboardingChecklist({ onDismiss, compact = false }: OnboardingChecklistProps) {
  const t = useTranslations('onboarding')
  const { onboardingStatus, isLoading, updateProgress } = useOnboarding()
  const [dismissed, setDismissed] = useState(false)

  if (dismissed || !onboardingStatus || onboardingStatus.isFullyOnboarded) {
    return null
  }

  const handleDismiss = () => {
    setDismissed(true)
    onDismiss?.()
  }

  const handleStepClick = async (moduleId: string, completed: boolean) => {
    if (completed) return // Don't allow unchecking completed items
    
    try {
      await updateProgress(moduleId as any, true)
    } catch (error) {
      console.error('Failed to update progress:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-600">Loading onboarding status...</span>
        </div>
      </div>
    )
  }

  if (compact) {
    return (
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-venture-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 bg-venture-500 rounded-full flex items-center justify-center">
              <Trophy className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{t('quickSetup')}</h3>
              <p className="text-sm text-gray-600">
                {onboardingStatus.completedSteps} of {onboardingStatus.totalSteps} completed
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Progress value={onboardingStatus.completionPercentage} className="w-20" />
            <span className="text-sm font-medium text-gray-700">
              {onboardingStatus.completionPercentage}%
            </span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg border shadow-sm">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
              <Trophy className="h-5 w-5 text-yellow-500" />
              {t('title')}
            </h2>
            <p className="text-gray-600 mt-1">{t('description')}</p>
          </div>
          <Button variant="ghost" size="sm" onClick={handleDismiss}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="mt-4">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>
              {onboardingStatus.completedSteps} of {onboardingStatus.totalSteps} completed
            </span>
            <span>{onboardingStatus.completionPercentage}% complete</span>
          </div>
          <Progress value={onboardingStatus.completionPercentage} className="h-2" />
        </div>
      </div>

      <div className="p-6">
        <div className="space-y-3">
          {onboardingStatus.steps.map((step) => {
            const IconComponent = iconMap[step.icon as keyof typeof iconMap] || Circle
            
            return (
              <div
                key={step.module}
                className={`flex items-center gap-4 p-3 rounded-lg border transition-colors ${
                  step.completed 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                }`}
              >
                <button
                  onClick={() => handleStepClick(step.module, step.completed)}
                  className="flex-shrink-0"
                  disabled={step.completed}
                >
                  {step.completed ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <Circle className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  )}
                </button>
                
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className={`p-2 rounded-lg ${
                    step.completed ? 'bg-green-100' : 'bg-white'
                  }`}>
                    <IconComponent className={`h-4 w-4 ${
                      step.completed ? 'text-green-600' : 'text-gray-600'
                    }`} />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h3 className={`font-medium ${
                      step.completed ? 'text-green-900' : 'text-gray-900'
                    }`}>
                      {step.title}
                    </h3>
                    <p className={`text-sm ${
                      step.completed ? 'text-green-700' : 'text-gray-600'
                    }`}>
                      {step.description}
                    </p>
                    {step.completed && step.completedAt && (
                      <p className="text-xs text-green-600 mt-1">
                        Completed {new Date(step.completedAt).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                </div>
                
                {!step.completed && (
                  <Link href={step.actionUrl}>
                    <Button variant="ghost" size="sm" className="flex-shrink-0">
                      <span className="sr-only">Go to {step.title}</span>
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </Link>
                )}
              </div>
            )
          })}
        </div>

        {onboardingStatus.isFullyOnboarded && (
          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-3">
              <Trophy className="h-6 w-6 text-green-600" />
              <div>
                <h3 className="font-medium text-green-900">{t('congratulations')}</h3>
                <p className="text-sm text-green-700">{t('allComplete')}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
