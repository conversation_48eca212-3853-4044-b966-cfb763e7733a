'use client'

import { useTranslations } from 'next-intl'

interface RolesOverviewProps {
  businessId: string
}

export function RolesOverview({ businessId }: RolesOverviewProps) {
  const t = useTranslations('business.roles')
  
  return (
    <div className="space-y-6">
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h3 className="text-lg font-medium mb-4">{t('overview')}</h3>
        <div className="grid gap-4 md:grid-cols-3">
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Total Roles</h4>
            <p className="text-2xl font-bold">8</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Active Users</h4>
            <p className="text-2xl font-bold">24</p>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium">Permissions</h4>
            <p className="text-2xl font-bold">45</p>
          </div>
        </div>
      </div>
    </div>
  )
}
