'use client'

import { useTranslations } from 'next-intl'

interface UserProfileProps {
  businessId: string
}

export function UserProfile({ businessId }: UserProfileProps) {
  const t = useTranslations('business.profile')
  
  return (
    <div className="space-y-6">
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h3 className="text-lg font-medium mb-4">{t('personalInfo')}</h3>
        <div className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="block text-sm font-medium mb-2">First Name</label>
              <input type="text" className="w-full p-2 border rounded-md" placeholder="Enter first name" />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Last Name</label>
              <input type="text" className="w-full p-2 border rounded-md" placeholder="Enter last name" />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Email</label>
            <input type="email" className="w-full p-2 border rounded-md" placeholder="Enter email" />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Phone</label>
            <input type="tel" className="w-full p-2 border rounded-md" placeholder="Enter phone number" />
          </div>
        </div>
      </div>
    </div>
  )
}
