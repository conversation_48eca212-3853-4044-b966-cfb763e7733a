This is schema will used as reference to peform crud with native mongodb driver for timeseries log, search and analytics.

model BusinessRegistry {
  id                  String   @id @default(uuid()) @map("_id")
  countryId           String
  name                String
  socialMediaIds      String[]
  registrationNumber  String   @unique
  status              String
  businessAddressIds  String[]
  lastUpdated         DateTime
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  businessIndexes     AllBusinessIndex[]
  @@index([name])
  @@index([registrationNumber])
  @@map("business_registries")
}

model SocialMedia {
  id              String   @id @default(uuid()) @map("_id")
  name            String
  url             String?
  handle          String?
  externalId      String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  @@map("social_media")
}

model BusinessAddresses {
  id              String   @id @default(uuid()) @map("_id")
  houseNumber     String?
  street          String
  city            String
  state           String?
  country         String
  postcode        Int?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  @@map("business_addresses")
}

model AllBusinessIndex {
  id                      String               @id @default(uuid()) @map("_id")
  name                    String
  businessRegistryId      String?
  nonBusinessRegistryId   String?
  businessRegistry        BusinessRegistry?    @relation(fields: [businessRegistryId], references: [id])
  nonBusinessRegistry     NonBusinessRegistry? @relation(fields: [nonBusinessRegistryId], references: [id])
  @@map("all_business_indexes")
}

model NonBusinessRegistry {
  id                  String           @id @default(uuid()) @map("_id")
  name                String
  socialMediaIds      String[]
  source              String
  businessAddressIds  String[]
  createdAt           DateTime         @default(now())
  updatedAt           DateTime         @updatedAt
  businessIndexes     AllBusinessIndex[]
  @@map("non_business_registries")
}

model Log {
  id              String   @id @default(uuid()) @map("_id")
  userId          String
  businessId      String
  action          String
  details         String
  timestamp       DateTime @default(now())
  @@map("logs")
}

model IntegrationLog {
  id              String   @id @default(uuid()) @map("_id")
  integrationId   String
  event           String
  details         Json
  timestamp       DateTime @default(now())
  @@index([integrationId])
  @@index([timestamp])
  @@map("integration_logs")
}
